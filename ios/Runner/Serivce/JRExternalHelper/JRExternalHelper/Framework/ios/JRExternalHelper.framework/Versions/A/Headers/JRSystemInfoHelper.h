//
//  JRSystemInfoHelper.h
//  joyRunner
//
//  Created by Blade on 09/05/2018.
//  Copyright © 2018 Joyrun. All rights reserved.
//

#ifndef JRSystemInfoHelper_h
#define JRSystemInfoHelper_h

#import <dlfcn.h>
#import <sys/types.h>  

typedef int (*ptrace_ptr_t)(int _request, pid_t _pid, caddr_t _addr, int _data);
#if !defined(PT_DENY_ATTACH)
    #define PT_DENY_ATTACH 31
#endif  // !defined(PT_DENY_ATTACH)

#if !defined(DEBUG)
    #define DEFENSE_DEBUG_ATTATCH
#else 
    #define DEFENSE_DEBUG_ATTATCH   char ptraceStr[6] = {'p', 't', 'r', 'a', 'c', 'e'};                 \
                                    void* handle = dlopen(0, RTLD_GLOBAL | RTLD_NOW);                   \
                                    ptrace_ptr_t ptrace_ptr = dlsym(handle, (const char *)ptraceStr);   \
                                    ptrace_ptr(PT_DENY_ATTACH, 0, 0, 0);                                \
                                    dlclose(handle);
#endif


#define SYSTEMINFO_RING_MAX_COUNT 100
#define SYSTEMINFO_RING_MAX_LENGTH 100


typedef struct JREHDD_Util {
#if !defined(JREXTERNALHELPER_PRODUCTION) || !JREXTERNALHELPER_PRODUCTION
    uint8_t (*identifyRingData)(int8_t ringData);
    void (*chainRings)(uint8_t buffer[SYSTEMINFO_RING_MAX_COUNT][SYSTEMINFO_RING_MAX_LENGTH], bool *isSucess);
#endif
    void (*chainScission)(bool *isScissored);
    bool (*amIFeeding)(void);
} JREHDD_Util_Type ;

extern JREHDD_Util_Type JREHDDUtil;

#if !defined(JREXTERNALHELPER_PRODUCTION) || !JREXTERNALHELPER_PRODUCTION
extern const uint8_t originalRingsData[SYSTEMINFO_RING_MAX_COUNT][SYSTEMINFO_RING_MAX_LENGTH];
extern const char originalRings[SYSTEMINFO_RING_MAX_COUNT][SYSTEMINFO_RING_MAX_LENGTH];
#endif
#endif /* JRSystemInfoHelper_h */
