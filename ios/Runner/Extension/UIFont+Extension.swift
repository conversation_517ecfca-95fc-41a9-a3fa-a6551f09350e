//
//  UIFont+Extension.swift
//  Calendar
//
//  Created by Fidetro on 25/12/2017.
//  Copyright © 2017 Fidetro. All rights reserved.
//

import UIKit

extension UIFont {
    static func pingFangSCRegularFont(ofSize: CGFloat) -> UIFont {
        guard let font = UIFont.init(name: "PingFangSC-Regular", size: ofSize) else {
            return UIFont.systemFont(ofSize:ofSize)
        }
        return font
    }
    
    static func helveticaFont(ofSize: CGFloat) -> UIFont {
        guard let font = UIFont.init(name: "Helve<PERSON>", size: ofSize) else {
            return UIFont.systemFont(ofSize:ofSize)
        }
        return font
    }
    
    static func robotoMediumFont(ofSize: CGFloat) -> UIFont {
        guard let font = UIFont.init(name: "Roboto-Medium", size: ofSize) else {
            return UIFont.systemFont(ofSize:ofSize)
        }
        return font
    }
    
    static func bebasNeueBoldFont(ofSize: CGFloat) -> UIFont {
        guard let font = UIFont.init(name: "BebasNeueBold", size: ofSize) else {
            return UIFont.systemFont(ofSize:ofSize)
        }
        return font
    }
    
    static func bebasNeueRegularFont(ofSize: CGFloat) -> UIFont {
        guard let font = UIFont.init(name: "BebasNeue-Regular", size: ofSize) else {
            return UIFont.systemFont(ofSize:ofSize)
        }
        return font
    }
    
    static func robotoBoldFont(ofSize: CGFloat) -> UIFont {
        guard let font = UIFont.init(name: "Roboto-Bold", size: ofSize) else {
            return UIFont.systemFont(ofSize:ofSize)
        }
        return font
    }
    
    static func robotoRegularFont(ofSize: CGFloat) -> UIFont {
        guard let font = UIFont.init(name: "Roboto-Regular", size: ofSize) else {
            return UIFont.systemFont(ofSize:ofSize)
        }
        return font
    }
    
    static func robotoBlackFont(ofSize: CGFloat) -> UIFont {
        guard let font = UIFont.init(name: "Roboto-Black", size: ofSize) else {
            return UIFont.systemFont(ofSize:ofSize)
        }
        return font
    }
    
    
}
