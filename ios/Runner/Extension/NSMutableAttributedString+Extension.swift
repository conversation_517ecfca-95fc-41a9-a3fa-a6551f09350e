import UIKit
extension NSMutableAttributedString {
    static func attribute(strings:[String],attributes:[[Any]]) -> NSMutableAttributedString {
        let muAttrStr = NSMutableAttributedString()
        for (index,str) in strings.enumerated() {
            let attrStr = NSMutableAttributedString.init(string: str)
            for atrribute in attributes[index] {
                switch atrribute {
                case is UIColor :
                    attrStr.addAttribute(.foregroundColor, value: atrribute, range: NSRange.init(location: 0, length: str.count))
                    
                case is UIFont :
                    attrStr.addAttribute(.font, value: atrribute, range: NSRange.init(location: 0, length: str.count))
                    
                case is [NSAttributedString.Key:Any] :
                    attrStr.addAttributes(atrribute as! [NSAttributedString.Key : Any], range: NSRange.init(location: 0, length: str.count))
                    
                default:
                    break
                }
            }
            muAttrStr.append(attrStr)
        }
        return muAttrStr
    }
}

