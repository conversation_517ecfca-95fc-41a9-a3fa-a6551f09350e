//
//  LocationService.swift
//  Runner
//
//  Created by wmh on 2021/6/30.
//

import UIKit

struct LocationService {
    
    static let mapboxStyle = "mapbox://styles/iminapp/ckn5hc2ig0k3g17o54x6l3fuf"
    static let mapboxLocalStyle = Bundle.main.url(forResource: "mapbox_style", withExtension: "json")!
    
    static func checkAuthorizationStatus() {
        let status = CLLocationManager.authorizationStatus()
        if status == .denied {
            self.showAlert(title: "EDGE-SPORTS需要定位服务才能记录骑行轨迹")
            return
        } else {
            if #available(iOS 14.0, *) {
                let accuracyAuthorization = CLLocationManager().accuracyAuthorization
                if accuracyAuthorization == .reducedAccuracy {
                    showAlert(title: "打开“精准定位”来允许“【一直骑行】”确定您的位置")
                }
            }
        }
    }
    
    static func showAlert(title: String) {
        let alertController = UIAlertController(title: title, message: nil, preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: "取消", style: .cancel, handler: nil)
        let okAction = UIAlertAction(title: "去设置", style: .default, handler: {
            action in
            guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
                return
            }
            
            if UIApplication.shared.canOpenURL(settingsUrl) {
                UIApplication.shared.open(settingsUrl, completionHandler: { (success) in
                    print("Settings opened: \(success)") // Prints true
                })
            }
        })
        alertController.addAction(cancelAction)
        alertController.addAction(okAction)
        UIApplication.shared.keyWindow?.rootViewController?.present(alertController, animated: true, completion: nil)
    }

}
