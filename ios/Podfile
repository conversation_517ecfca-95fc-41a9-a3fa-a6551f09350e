source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'
# Uncomment this line to define a global platform for your project
platform :ios, '15.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

def flutter_install_ios_plugin_pods(ios_application_path = nil)
  # defined_in_file is set by CocoaPods and is a Pathname to the Podfile.
  ios_application_path ||= File.dirname(defined_in_file.realpath) if self.respond_to?(:defined_in_file)
  raise 'Could not find iOS application path' unless ios_application_path

  # Prepare symlinks folder. We use symlinks to avoid having Podfile.lock
  # referring to absolute paths on developers' machines.

  symlink_dir = File.expand_path('.symlinks', ios_application_path)
  system('rm', '-rf', symlink_dir) # Avoid the complication of dependencies like FileUtils.

  symlink_plugins_dir = File.expand_path('plugins', symlink_dir)
  system('mkdir', '-p', symlink_plugins_dir)

  plugins_file = File.join(ios_application_path, '..', '.flutter-plugins-dependencies')
  plugin_pods = flutter_parse_plugins_file(plugins_file)
  plugin_pods.each do |plugin_hash|
    plugin_name = plugin_hash['name']
    plugin_path = plugin_hash['path']
    if (plugin_name && plugin_path)
      symlink = File.join(symlink_plugins_dir, plugin_name)
      File.symlink(plugin_path, symlink)

      if plugin_name == 'flutter_ffmpeg'
          pod 'flutter_ffmpeg/full-gpl-lts', :path => File.join('.symlinks', 'plugins', plugin_name, 'ios')
      else
          pod plugin_name, :path => File.join('.symlinks', 'plugins', plugin_name, 'ios')
      end
    end
  end
end

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  
  pod 'YYModel', '1.0.4'
  pod 'UFileSDK'
  pod 'SVProgressHUD'
  
  #source '*************:adingStorage/ios-specs.git'
  source '*************:kexuejin/mobile-client-ios-edge-specs.git'
  pod 'CRideRecord', '0.1.1'
  pod 'JRExternalHelper', '0.1.9'
  
  # 高德地图
  #pod 'AMap3DMap', '8.0.0'  # AMapNavi中包含了此项所以要改用AMapNavi
  pod 'AMapNavi'
  pod 'AMapSearch'
  pod 'AMapFoundation', '1.6.8'

  target 'RunnerTests' do
    inherit! :search_paths
  end

  # Share Extension is name of Extension which you created which is in this case 'Share Extension'
  target 'Share Extension' do
     inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
    config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
            '$(inherited)',
 
            ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
            'PERMISSION_LOCATION=1',
            
            ## dart: PermissionGroup.camera
            'PERMISSION_CAMERA=1',
           
            ## dart: PermissionGroup.photos
            'PERMISSION_PHOTOS=1',
          ]
      end
    if target.name == "Pods-MYPROJECT"
               puts "Updating #{target.name} OTHER_LDFLAGS"
               target.build_configurations.each do |config|
                   xcconfig_path = config.base_configuration_reference.real_path

                   # read from xcconfig to build_settings dictionary
                   build_settings = Hash[*File.read(xcconfig_path).lines.map{|x| x.split(/\s*=\s*/, 2)}.flatten]

                   # modify OTHER_LDFLAGS
                   vlc_flag = ' -framework "MobileVLCKit"'
                   build_settings['OTHER_LDFLAGS'].gsub!(vlc_flag, "")
                   build_settings['OTHER_LDFLAGS'].gsub!("\n", "")
                   build_settings['OTHER_LDFLAGS'] += vlc_flag + "\n"

                   # write build_settings dictionary to xcconfig
                   File.open(xcconfig_path, "w") do |file|
                     build_settings.each do |key,value|
                       file.write(key + " = " + value)
                     end
                   end
               end
           end
  end
end

