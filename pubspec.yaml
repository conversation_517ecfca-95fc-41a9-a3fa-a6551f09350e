name: new_edge
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.2+38

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  # Flutter Localization 是一个用于使用地图数据进行应用内本地化的包。实施起来更容易、更快捷。
  # 该包的灵感来自 Flutter SDK flutter_localizations 本身
#  flutter_localization: ^0.1.12 #注释多语言的支持，因为引用矛盾
  #    sdk: flutter没用过

  #  intl_utils: ^2.8.3 国际化语言包，暂时注释掉先不做国际化

#  flutter_l10n: ^1.1.2

  get: ^4.6.6
  get_storage: ^2.1.1

  flutter_easyloading: ^3.0.5

  #  网络请求框架
  dio: ^5.4.3+1
  # retrofi网络框架，不知道是不是在和dio共同使用，
  retrofit: ^4.1.0
  # logger是retrofit要求引用的
  logger: ^2.3.0

    #  http_parser: ^4.0.2 http解析器，鬼知道有没有用，又用dio又用retrofit的，猜测http_parser是多余的
  flutter_swiper_view: ^1.1.8

  webview_flutter: ^4.7.0

  #简单数据持久存储
  shared_preferences: ^2.2.3

  # 创建分享链接用的，已经弃用了，迁移到share_plus版本https://pub-web.flutter-io.cn/packages/share
  #  share: ^0.6.5+4
  share_plus: ^10.0.2

  # json序列化反序列化，搜索发现代码里面没用过，先注释起来
  #  json_annotation: ^4.8.1

  #各种可重新排序（也称为拖放）的 Flutter 小部件，包括可重新排序的表格、行、列、环绕和条形列表
  reorderables: ^0.6.0

  #强大的官方 Image 扩展组件, 支持加载以及失败显示，缓存网络图片，缩放拖拽图片，图片浏览(微信掘金效果)
  #滑动退出页面(微信掘金效果)，编辑图片(裁剪旋转翻转)，保存，绘制自定义效果等功能
  extended_image: ^9.0.4

  # 多媒体资源管理器
  photo_manager: ^3.2.3

  # 对 InheritedWidget 组件的上层封装，使其更易用，更易复用。
  provider: ^6.1.2

  # 适用于 iOS、Android 和 Web 的 Flutter 插件，用于在 Widget 表面上播放视频
  video_player: ^2.9.2

#  flutter_ffmpeg: ^0.4.2 #已经废弃了，处理音视频裁剪用的，再用的话得找替代框架 https://pub.dev/packages/flutter_ffmpeg

  # 简单的格式化日期插件
  date_format: ^2.0.7

  # 高德Flutter定位插件
  # amap_location_flutter_plugin:
  #     git:
  #       url: https://github.com/amap-demo/amap-location-flutter.git

  #用于显示来自互联网的图像并将它们保存在缓存目录中
  cached_network_image: ^3.4.1
  #
  #  # 可缩放图像/内容小部件的小部件，一年内没有维护
  photo_view: ^0.15.0

  # 修改状态栏颜色的插件，已经不维护了，不兼容dart3
  #  flutter_statusbarcolor: ^0.2.3

  # 文件路径提供
  path_provider: ^2.1.3

  # 图片裁剪
  image_cropper: ^9.1.0

  # 图片压缩插件
  flutter_image_compress: ^2.3.0

  # 创建二维码图像
  qr_flutter: ^4.1.0
  qr_code_dart_scan: ^0.10.0

  # 相册照片选择器，这玩意儿里面应该已经包括有image_picker了，尝试着不引入image_picker看看
  image_gallery_saver_plus: ^4.0.1
  image_picker: ^1.1.2 #有importimage_picker的地方有报错，所以打开注释

  # 网络状态 已经弃用，使用https://plus.fluttercommunity.dev/替代，有需要再引入
  #  connectivity: ^0.4.8+1
  connectivity_plus: ^6.0.3

  # 刷新加载
  pull_to_refresh: ^2.0.0

  # 打电话 发邮件 发短信
  url_launcher: ^6.2.6

  # 简单、快速地生成RFC4122 UUID
  uuid: ^4.4.0

  # SQLite
  sqflite: ^2.3.3+1

  # bloc状态管
  flutter_bloc: ^9.1.0

  # 简化相等比较
  equatable: ^2.0.5

  # 版本信息 已经过时 用到的话使用 https://plus.fluttercommunity.dev/ 进行替代
  #  package_info: ^0.4.0+14
  package_info_plus: ^8.0.0

  # 系统信息
  #  device_info: ^0.4.1+5
  device_info_plus: ^11.3.3

  # 权限管理
  permission_handler: ^12.0.0+1

  # 给app评分
  rate_my_app: ^2.1.0

  #  smooth_star_rating: 1.0.4+2 滑动评价组件，不支持dart3

  # 日期
  day: ^0.8.0

  # 一个 CacheManager，用于下载文件并将其缓存到应用程序的缓存目录中。可以更改有关保留文件多长时间的各种设置。
  flutter_cache_manager: ^3.3.2

  # 用来展示不同样式的验证码，简单好用！支持所有flutter支持的平台
  pin_input_text_field: ^4.5.2

  #  apple_sign_in: ^0.1.0 苹果登录的，已过时，替换为下面这个
  sign_in_with_apple: ^7.0.1

  # 简单的toast库
  oktoast: ^3.4.0

  # 防止并发访问异步代码的基本锁定机制
  synchronized: ^3.1.0

  # 使用 Dart Streams来解耦应用程序的简单事件总线 。
  event_bus: ^2.0.0
  rxdart: ^0.28.0

  # 图表库
  fl_chart: ^0.70.2
  #文本播放
  flutter_tts: ^4.0.2

  #这个插件不支持空安全，没办法用。允许您保持设备屏幕清醒的插件，即防止屏幕休眠,这玩意儿跟好几个包冲突，暂时先注释
  wakelock_plus: ^1.2.5

  # 可滑动列表项的 Flutter 实现，具有可以取消的定向滑动操作
  flutter_slidable: ^4.0.0

  # 高质量的 Flutter 预建动画
  animations: ^2.0.11

  #  flutter_gifimage: ^1.0.1 已过时，不支持dart3

  # 键盘显示隐藏
  flutter_keyboard_visibility: ^6.0.0
  # 汉字转拼音
  lpinyin: ^2.0.3

  # 实现的粘性标头，带有一个子条
  flutter_sticky_header: ^0.7.0

#  umeng_sdk:
#    path: ./umeng_sdk/ #已经不再维护了，需要重新找、
  umeng_push_sdk: ^2.3.0

  # 流式列表布局 0.6.2
  flutter_staggered_grid_view: ^0.7.0

  # 腾讯bugly
#  flutter_bugly: ^0.4.4

  # html
  flutter_html: ^3.0.0-beta.2

  copy_with_extension: ^6.0.1

  # 图片选择器
  wechat_assets_picker: ^9.1.0

  #支付
  wechat_kit: ^6.0.1
  wechat_kit_extension: ^1.0.1
  alipay_kit: ^6.0.0
  alipay_kit_ios: ^6.0.1

  timelines_plus: ^1.0.6
  selectable_autolink_text: ^2.6.0

  # 视频播放器
  chewie: ^1.8.1
  # 视频预览
  video_thumbnail: ^0.5.3

  ota_update: ^7.0.1

  emoji_picker_flutter: ^4.3.0

  geolocator: ^13.0.1

  flutter_contacts: ^1.1.9+2
  json_annotation: ^4.9.0
  receive_sharing_intent: ^1.8.1

dependency_overrides:
  umeng_common_sdk:
    path: third/umeng_common_sdk
  umeng_push_sdk:
    path: third/umeng_push_sdk

wechat_kit:
  #  ios: no_pay # 默认 pay
  app_id: wx9fa9facdbb1b6334
  universal_link: https://www.edgecycling.cn/apple-app-site-association

alipay_kit:
  scheme: alipay2021004141629070

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  #  # 以下三个是retrofit要求引用的
  retrofit_generator: ^9.1.9
  analyzer: 7.3.0
  build_runner: ^2.4.10
  json_serializable: ^6.8.0

  copy_with_extension_gen: ^6.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/files/
    - assets/mock/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
  fonts:
    - family: DIN
      fonts:
        - asset: assets/fonts/DIN-Condensed-Bold.ttf

    - family: PF
      fonts:
        - asset: assets/fonts/PingFang-SC.ttf

    - family: BN
      fonts:
        - asset: assets/fonts/BebasNeue.otf