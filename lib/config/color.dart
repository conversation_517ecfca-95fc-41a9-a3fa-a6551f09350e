import 'dart:ui';

import 'package:flutter/material.dart';

class DColor {


  DColor._();

  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color transparent = Color(0x00000000);
  static const Color backgroundColor = Color(0xFFFFFFFF);
  static const Color hitText = Color(0xFFAAB2BA);

  static const Color hitbg = Color(0xFFFFFFFF);
  // static const Color color_ff189659 = Color(0xFF189659);
  static const Color color_ff0964a0 = Color(0xFF0964A0);
  static const Color color_ffb63d22 = Color(0xFFB63D22);
  static const Color color_ff2d2f3e = Color(0xFF2D2F3E);

  // static const Color color_ff747b81 = Color(0xFF747B81);
  // static const Color ff808080 = Color(0xFFF3F5F7);
  // static const Color color_ff171824 = Color(0xFFFFFFFF);
  static const Color color_ff8d99b2 = Color(0xFF8D99B2);
  static const Color color_ff6e7faa = Color(0xFF6E7FAA);

  // static const Color color_ff1f202d = Color(0xFF1F202D);
  static const Color color_ff2b293b = Color(0xFF28293B);
  // static const Color color_ff12121d = Color(0xFF12121D);
  static const Color color_ff2e2f3e = Color(0xFF2E2F3E);
  // static const Color color_ff12131f = Color(0xFF12131F);
  // static const Color color_ff148743 = Color(0xFF148743);
  static const Color color_ff148743 = fffb691d;
  static const Color color_ff1a1a1a = Color(0xFF1A1A1A);

  // static const Color color_ff666666 = Color(0xFF666666);
  // static const Color color_ff090e14 = Color(0xFF090e14);
  static const Color color_ff383535 = Color(0xFF383535);
  static const Color color_4d383535 = Color(0x4D383535);
  static const Color color_ff313131 = Color(0xFF313131);
  static const Color color_4c343131 = Color(0x4C343131);
  static const Color color_ffdddddd = Color(0xFFDDDDDD);
  static const Color color_ffe0e0e0 = Color(0xFFE0E0E0);
  static const Color color_ffe64340 = Color(0xFFE64340);
  // static const Color color_ff343545 = Color(0xFF343545);
  static const Color color_ffFAFAFA = Color(0xFFFAFAFA);
  // static const Color color_ff17513c = Color(0xFF17513C);
  static const Color color_ff17513c = f80fb691d;
  static const Color color_ff808087 = Color(0xFF808087);
  static const Color color_ff808080 = Color(0xFF808080);
  static const Color color_ff333333 = Color(0xFF333333);
  static const Color color_ff999999 = Color(0xFF999999);
  static const Color color_ff252633 = Color(0xFF252633);
  static const Color color_ff2f3045 = Color(0xFF2f3045);
  static const Color color_ff281c23 = Color(0xFF281c23);
  static const Color color_ff172427 = Color(0xFF172427);
  static const Color color_ff383a53 = Color(0xFF383A53);
  static const Color color_ff1b1c2c = Color(0xFF1B1C2C);
  static const Color color_ff2f2d37 = Color(0xFF2F2D37);
  static const Color color_ffe3e3e3 = Color(0xFFE3E3E3);
  static const Color color_ff42445c = Color(0xFF42445C);
  static const Color color_ff0f1017 = Color(0xFFFFFFFF);



  static const Color f80fb691d = Color(0x80FB691D);
  static const Color a6fb691d = Color(0xA6FB691D);
  static const Color ffF6F6F6 = Color(0xFFF6F6F6);
  static const Color ffa4a4a4 = Color(0xFFA4A4A4);
  static const Color ff242424 = Color(0xff242424);

  static const Color f1a808080 = Color(0X1a808080);
  static const Color ff222332 = Color(0Xff222332);
  static const Color ff666666 = Color(0Xff666666);

  static const Color fffbfbfb = Color(0Xfffbfbfb);
  static const Color ffaab2ba = Color(0Xffaab2ba);
  static const Color ff797979 = Color(0Xff797979);
  static const Color ff3ecc49 = Color(0Xff3ecc49);
  static const Color fffef1e8 = Color(0Xfffef1e8);
  static const Color ff4e4e4e = Color(0Xff4e4e4e);
  static const Color ff3ebcff = Color(0Xd93ebcff);
  static const Color ccb9b9b9 = Color(0Xd9b9b9b9);
  static const Color ccff6c6c = Color(0Xd9ff6c6c);
  static const Color cc776cff = Color(0Xd9776cff);
  static const Color cc676767 = Color(0Xd9676767);
  static const Color ccffb06c = Color(0Xd9ffb06c);
  static const Color ffd5d5d5 = Color(0Xffd5d5d5);
  static const Color ffd9d9d9 = Color(0xffd9d9d9);
  static const Color ff6b6b6b = Color(0xff6b6b6b);
  static const Color fff0f0f0 = Color(0xfff0f0f0);
  static const Color ff0D74B7 = Color(0xff0D74B7);

  static const Color color_fff07409 = Color(0xFFF07409);

  static const Color fffb691d = Color(0xFFFB691D);
  static const Color a0ffb691d = Color(0x0FFB691D);
  static const Color ff232323 = Color(0Xff232323);
  static const Color ff808080 = Color(0Xff808080);
  static const Color fff2f2f2 = Color(0Xfff2f2f2);
  static const Color ffe9e9e9 = Color(0Xffe9e9e9);
  static const Color ffeaeaea = Color(0Xffeaeaea);
  static const Color fff7f7f7 = Color(0Xfff7f7f7);
  static const Color fff10000 = Color(0Xfff10000);
  static const Color ff707070 = Color(0Xff707070);

  static const Color primary = fffb691d;
}
