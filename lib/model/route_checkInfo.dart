import 'package:new_edge/model/route_rank_info.dart';
import 'package:json_annotation/json_annotation.dart';


part 'route_checkInfo.g.dart';
//路段总排行
@JsonSerializable()
class RouteCheckInfo {
  int? id;//匹配ID
  int? routeId;//路段ID
  int? uid;//用户ID
  int? postRideId;//骑行ID
  int? rideCheckinTime;//打卡次数
  int? routeSecond;//用时
  int? checkinTime;//打卡时间
  int? ranking;//排名
  String? transcend;//超越
  double? averageVelocity;//匀速 km/h

  RouteCheckInfo(
      {
        this.id,
        this.routeId,
        this.uid,
        this.postRideId,
        this.rideCheckinTime,
        this.routeSecond,
        this.checkinTime,
        this.ranking,
        this.transcend,
        this.averageVelocity,
      });

  factory RouteCheckInfo.fromJson(Map<String, dynamic> json) => _$RouteCheckInfoFromJson(json);
  Map<String, dynamic> toJson() => _$RouteCheckInfoToJson(this);
}
