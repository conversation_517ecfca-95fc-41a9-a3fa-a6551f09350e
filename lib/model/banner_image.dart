import 'package:json_annotation/json_annotation.dart';
part 'banner_image.g.dart';

@JsonSerializable()
class BannerImage{
  num? id;
  String? photoPath;
  String? title;
  String? jumpUrl;
  String? startTime;
  String? endTime;
  String? creatTime;
  num? jumpType;
  String? appletName;
  String? appletId;

  BannerImage(
      {this.photoPath,
        this.title,
        this.jumpUrl,
        this.startTime,
        this.endTime,
        this.creatTime,
        this.jumpType,
        this.appletName,
        this.appletId,
      });
  factory BannerImage.fromJson(Map<String, dynamic> json) => _$BannerImageFromJson(json);
  Map<String, dynamic> toJson() => _$BannerImageToJson(this);
}