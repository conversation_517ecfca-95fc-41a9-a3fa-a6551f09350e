// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pay_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PayInfo _$PayInfoFromJson(Map<String, dynamic> json) => PayInfo(
      return_code: json['return_code'] as String?,
      return_msg: json['return_msg'] as String?,
      appid: json['appid'] as String?,
      partnerId: json['partnerId'] as String?,
      device_info: json['device_info'] as String?,
      nonceStr: json['nonceStr'] as String?,
      timeStamp: json['timeStamp'] as String?,
      sign: json['sign'] as String?,
      result_code: json['result_code'] as String?,
      trade_type: json['trade_type'] as String?,
      prepayId: json['prepayId'] as String?,
      orderString: json['orderString'] as String?,
    );

Map<String, dynamic> _$PayInfoToJson(PayInfo instance) => <String, dynamic>{
      'return_code': instance.return_code,
      'return_msg': instance.return_msg,
      'appid': instance.appid,
      'partnerId': instance.partnerId,
      'device_info': instance.device_info,
      'nonceStr': instance.nonceStr,
      'timeStamp': instance.timeStamp,
      'sign': instance.sign,
      'result_code': instance.result_code,
      'trade_type': instance.trade_type,
      'prepayId': instance.prepayId,
      'orderString': instance.orderString,
    };
