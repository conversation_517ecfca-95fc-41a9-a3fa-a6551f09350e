import 'package:json_annotation/json_annotation.dart';

import 'prize_claim_form.dart';

part 'activities_challenge_store_reward_statistic.g.dart';

@JsonSerializable()
class ActivitiesChallengeStoreRewardStatistic {
  ShopInfo? shopInfo;
  List<PhysicalRewardStatisticVoList>? physicalRewardStatisticVoList;
  List<PhysicalRewardStatisticVoList>? luckyDrawaPhysicalRewardStatisticVoList;

  ActivitiesChallengeStoreRewardStatistic(
      {this.shopInfo, this.physicalRewardStatisticVoList,this.luckyDrawaPhysicalRewardStatisticVoList});

  ActivitiesChallengeStoreRewardStatistic.fromJson(Map<String, dynamic> json) {
    shopInfo = json['shopInfo'] != null
        ? new ShopInfo.fromJson(json['shopInfo'])
        : null;
    if (json['physicalRewardStatisticVoList'] != null) {
      physicalRewardStatisticVoList = <PhysicalRewardStatisticVoList>[];
      json['physicalRewardStatisticVoList'].forEach((v) {
        physicalRewardStatisticVoList!
            .add(new PhysicalRewardStatisticVoList.fromJson(v));
      });
    }

    if (json['luckyDrawaPhysicalRewardStatisticVoList'] != null) {
      luckyDrawaPhysicalRewardStatisticVoList = <PhysicalRewardStatisticVoList>[];
      json['luckyDrawaPhysicalRewardStatisticVoList'].forEach((v) {
        luckyDrawaPhysicalRewardStatisticVoList!
            .add(new PhysicalRewardStatisticVoList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.shopInfo != null) {
      data['shopInfo'] = this.shopInfo!.toJson();
    }
    if (this.physicalRewardStatisticVoList != null) {
      data['physicalRewardStatisticVoList'] =
          this.physicalRewardStatisticVoList!.map((v) => v.toJson()).toList();
    }
    if (this.luckyDrawaPhysicalRewardStatisticVoList != null) {
      data['luckyDrawaPhysicalRewardStatisticVoList'] =
          this.luckyDrawaPhysicalRewardStatisticVoList!.map((v) => v.toJson()).toList();
    }
    return data;
  }

}

@JsonSerializable()
class PhysicalRewardStatisticVoList {
  String? rewardId;
  String? name;
  String? image;
  String? description;
  String? prizeClaimDeadline;
  int? totalCount;
  int? distributedCount;

  PhysicalRewardStatisticVoList(
      {this.rewardId,
        this.name,
        this.image,
        this.description,
        this.prizeClaimDeadline,
        this.totalCount,
        this.distributedCount});

  PhysicalRewardStatisticVoList.fromJson(Map<String, dynamic> json) {
    rewardId = json['rewardId'];
    name = json['name'];
    image = json['image'];
    description = json['description'];
    prizeClaimDeadline = json['prizeClaimDeadline'];
    totalCount = json['totalCount'];
    distributedCount = json['distributedCount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rewardId'] = this.rewardId;
    data['name'] = this.name;
    data['image'] = this.image;
    data['description'] = this.description;
    data['prizeClaimDeadline'] = this.prizeClaimDeadline;
    data['totalCount'] = this.totalCount;
    data['distributedCount'] = this.distributedCount;
    return data;
  }
}
