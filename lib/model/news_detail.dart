import 'package:json_annotation/json_annotation.dart';
part 'news_detail.g.dart';

@JsonSerializable()
class NewsDetail{
  num? id;
  num? postRideId; //骑行ID
  String? faceUrl;
  String? bodyName;
  String? title;
  String? content;
  String? jumpUrl;
  num? startTime;
  num? endTime;
  String? appletId;
  num? jumpType;   //跳转类型 postRideId不为null且不为0-轨迹详情  1-跳转H5（区分淘宝打开和网页打开）2-跳转小程序
  num? createTime;
  num? updateTime;
  String? pushTitle;
  String? pushContent;
  NewsDetail({
    this.id,
    this.postRideId,
    this.faceUrl,
    this.bodyName,
    this.title,
    this.content,
    this.jumpUrl,
    this.startTime,
    this.endTime,
    this.appletId,
    this.jumpType,
    this.createTime,
    this.updateTime,
    this.pushTitle,
    this.pushContent
});
  factory NewsDetail.fromJson(Map<String, dynamic> json) => _$NewsDetailFromJson(json);
  Map<String, dynamic> toJson() => _$NewsDetailToJson(this);
}