import 'package:json_annotation/json_annotation.dart';

part 'nearby_shop.g.dart';

@JsonSerializable()
class NearbyShop {
  final String? address;
  final BusinessCloseTime? businessCloseTime;
  final BusinessOpenTime? businessOpenTime;
  final String? coverImageUrl;
  final String? detailContent;
  final int? districtStartPoint;
  final String? id;
  final String? latLon;
  final String? name;
  final int? sort;
  final String? status;
  final int? telephone;
  final String? type;

  const NearbyShop({
    this.address,
    this.businessCloseTime,
    this.businessOpenTime,
    this.coverImageUrl,
    this.detailContent,
    this.districtStartPoint,
    this.id,
    this.latLon,
    this.name,
    this.sort,
    this.status,
    this.telephone,
    this.type,
  });

  factory NearbyShop.fromJson(Map<String, dynamic> json) =>
      _$NearbyShopFromJson(json);

  Map<String, dynamic> toJson() => _$NearbyShopToJson(this);
}

@JsonSerializable()
class BusinessCloseTime {
  final int? hour;
  final int? minute;
  final int? nano;
  final int? second;

  const BusinessCloseTime({
    this.hour,
    this.minute,
    this.nano,
    this.second,
  });

  factory BusinessCloseTime.fromJson(Map<String, dynamic> json) =>
      _$BusinessCloseTimeFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessCloseTimeToJson(this);
}

@JsonSerializable()
class BusinessOpenTime {
  final int? hour;
  final int? minute;
  final int? nano;
  final int? second;

  const BusinessOpenTime({
    this.hour,
    this.minute,
    this.nano,
    this.second,
  });

  factory BusinessOpenTime.fromJson(Map<String, dynamic> json) =>
      _$BusinessOpenTimeFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessOpenTimeToJson(this);
}
