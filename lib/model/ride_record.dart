import 'dart:convert';

import 'package:new_edge/model/ride_extras.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ride_record.g.dart';

@JsonSerializable()
class RideRecord {
  int? id; //	主键
  int? postRideId; //	骑行业务主键ID
  int? uid; //	用户id
  int? meter; //	距离 - 米
  int? second; //	耗时 - 秒
  double? speed; //	运动速度 - 米/秒
  double? maxSpeed; //	最快速度 - 米/秒
  double? fullSpeed; //	全程速度（含暂停时间）- 米/秒
  int? climb; //	爬升距离 - 米
  int? startTime; //	骑行开始时间
  int? endTime; //	骑行结束时间
  int? uploadTime; //	上传时间
  int? sampleInterval; //	取点时间间隔
  String? source; //	来源 比如跑表
  String? rideId; //	骑行记录ID（客户端生产）
  int? wgs; //	地图类型 baidu=0，gaode=1
  String? province; //	定位省份
  String? city; //	定位城市
  int? type; //	类型 0-室外
  String? coverImg; //	轨迹缩略图
  String? coverMapImg; //	轨迹缩略图
  String? lightCoverImg; //	轨迹缩略图
  String? lightCoverMapImg; //	轨迹缩略图
  int? status; //	状态 1-正常 4-隐私模式 8-作弊 9-删除
  int? subStatus; //	子状态 status=8时。1-数据作弊 2-时间冲突 3-数据作弊和时间冲突
  String?
      content; //	骑行轨迹坐标点//["lat":23147769, "lng":113298418, "starTime":1627647311]
  String? pauseTime; //	暂停时间数据
  String? altitude; //	海拔数据
  String? heartRate; //	心率数据
  String? cadence; //	踏频数据
  String? power; //	功率数据
  String? extras; //	功率数据
  String? locationDetail; //轨迹点["altitude":2770.0, "meter":5265.0, "speed":9.0]
  double? _maxAltitude; // 最大海拔
  double? _minAltitude; // 最小海拔
  double? _avgAltitude; // 平均海拔
  int? _maxAltitudeIndex; // 最大海拔index
  int? _minAltitudeIndex; // 最小海拔index
  int? _maxHeartRate; // 最大心率
  int? _minHeartRate; // 最小心率
  int? _avgHeartRate; // 平均心率
  int? _maxHeartRateIndex; // 最大心率index
  int? _minHeartRateIndex; // 最小心率index
  int? _maxPowerRate; // 最大功率
  int? _minPowerRate; // 最小功率
  int? _avgPowerRate; // 平均功率
  int? _maxPowerRateIndex; // 最大功率index
  int? _minPowerRateIndex; // 最小功率index
  int? _maxCadenceRate; // 最大踏频
  int? _minCadenceRate; // 最小踏频
  int? _avgCadenceRate; // 平均踏频
  int? _maxCadenceRateIndex; // 最大踏频index
  int? _minCadenceRateIndex; // 最小踏频index
  int? _maxSpeedIndex; // 最大速度index
  int? _minSpeedIndex; // 最小速度index
  int? heartRateStatus; // 心率可见性 0可见  1不可见
  int? cadenceStatus; // 踏频可见性
  int? powerStatus; // 功率可见性

  Map? _extras;

  RideRecord({
    this.id,
    this.postRideId,
    this.uid,
    this.meter,
    this.second,
    this.speed,
    this.maxSpeed,
    this.fullSpeed,
    this.climb,
    this.startTime,
    this.endTime,
    this.uploadTime,
    this.sampleInterval,
    this.locationDetail,
    this.source,
    this.rideId,
    this.wgs,
    this.province,
    this.city,
    this.type,
    this.coverImg,
    this.coverMapImg,
    this.lightCoverImg,
    this.lightCoverMapImg,
    this.status,
    this.subStatus,
    this.content,
    this.pauseTime,
    this.altitude,
    this.heartRate,
    this.cadence,
    this.power,
    this.extras,
    this.heartRateStatus,
    this.cadenceStatus,
    this.powerStatus,
  });

  factory RideRecord.fromJson(Map<String, dynamic> json) =>
      _$RideRecordFromJson(json);

  Map<String, dynamic> toJson() => _$RideRecordToJson(this);

  int? get maxCadenceRateIndex {
    return _maxCadenceRateIndex;
  }

  set setMaxCadenceRateIndex(int index) {
    this._maxCadenceRateIndex = index;
  }

  int? get minCadenceRateIndex {
    return _minCadenceRateIndex;
  }

  set setMinCadenceRateIndex(int index) {
    this._minCadenceRateIndex = index;
  }

  set setAvgCadenceRate(int cadenceRate) {
    this._avgCadenceRate = cadenceRate;
  }

  int? get avgCadenceRate {
    return _avgCadenceRate;
  }

  int? get maxAltitudeIndex {
    return _maxAltitudeIndex;
  }

  set setMaxAltitudeIndex(int index) {
    this._maxAltitudeIndex = index;
  }

  int? get minAltitudeIndex {
    return _minAltitudeIndex;
  }

  set setMinAltitudeIndex(int index) {
    this._minAltitudeIndex = index;
  }

  set setAvgAltitude(double altitude) {
    this._avgAltitude = altitude;
  }

  double? get avgAltitude {
    return _avgAltitude;
  }

  int? get maxSpeedIndex {
    return _maxSpeedIndex;
  }

  double get avgSpeed {
    if ((second ?? 0) == 0) {
      return 0;
    }
    return meter!.toDouble() / second!;
  }

  set setMaxSpeedIndex(int index) {
    this._maxSpeedIndex = index;
  }

  int? get minSpeedIndex {
    return _minSpeedIndex;
  }

  set setMinSpeedIndex(int index) {
    this._minSpeedIndex = index;
  }

  int? get maxPowerRateIndex {
    return _maxPowerRateIndex;
  }

  set setMaxPowerRateIndex(int index) {
    this._maxPowerRateIndex = index;
  }

  int? get minPowerRateIndex {
    return _minPowerRateIndex;
  }

  set setMinPowerRateIndex(int index) {
    this._minPowerRateIndex = index;
  }

  set setAvgPowerRate(int powerRate) {
    this._avgPowerRate = powerRate;
  }

  int? get avgPowerRate {
    return _avgPowerRate;
  }

  int? get maxHeartRateIndex {
    return _maxHeartRateIndex;
  }

  set setMaxHeartRateIndex(int index) {
    this._maxHeartRateIndex = index;
  }

  int? get minHeartRateIndex {
    return _minHeartRateIndex;
  }

  set setMinHeartRateIndex(int index) {
    this._minHeartRateIndex = index;
  }

  set setAvgHeartRate(int heartRate) {
    this._avgHeartRate = heartRate;
  }

  int? get avgHeartRate {
    return _avgHeartRate;
  }

  int? get maxCadenceRate {
    return _maxCadenceRate;
  }

  set setMaxCadenceRate(int cadencerate) {
    this._maxCadenceRate = cadencerate;
  }

  int? get minCadenceRate {
    return _minCadenceRate;
  }

  set setMinCadenceRate(int cadencerate) {
    this._minCadenceRate = cadencerate;
  }

  int? get maxPowerRate {
    return _maxPowerRate;
  }

  set setMaxPowerRate(int powerrate) {
    this._maxPowerRate = powerrate;
  }

  int? get minPowerRate {
    return _minPowerRate;
  }

  set setMinPowerRate(int powerrate) {
    this._minPowerRate = powerrate;
  }

  int? get maxHeartRate {
    return _maxHeartRate;
  }

  set setMaxHeartRate(int heartrate) {
    this._maxHeartRate = heartrate;
  }

  int? get minHeartRate {
    return _minHeartRate;
  }

  set setMinHeartRate(int heartrate) {
    this._minHeartRate = heartrate;
  }

  Map? get getExtras {
    if (this._extras == null) {
      caluavgExtras();
    }
    return _extras;
  }

  double? get maxAltitude {
    return _maxAltitude;
  }

  set setMaxAltitude(double altitude) {
    this._maxAltitude = altitude;
  }

  double? get minAltitude {
    return _minAltitude;
  }

  set setMinAltitude(double altitude) {
    this._minAltitude = altitude;
  }

  void caluavgExtras() {
    if (this.extras != null) {
      this.extras!.replaceAll("\\", "");
      try {
        Map? rideExtras = json.decode(this.extras!);
        if (rideExtras != null) {
          this._extras = rideExtras;
        }
      } catch (e) {
        print(e);
      }
    }
  }

  static RideRecord getRecordFromNativeMap(Map record) {
    // map to model
    RideRecord rideRecord = RideRecord();
    rideRecord.meter = record["meter"];
    rideRecord.second = record["second"];
    rideRecord.startTime = record["startTime"];
    rideRecord.endTime = record["endTime"];
    rideRecord.rideId = record["rideId"];
    rideRecord.sampleInterval = record["sampleInterval"];
    rideRecord.altitude = record["altitude"]; //[float,float,float]
    rideRecord.pauseTime = record["pauseTime"]; //[[index,second]]
    rideRecord.climb = record["climb"];
    rideRecord.maxSpeed = record["maxSpeed"];
    rideRecord.wgs = 1;
    rideRecord.province = record["province"] ?? "";
    rideRecord.city = record["city"] ?? "";
    rideRecord.speed = rideRecord.meter!.toDouble() / rideRecord.second!;
    rideRecord.coverImg = record["coverImg"];

    // 拆分content，服务端暂不支持修改content字段
    List content = [];
    List locationDetail = [];
    List contentData = jsonDecode(record["content"]);
    contentData.forEach((element) {
      List locationData = element;
      List contentElement = [];
      List locationDetailElement = [];
      for (var i = 0; i < locationData.length; i++) {
        if (i < 3) {
          contentElement.add(locationData[i]);
        } else {
          locationDetailElement.add(locationData[i]);
        }
      }
      content.add(contentElement);
      locationDetail.add(locationDetailElement);
    });
    // rideRecord.content = jsonEncode(content);// [[latitude,longitude]]
    var contentString = "";
    content.forEach((element) {
      contentString += jsonEncode(element);
      contentString += '-';
    });
//    var contentString = content.reduce((value, element) {
//      return jsonEncode(value) + '-' + jsonEncode(element);
//    });
    if (contentString.length > 0) {
      contentString = contentString.substring(0, contentString.length - 1);
    }
    rideRecord.content =
        contentString; // [latitude,longitude]-[latitude,longitude]
    rideRecord.locationDetail =
        jsonEncode(locationDetail); //[[elevation,meter,speed]]
    return rideRecord;
  }

  static RideRecord getRecordFromDBMap(Map record) {
    // map to model
    RideRecord rideRecord = RideRecord();
    rideRecord.meter = record["meter"];
    rideRecord.second = record["second"];
    rideRecord.startTime = record["startTime"];
    rideRecord.endTime = record["endTime"];
    rideRecord.rideId = record["rideId"];
    rideRecord.sampleInterval = record["sampleInterval"];
    rideRecord.altitude = record["altitude"]; //[float,float,float]
    rideRecord.pauseTime = record["pauseTime"]; //[[index,second]]
    rideRecord.climb = record["climb"];
    rideRecord.maxSpeed = record["maxSpeed"];
    rideRecord.wgs = 1;
    rideRecord.province = record["province"] ?? "";
    rideRecord.city = record["city"] ?? "";
    rideRecord.speed = rideRecord.meter!.toDouble() / rideRecord.second!;
    rideRecord.content =
        record["content"]; // [latitude,longitude]-[latitude,longitude]
    rideRecord.locationDetail =
        record["locationDetail"]; //[[elevation,meter,speed]]
    rideRecord.coverImg = record["coverImg"];
    return rideRecord;
  }
}
