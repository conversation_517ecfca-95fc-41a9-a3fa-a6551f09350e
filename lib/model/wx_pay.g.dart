// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wx_pay.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WxPay _$WxPayFromJson(Map<String, dynamic> json) => WxPay(
      package: json['package'] as String?,
      appid: json['appid'] as String?,
      sign: json['sign'] as String?,
      partnerid: json['partnerid'] as String?,
      prepayid: json['prepayid'] as String?,
      noncestr: json['noncestr'] as String?,
      timestamp: json['timestamp'] as String?,
    );

Map<String, dynamic> _$WxPayToJson(WxPay instance) => <String, dynamic>{
      'package': instance.package,
      'appid': instance.appid,
      'sign': instance.sign,
      'partnerid': instance.partnerid,
      'prepayid': instance.prepayid,
      'noncestr': instance.noncestr,
      'timestamp': instance.timestamp,
    };
