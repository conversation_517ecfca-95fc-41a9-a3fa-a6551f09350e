import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:new_edge/channels/common_channel.dart';
import 'package:new_edge/model/bike.dart';
import 'package:new_edge/util/image_utils.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/push.dart';
import 'package:new_edge/util/settings.dart';
import 'package:new_edge/pages/common/developer_tools.dart';
import 'package:new_edge/util/sp_utils.dart';
import 'package:json_annotation/json_annotation.dart';
import '../app.dart';
import 'ride_data_type.dart';
import 'user.dart';

part 'account.g.dart';

@JsonSerializable()
class Account extends User {
  String? birthday;
  String? city;
  String? firstName;
  String? lastName;
  int? idType;
  String? idNumber;
  String? nationality;

  int? height;
  int? idAuth;
  String? intro;
  double? latitude;
  double? longitude;
  String? mail;
  String? mobile;
  String? areaCode;
  String? province;
  int? regDatetime;
  String? sid;
  int? status;
  int? type;
  int? weight;
  String? headerBgUrl;
  bool? isLogout;
  bool? isClerk;
  bool? isCoachLogin;
  int? canLive;
  int? nickRepeat;

  //默认自行车
  Bike? defaultbike;

  //自行车列表
  List<Bike>? bikes;

  /// 判断是否教练
  static bool? isCoach;

  int? coachId;

  /// 是否新用户
  int? newUser;
  List<String>? certificates;

  int? memberLevel; // 会员级别（1.普通会员）
  int? memberStartTime; // 会员开始时间
  int? memberEndTime; // 会员结束时间
  int? userSource; // 用户来源(0.IMIN注册 1.悦跑圈会员兑换)
  String? otherBodyState; // 身体状况
  int? bodyStatus; //身体情况 0-未填 1-无状况 2-有状况
  int? bodyVisible; //身体状况是否公开 0-否 1-是
  List<BodyStateDict>? bodyStateDict; //用户身体状况

  int? bikeNum = 2; //自行车数量
  String? raceToken;
  int? fansTotal; //粉丝数
  int? followTotal; //关注数
  String? backgroundUrl; //个人主页背景图
  int? pushMessageType; //接收IMIN消息通知 0-公开  1-不公开
  int? feedVisableType; //数据动态默认可见性
  int? feedBikeType; //数据动态显示关联自行车
  String? rideDataType; //心率，踏频，功率数据设置

  Account({
    this.otherBodyState,
    this.bodyStatus,
    this.bodyVisible,
    this.bodyStateDict,
    this.birthday,
    this.city,
    this.firstName,
    this.lastName,
    this.idType,
    this.idNumber,
    this.nationality,
    this.areaCode,
    String? faceUrl,
    int? gender,
    this.height,
    this.intro,
    this.latitude,
    this.longitude,
    this.mail,
    String? nick,
    this.mobile,
    this.idAuth,
    this.province,
    this.regDatetime,
    this.isCoachLogin,
    this.sid,
    this.status,
    this.type,
    int? uid,
    this.coachId,
    this.newUser,
    this.headerBgUrl,
    this.weight,
    this.isLogout,
    this.canLive,
    this.nickRepeat,
    this.defaultbike,
    this.bikes,
    this.certificates,
    int? isMember,
    this.memberLevel,
    this.memberStartTime,
    this.memberEndTime,
    this.userSource,
    this.bikeNum,
    this.raceToken,
    this.fansTotal,
    this.followTotal,
    this.backgroundUrl,
    this.feedVisableType,
    this.feedBikeType,
    this.pushMessageType,
    this.rideDataType,
    int? kolStatus,
    String? kolIntro,
  }) : super(
          faceUrl: faceUrl,
          gender: gender,
          nick: nick,
          uid: uid,
          isMember: isMember,
          kolStatus: kolStatus,
          kolIntro: kolIntro,
        ) {
    if (this.intro == null) {
      this.intro = "";
    }
    if (this.headerBgUrl == null) {
      this.headerBgUrl = "";
    }
    if (this.isLogout == null) {
      this.isLogout = false;
    }
  }

  factory Account.fromJson(Map<String, dynamic> json) =>
      _$AccountFromJson(json);

  Map<String, dynamic> toJson() => _$AccountToJson(this);

  static Account? loginAccount;

  static updateAccount(Account? account) async {
    if (loginAccount == null) {
      return;
    }
    account!.sid = loginAccount!.sid;
    account.uid = loginAccount!.uid;
    await login(account);
  }

  static loginAndToHome(BuildContext context, Account account) async {
    await login(account);
    var downloadChannel = await CommonChannel.getChannel();
    if (!DeveloperTools.isJoyrunInner) {
      // 埋点
      // dataStatistics.login("${account.uid}");
      // dataStatistics.profileSet(
      //   downloadChannel: downloadChannel,
      //   nickname: account.nick,
      //   sex: account.gender == 0 ? "未知" : (account.gender == 1 ? "男" : "女"),
      //   height: account.height,
      //   weight: account.weight,
      //   birthday: account.birthday,
      //   province: account.province,
      //   city: account.city,
      // );
    } else {
      // dataStatistics.profileSet(flamousUid: account.uid);
    }
    // // launch
    // var show = localSettings.getBool(LocalSettings.LAUNCH_GUIDE_SHOW, false);
    // if (show) {
    Navigator.of(context).pushNamedAndRemoveUntil("/home", (route) => false);
    // } else {
    //   localSettings.setBool(LocalSettings.LAUNCH_GUIDE_SHOW, true);
    //   Navigator.of(context).pushNamedAndRemoveUntil(
    //       "/launchGuide", ModalRoute.withName("/launchGuide"));
    // }
  }

  static login(Account? account) async {
    if (account == null || account.sid == null) {
      return;
    }
    loginAccount = account;
    SpUtil.putString('loginAccount', jsonEncode(loginAccount));
  }

  static Future<Account?> initLoginAccount() async {
    var text = SpUtil.getString("loginAccount");
    if (text == null || text.isEmpty) {
      return null;
    }
    var account = Account.fromJson(jsonDecode(text));
    Account.login(account);
    return account;
  }

  static logout() async {
    Push.instance!.deregister();
    loginAccount = null;
    await SpUtil.remove("loginAccount");
  }

  static Future<void> logoutAndToLogin() async {
    if (loginAccount == null) {
      return;
    }
    logout();
    localSettings.setBool(
        LocalSettings.NEW_USER_GUIDE_SHOW, false); // 避免切换账号没有出现新手引导的bug
    if (!DeveloperTools.isJoyrunInner) {
      // dataStatistics.logout();
    }
    navigatorKey.currentState!
        .pushNamedAndRemoveUntil("/login", ModalRoute.withName("/login"));
  }

  static bool isLogin() {
    return loginAccount != null;
  }

  Account clone() {
    return Account.fromJson(jsonDecode(jsonEncode(toJson())));
  }
}

@JsonSerializable()
class BodyStateDict {
  int? id;
  int? type;
  String? name;

  BodyStateDict({this.id, this.type, this.name});

  factory BodyStateDict.fromJson(Map<String, dynamic> json) =>
      _$BodyStateDictFromJson(json);

  Map<String, dynamic> toJson() => _$BodyStateDictToJson(this);
}
