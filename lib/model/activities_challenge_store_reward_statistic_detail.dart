import 'package:json_annotation/json_annotation.dart';

part 'activities_challenge_store_reward_statistic_detail.g.dart';

@JsonSerializable()
class ActivitiesChallengeStoreRewardStatisticDetail {
  int? uid;
  String? faceUrl;
  String? nick;
  String? mobile;
  String? distributionUid;
  String? distributionFullName;
  int? distributionStatus;
  String? distributionTime;

  ActivitiesChallengeStoreRewardStatisticDetail(
      {this.uid,
        this.faceUrl,
        this.nick,
        this.mobile,
        this.distributionUid,
        this.distributionFullName,
        this.distributionStatus,
        this.distributionTime});

  ActivitiesChallengeStoreRewardStatisticDetail.fromJson(Map<String, dynamic> json) {
    uid = json['uid'];
    faceUrl = json['faceUrl'];
    nick = json['nick'];
    mobile = json['mobile'];
    distributionUid = json['distributionUid'];
    distributionFullName = json['distributionFullName'];
    distributionStatus = json['distributionStatus'];
    distributionTime = json['distributionTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['uid'] = this.uid;
    data['faceUrl'] = this.faceUrl;
    data['nick'] = this.nick;
    data['mobile'] = this.mobile;
    data['distributionUid'] = this.distributionUid;
    data['distributionFullName'] = this.distributionFullName;
    data['distributionStatus'] = this.distributionStatus;
    data['distributionTime'] = this.distributionTime;
    return data;
  }
}
