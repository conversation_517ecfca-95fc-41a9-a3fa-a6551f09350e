import 'package:copy_with_extension/copy_with_extension.dart';
import 'package:json_annotation/json_annotation.dart';

part 'shopping_address.g.dart';

@JsonSerializable()
@CopyWith()
class ShoppingAddress {
  String? address;
  String? city;
  int? id;
  String? mobile;
  String? areaCode;
  String? postCode;
  String? province;
  String? shoppingName;
  String? town;
  num? uid;

  ShoppingAddress(
      {this.address,
      this.city,
      this.id,
      this.mobile,
      this.areaCode = "+86",
      this.postCode,
      this.province,
      this.shoppingName,
      this.town,
      this.uid});

  factory ShoppingAddress.fromJson(Map<String, dynamic> json) =>
      _$ShoppingAddressFromJson(json);

  Map<String, dynamic> toJson() => _$ShoppingAddressToJson(this);

  bool isValid() {
    return (address?.isNotEmpty ?? false) &&
        (mobile?.isNotEmpty ?? false) &&
        (shoppingName?.isNotEmpty ?? false) &&
        (town?.isNotEmpty ?? false);
  }

  String getArea() {
    if ((province ?? "").isEmpty &&
        (city ?? "").isEmpty &&
        (town ?? "").isEmpty) {
      return "";
    }

    return "$province $city $town";
  }

  String getFullAddress() {
    return "${getArea()} ${address ?? ""}";
  }
}
