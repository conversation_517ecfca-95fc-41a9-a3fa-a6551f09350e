import 'package:json_annotation/json_annotation.dart';

part 'challenge_detail.g.dart';

////奖品类型（1实物邮寄 2实物到店 3邮寄加到店 4虚拟奖品）
@JsonEnum(valueField: "type")
enum CollectType {
  @JsonValue(1)
  physicalMail(1),
  @JsonValue(2)
  physicalStore(2),
  @JsonValue(3)
  physicalMailStore(3),
  @JsonValue(4)
  third(4);

  final int value;

  const CollectType(this.value);
}

///奖品类型 0：完赛 1：抽奖
@JsonEnum(valueField: "prizeType")
enum PrizeType {
  @JsonValue(0)
  complete,
  @JsonValue(1)
  lottery;
}

///状态 0：未开始，1:报名中，2:进行中，3，已结束
@JsonEnum(valueField: "runningStatus")
enum RunningStatus {
  @JsonValue(0)
  notStarted,
  @JsonValue(1)
  signUp,
  @JsonValue(2)
  running,
  @JsonValue(3)
  ended;
}

///领奖状态 0去领奖 1已登记 2已领奖
@JsonEnum(valueField: "prizeGetState")
enum PrizeGetState {
  @JsonValue(0)
  toGet,
  @JsonValue(1)
  registered,
  @JsonValue(2)
  got;
}

/// 奖品领取方式 1 到店 2邮寄 3虚拟
@JsonEnum(valueField: "prizeGetMethod")
enum PrizeGetMethod {
  @JsonValue(1)
  store,
  @JsonValue(2)
  mail,
  @JsonValue(3)
  virtual;
}

extension ChallengeDetailExtension on ChallengeDetail {
  num? get deliveryFee {
    return prizeList
        ?.firstWhere((element) =>
            element.type == CollectType.physicalMailStore ||
            element.type == CollectType.physicalMail)
        .deliveryFee;
  }

  bool get hasPhysicalReward {
    return prizeList?.any((element) =>
            element.type == CollectType.physicalMail ||
            element.type == CollectType.physicalStore ||
            element.type == CollectType.physicalMailStore) ??
        false;
  }

  bool get hasVirtualReward {
    return medalInfoNew != null;
  }

  //实物奖品
  List<PrizeInfo> get physicalRewards {
    return prizeList
            ?.where((element) =>
                element.type == CollectType.physicalMail ||
                element.type == CollectType.physicalStore ||
                element.type == CollectType.physicalMailStore)
            .toList() ??
        [];
  }

  //虚拟奖品
  bool get hasVirtualRewardProvided {
    return medalInfoNew?.trophyRecordId != null;
  }

  //第三方奖品
  List<PrizeInfo> get thirdPartyRewards {
    return prizeList
            ?.where((element) => element.type == CollectType.third)
            .toList() ??
        [];
  }
}

extension PrizeInfoExtension on PrizeInfo {
  bool get isProvided {
    return prizeClaimFormId != null;
  }

  bool get isPhysicalReward  {
    return type == CollectType.physicalMail ||
        type == CollectType.physicalMailStore;
  }

  bool get isThirdPartyReward  {
    return type == CollectType.third;
  }

  bool get hasStore {
    return type == CollectType.physicalStore ||
        type == CollectType.physicalMailStore;
  }

  bool get hasExpress {
    return type == CollectType.physicalMail ||
        type == CollectType.physicalMailStore;
  }
}

@JsonSerializable()
class ChallengeDetail {
  final List<String>? accomplishHeadPortraits;
  final int? accomplishNum;
  final int? altitudeTarget;
  final bool? autonym;
  final String? createBy;
  final String? createTime;
  final int? durationTarget;
  final String? endTime;
  final String? groupChatLink;
  final String? headBg;
  final String? id;
  final int? integralComplete;
  final int? isDeleted;
  final String? logo;
  final int? lotteryNum;
  final String? luckyDrawaId;
  final LuckyDrawaInfoVo? luckyDrawaInfoVo;
  final String? medalId;
  final MedalInfo? medalInfoNew;
  final int? mileageTarget;
  final int? playersLimit;
  final int? price;
  final List<PrizeInfo>? prizeList;
  final String? registrationEndTime;
  final String? registrationRecordId;
  final String? registrationStartTime;
  final String? richTextDescriptions;
  final String? richTextLiabilityWaiver;
  final int? runningStatus;
  final List<String>? signUpHeadPortraits;
  final int? signUpNum;
  final int? singleMinimumMileageLimit;
  final String? startTime;
  final int? status;
  final bool? successPrizeToStoreFlag;
  final int? tenantId;
  final int? timesTarget;
  final String? title;
  final String? titleEn;
  final int? type;
  final String? updateBy;
  final String? updateTime;
  final int? userAltitudeTarget;
  final int? userDurationTarget;
  final bool? userIsComplete;
  final int? userMileageTarget;
  final bool? userSignUpFlag;
  final bool? allowJoin;
  final String? overRangeTips;
  final int? userTimesTarget;
  final String? wxMiniShareImg;

  const ChallengeDetail({
    this.accomplishHeadPortraits,
    this.accomplishNum,
    this.altitudeTarget,
    this.autonym,
    this.createBy,
    this.createTime,
    this.durationTarget,
    this.endTime,
    this.groupChatLink,
    this.headBg,
    this.id,
    this.integralComplete,
    this.isDeleted,
    this.logo,
    this.lotteryNum,
    this.luckyDrawaId,
    this.luckyDrawaInfoVo,
    this.medalId,
    this.medalInfoNew,
    this.mileageTarget,
    this.playersLimit,
    this.price,
    this.prizeList,
    this.registrationEndTime,
    this.registrationRecordId,
    this.registrationStartTime,
    this.richTextDescriptions,
    this.richTextLiabilityWaiver,
    this.runningStatus,
    this.signUpHeadPortraits,
    this.signUpNum,
    this.singleMinimumMileageLimit,
    this.startTime,
    this.status,
    this.successPrizeToStoreFlag,
    this.tenantId,
    this.timesTarget,
    this.title,
    this.titleEn,
    this.type,
    this.updateBy,
    this.updateTime,
    this.userAltitudeTarget,
    this.userDurationTarget,
    this.userIsComplete,
    this.userMileageTarget,
    this.userSignUpFlag,
    this.userTimesTarget,
    this.allowJoin,
    this.overRangeTips,
    this.wxMiniShareImg,
  });

  factory ChallengeDetail.fromJson(Map<String, dynamic> json) =>
      _$ChallengeDetailFromJson(json);

  Map<String, dynamic> toJson() => _$ChallengeDetailToJson(this);
}

@JsonSerializable()
class LuckyDrawaInfoVo {
  final String? channelType;
  final String? channelTypeId;
  final String? cover;
  final String? createBy;
  final String? createTime;
  final String? endTime;
  final List<PrizeInfo>? gainPrizeList;
  final String? id;
  final int? isDeleted;
  final String? name;
  final int? numberOfLotteryDraws;
  final List<PrizeInfo>? prizeList;
  final String? rtDescription;
  final String? startTime;
  final int? tenantId;
  final String? updateBy;
  final String? updateTime;
  final bool? useState;

  const LuckyDrawaInfoVo({
    this.channelType,
    this.channelTypeId,
    this.cover,
    this.createBy,
    this.createTime,
    this.endTime,
    this.gainPrizeList,
    this.id,
    this.isDeleted,
    this.name,
    this.numberOfLotteryDraws,
    this.prizeList,
    this.rtDescription,
    this.startTime,
    this.tenantId,
    this.updateBy,
    this.updateTime,
    this.useState,
  });

  factory LuckyDrawaInfoVo.fromJson(Map<String, dynamic> json) =>
      _$LuckyDrawaInfoVoFromJson(json);

  Map<String, dynamic> toJson() => _$LuckyDrawaInfoVoToJson(this);
}

@JsonSerializable()
class PrizeInfo {
  final bool? autonymFlag;
  final String? createBy;
  final String? createTime;
  final num? deliveryFee;
  final String? description;
  final String? id;
  final String? image;
  final int? initialQuantity;
  final int? isDeleted;
  final String? logo;
  final String? name;
  final String? sponsor;
  final String? pickupEndTime;
  final String? pickupStartTime;
  final String? placeType;
  final String? placeTypeId;
  final String? prizeClaimDeadline;
  final String? prizeClaimFormId; //奖品获得记录表id
  final PrizeType? prizeType; //奖品类型 0：完赛 1：抽奖
  final PrizeGetState? prizeGetState; //领奖状态 0去领奖 1已登记 2已领奖
  final int? repertoryNum;
  final String? scheduledTime;
  final int? tenantId;
  final CollectType? type; //奖品类型（1实物邮寄 2实物到店 3邮寄加到店 4虚拟奖品）
  final PrizeGetMethod? prizeGetMethod; // 奖品领取方式 1 到店 2邮寄 3虚拟
  final String? thirdPartyName;
  final String? updateBy;
  final String? updateTime;
  final int? winRate;

  const PrizeInfo({
    this.autonymFlag,
    this.createBy,
    this.createTime,
    this.deliveryFee,
    this.description,
    this.id,
    this.image,
    this.initialQuantity,
    this.isDeleted,
    this.logo,
    this.name,
    this.sponsor,
    this.pickupEndTime,
    this.pickupStartTime,
    this.placeType,
    this.placeTypeId,
    this.prizeClaimDeadline,
    this.prizeClaimFormId,
    this.prizeGetState,
    this.prizeGetMethod,
    this.prizeType,
    this.thirdPartyName,
    this.repertoryNum,
    this.scheduledTime,
    this.tenantId,
    this.type,
    this.updateBy,
    this.updateTime,
    this.winRate,
  });

  factory PrizeInfo.fromJson(Map<String, dynamic> json) =>
      _$PrizeInfoFromJson(json);

  Map<String, dynamic> toJson() => _$PrizeInfoToJson(this);
}

@JsonSerializable()
class MedalInfo {
  final String? shortTitle;
  final String? thumb;
  final String? trophyRecordId;
  final String? description;

  const MedalInfo({
    this.shortTitle,
    this.description,
    this.thumb,
    this.trophyRecordId,
  });

  factory MedalInfo.fromJson(Map<String, dynamic> json) =>
      _$MedalInfoFromJson(json);

  Map<String, dynamic> toJson() => _$MedalInfoToJson(this);
}
