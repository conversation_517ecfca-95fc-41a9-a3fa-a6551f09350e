import 'package:json_annotation/json_annotation.dart';

part 'join_challenge.g.dart';

@JsonSerializable()
class JoinChallenge {
  final String? logo;
  final int? altitudeProgress;
  final int? altitudeTarget;
  final String? challengeId;
  final int? durationProgress;
  final int? durationTarget;
  final String? endTime;
  final String? id;
  final int? isTargetCompleted;
  final int? mileageProgress;
  final int? mileageTarget;
  final String? startTime;
  final String? state;
  final int? timesProgress;
  final int? timesTarget;
  final String? title;
  final int? uid;

  const JoinChallenge({
    this.logo,
    this.altitudeProgress,
    this.altitudeTarget,
    this.challengeId,
    this.durationProgress,
    this.durationTarget,
    this.endTime,
    this.id,
    this.isTargetCompleted,
    this.mileageProgress,
    this.mileageTarget,
    this.startTime,
    this.state,
    this.timesProgress,
    this.timesTarget,
    this.title,
    this.uid,
  });

  factory JoinChallenge.fromJson(Map<String, dynamic> json) =>
      _$JoinChallengeFromJson(json);

  Map<String, dynamic> toJson() => _$JoinChallengeToJson(this);
}
