import 'package:json_annotation/json_annotation.dart';

import 'challenge_detail.dart';

part 'challenge_lucky_drawa.g.dart';

@JsonSerializable()
class ChallengeLuckyDrawa {
  final String? channelType;
  final String? channelTypeId;
  final String? cover;
  final String? createBy;
  final String? createTime;
  final String? endTime;
  final List<PrizeInfo>? gainPrizeList;
  final String? id;
  final int? isDeleted;
  final String? name;
  final int? numberOfLotteryDraws;
  @Json<PERSON>ey(defaultValue: [])
  final List<PrizeInfo> prizeList;
  final String? rtDescription;
  final String? startTime;
  final int? tenantId;
  final String? updateBy;
  final String? updateTime;
  final bool? useState;

  const ChallengeLuckyDrawa({
    this.channelType,
    this.channelTypeId,
    this.cover,
    this.createBy,
    this.createTime,
    this.endTime,
    this.gainPrizeList,
    this.id,
    this.isDeleted,
    this.name,
    this.numberOfLotteryDraws,
    required this.prizeList,
    this.rtDescription,
    this.startTime,
    this.tenantId,
    this.updateBy,
    this.updateTime,
    this.useState,
  });

  factory ChallengeLuckyDrawa.fromJson(Map<String, dynamic> json) =>
      _$ChallengeLuckyDrawaFromJson(json);

  Map<String, dynamic> toJson() => _$ChallengeLuckyDrawaToJson(this);
}
