// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activities_challenge.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ActivitiesChallenge _$ActivitiesChallengeFromJson(Map<String, dynamic> json) =>
    ActivitiesChallenge(
      activitiesChallengeRegistrationRecord:
          json['activitiesChallengeRegistrationRecord'] == null
              ? null
              : ActivitiesChallengeRegistrationRecord.fromJson(
                  json['activitiesChallengeRegistrationRecord']
                      as Map<String, dynamic>),
      altitudeTarget: (json['altitudeTarget'] as num?)?.toInt(),
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      durationTarget: (json['durationTarget'] as num?)?.toInt(),
      earlyBirdEndTime: json['earlyBirdEndTime'] as String?,
      earlyBirdPrice: json['earlyBirdPrice'] as num?,
      endTime: json['endTime'] as String?,
      finishUser: (json['finishUser'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      id: json['id'] as String?,
      inProgressUser: (json['inProgressUser'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      joined: json['joined'] as bool?,
      logo: json['logo'] as String?,
      wxMiniShareImg: json['wxMiniShareImg'] as String?,
      headBg: json['headBg'] as String?,
      detailImage: json['detailImage'] as String?,
      richTextDescriptions: json['richTextDescriptions'] as String?,
      richTextLiabilityWaiver: json['richTextLiabilityWaiver'] as String?,
      mileageTarget: (json['mileageTarget'] as num?)?.toInt(),
      price: json['price'] as num?,
      registrationEndTime: json['registrationEndTime'] as String?,
      registrationStartTime: json['registrationStartTime'] as String?,
      reward: json['reward'] == null
          ? null
          : Reward.fromJson(json['reward'] as Map<String, dynamic>),
      singleMinimumMileageLimit: json['singleMinimumMileageLimit'] as num?,
      startTime: json['startTime'] as String?,
      status: json['status'] as String?,
      sysOrgCode: json['sysOrgCode'] as String?,
      timesTarget: (json['timesTarget'] as num?)?.toInt(),
      title: json['title'] as String?,
      titleEn: json['titleEn'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      nextProcessingTime: json['nextProcessingTime'] as String?,
      luckyDrawaImgUrl: json['luckyDrawaImgUrl'] as String?,
      allowInStorePickup: json['allowInStorePickup'] as bool?,
      deliveryFee: json['deliveryFee'] as num?,
    )
      ..allowJoin = json['allowJoin'] as bool?
      ..overRangeTips = json['overRangeTips'] as String?
      ..type = (json['type'] as num?)?.toInt();

Map<String, dynamic> _$ActivitiesChallengeToJson(
        ActivitiesChallenge instance) =>
    <String, dynamic>{
      'activitiesChallengeRegistrationRecord':
          instance.activitiesChallengeRegistrationRecord,
      'altitudeTarget': instance.altitudeTarget,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'durationTarget': instance.durationTarget,
      'earlyBirdEndTime': instance.earlyBirdEndTime,
      'earlyBirdPrice': instance.earlyBirdPrice,
      'endTime': instance.endTime,
      'finishUser': instance.finishUser,
      'id': instance.id,
      'inProgressUser': instance.inProgressUser,
      'joined': instance.joined,
      'allowJoin': instance.allowJoin,
      'overRangeTips': instance.overRangeTips,
      'logo': instance.logo,
      'wxMiniShareImg': instance.wxMiniShareImg,
      'headBg': instance.headBg,
      'detailImage': instance.detailImage,
      'richTextDescriptions': instance.richTextDescriptions,
      'richTextLiabilityWaiver': instance.richTextLiabilityWaiver,
      'mileageTarget': instance.mileageTarget,
      'price': instance.price,
      'registrationEndTime': instance.registrationEndTime,
      'registrationStartTime': instance.registrationStartTime,
      'reward': instance.reward,
      'singleMinimumMileageLimit': instance.singleMinimumMileageLimit,
      'startTime': instance.startTime,
      'status': instance.status,
      'type': instance.type,
      'sysOrgCode': instance.sysOrgCode,
      'timesTarget': instance.timesTarget,
      'title': instance.title,
      'titleEn': instance.titleEn,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'nextProcessingTime': instance.nextProcessingTime,
      'luckyDrawaImgUrl': instance.luckyDrawaImgUrl,
      'allowInStorePickup': instance.allowInStorePickup,
      'deliveryFee': instance.deliveryFee,
    };

Reward _$RewardFromJson(Map<String, dynamic> json) => Reward(
      challengeId: json['challengeId'] as String?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      id: json['id'] as String?,
      image: json['image'] as String?,
      physicalReward: (json['physicalReward'] as List<dynamic>?)
          ?.map((e) => PhysicalReward.fromJson(e as Map<String, dynamic>))
          .toList(),
      sysOrgCode: json['sysOrgCode'] as String?,
      thirdPartyReward: (json['thirdPartyReward'] as List<dynamic>?)
          ?.map((e) => ThirdPartyReward.fromJson(e as Map<String, dynamic>))
          .toList(),
      type: json['type'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      virtualReward: (json['virtualReward'] as List<dynamic>?)
          ?.map((e) => VirtualReward.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RewardToJson(Reward instance) => <String, dynamic>{
      'challengeId': instance.challengeId,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'id': instance.id,
      'image': instance.image,
      'physicalReward': instance.physicalReward,
      'sysOrgCode': instance.sysOrgCode,
      'thirdPartyReward': instance.thirdPartyReward,
      'type': instance.type,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'virtualReward': instance.virtualReward,
    };

PhysicalReward _$PhysicalRewardFromJson(Map<String, dynamic> json) =>
    PhysicalReward(
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      description: json['description'] as String?,
      distributionMethod: json['distributionMethod'] as String?,
      id: json['id'] as String?,
      image: json['image'] as String?,
      longImage: json['longImage'] as String?,
      name: json['name'] as String?,
      rewardId: json['rewardId'] as String?,
      sysOrgCode: json['sysOrgCode'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      sponsor: json['sponsor'] as String?,
      isProvided: (json['isProvided'] as num?)?.toInt(),
      rewardDistributionRecordId: json['rewardDistributionRecordId'] as String?,
      prizeClaimDeadline: json['prizeClaimDeadline'] as String?,
      pickupStartTime: json['pickupStartTime'] as String?,
      pickupEndTime: json['pickupEndTime'] as String?,
      postNum: json['postNum'] as String?,
      distributionStatus: (json['distributionStatus'] as num?)?.toInt(),
      slectedDistributionMethod:
          (json['slectedDistributionMethod'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PhysicalRewardToJson(PhysicalReward instance) =>
    <String, dynamic>{
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'description': instance.description,
      'distributionMethod': instance.distributionMethod,
      'id': instance.id,
      'image': instance.image,
      'longImage': instance.longImage,
      'name': instance.name,
      'rewardId': instance.rewardId,
      'sysOrgCode': instance.sysOrgCode,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'sponsor': instance.sponsor,
      'isProvided': instance.isProvided,
      'distributionStatus': instance.distributionStatus,
      'slectedDistributionMethod': instance.slectedDistributionMethod,
      'rewardDistributionRecordId': instance.rewardDistributionRecordId,
      'prizeClaimDeadline': instance.prizeClaimDeadline,
      'pickupStartTime': instance.pickupStartTime,
      'pickupEndTime': instance.pickupEndTime,
      'postNum': instance.postNum,
    };

ThirdPartyReward _$ThirdPartyRewardFromJson(Map<String, dynamic> json) =>
    ThirdPartyReward(
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      description: json['description'] as String?,
      distributionMethod: json['distributionMethod'] as String?,
      id: json['id'] as String?,
      image: json['image'] as String?,
      longImage: json['longImage'] as String?,
      name: json['name'] as String?,
      rewardId: json['rewardId'] as String?,
      sysOrgCode: json['sysOrgCode'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      sponsor: json['sponsor'] as String?,
      isProvided: (json['isProvided'] as num?)?.toInt(),
      thirdPartyName: json['thirdPartyName'] as String?,
      rewardDistributionRecordId: json['rewardDistributionRecordId'] as String?,
      slectedDistributionMethod: json['slectedDistributionMethod'] as String?,
      prizeClaimDeadline: json['prizeClaimDeadline'] as String?,
    );

Map<String, dynamic> _$ThirdPartyRewardToJson(ThirdPartyReward instance) =>
    <String, dynamic>{
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'description': instance.description,
      'distributionMethod': instance.distributionMethod,
      'id': instance.id,
      'image': instance.image,
      'longImage': instance.longImage,
      'name': instance.name,
      'rewardId': instance.rewardId,
      'sysOrgCode': instance.sysOrgCode,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'sponsor': instance.sponsor,
      'isProvided': instance.isProvided,
      'thirdPartyName': instance.thirdPartyName,
      'rewardDistributionRecordId': instance.rewardDistributionRecordId,
      'slectedDistributionMethod': instance.slectedDistributionMethod,
      'prizeClaimDeadline': instance.prizeClaimDeadline,
    };

VirtualReward _$VirtualRewardFromJson(Map<String, dynamic> json) =>
    VirtualReward(
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      id: json['id'] as String?,
      medalId: json['medalId'] as String?,
      rewardId: json['rewardId'] as String?,
      sysOrgCode: json['sysOrgCode'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      sponsor: json['sponsor'] as String?,
      isProvided: (json['isProvided'] as num?)?.toInt(),
      prizeClaimDeadline: json['prizeClaimDeadline'] as String?,
      rewardDistributionRecordId: json['rewardDistributionRecordId'] as String?,
      trophyRecordId: json['trophyRecordId'] as String?,
    )
      ..name = json['name'] as String?
      ..description = json['description'] as String?
      ..image = json['image'] as String?;

Map<String, dynamic> _$VirtualRewardToJson(VirtualReward instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'image': instance.image,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'id': instance.id,
      'medalId': instance.medalId,
      'rewardId': instance.rewardId,
      'sysOrgCode': instance.sysOrgCode,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'sponsor': instance.sponsor,
      'isProvided': instance.isProvided,
      'prizeClaimDeadline': instance.prizeClaimDeadline,
      'rewardDistributionRecordId': instance.rewardDistributionRecordId,
      'trophyRecordId': instance.trophyRecordId,
    };

BlankReward _$BlankRewardFromJson(Map<String, dynamic> json) => BlankReward(
      id: json['id'] as String?,
      name: json['name'] as String?,
      image: json['image'] as String?,
      rewardId: json['rewardId'] as String?,
    );

Map<String, dynamic> _$BlankRewardToJson(BlankReward instance) =>
    <String, dynamic>{
      'name': instance.name,
      'image': instance.image,
      'id': instance.id,
      'rewardId': instance.rewardId,
    };

OtherReward _$OtherRewardFromJson(Map<String, dynamic> json) => OtherReward(
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      id: json['id'] as String?,
      rewardId: json['rewardId'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      sponsor: json['sponsor'] as String?,
      isProvided: (json['isProvided'] as num?)?.toInt(),
      prizeClaimDeadline: json['prizeClaimDeadline'] as String?,
      rewardDistributionRecordId: json['rewardDistributionRecordId'] as String?,
    )
      ..name = json['name'] as String?
      ..description = json['description'] as String?
      ..image = json['image'] as String?;

Map<String, dynamic> _$OtherRewardToJson(OtherReward instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'image': instance.image,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'id': instance.id,
      'rewardId': instance.rewardId,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'sponsor': instance.sponsor,
      'isProvided': instance.isProvided,
      'prizeClaimDeadline': instance.prizeClaimDeadline,
      'rewardDistributionRecordId': instance.rewardDistributionRecordId,
    };
