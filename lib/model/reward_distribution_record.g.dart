// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reward_distribution_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RewardDistributionRecord _$RewardDistributionRecordFromJson(
        Map<String, dynamic> json) =>
    RewardDistributionRecord(
      address: json['address'] as String?,
      city: json['city'] as String?,
      distributionMethod: (json['distributionMethod'] as num?)?.toInt(),
      mobile: json['mobile'] as String?,
      name: json['name'] as String?,
      nickname: json['nickname'] as String?,
      phone: json['phone'] as String?,
      postCode: json['postCode'] as String?,
      prizeClaimFormId: json['prizeClaimFormId'] as String?,
      province: json['province'] as String?,
      remark: json['remark'] as String?,
      shopId: json['shopId'] as String?,
      shoppingName: json['shoppingName'] as String?,
      thirdPartyAccount: json['thirdPartyAccount'] as String?,
      town: json['town'] as String?,
      userMobile: json['userMobile'] as String?,
      userShoppingAddressId: (json['userShoppingAddressId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$RewardDistributionRecordToJson(
        RewardDistributionRecord instance) =>
    <String, dynamic>{
      'address': instance.address,
      'city': instance.city,
      'distributionMethod': instance.distributionMethod,
      'mobile': instance.mobile,
      'name': instance.name,
      'nickname': instance.nickname,
      'phone': instance.phone,
      'postCode': instance.postCode,
      'prizeClaimFormId': instance.prizeClaimFormId,
      'province': instance.province,
      'remark': instance.remark,
      'shopId': instance.shopId,
      'shoppingName': instance.shoppingName,
      'thirdPartyAccount': instance.thirdPartyAccount,
      'town': instance.town,
      'userMobile': instance.userMobile,
      'userShoppingAddressId': instance.userShoppingAddressId,
    };
