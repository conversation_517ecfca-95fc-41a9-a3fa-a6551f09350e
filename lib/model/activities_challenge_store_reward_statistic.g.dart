// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activities_challenge_store_reward_statistic.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ActivitiesChallengeStoreRewardStatistic
    _$ActivitiesChallengeStoreRewardStatisticFromJson(
            Map<String, dynamic> json) =>
        ActivitiesChallengeStoreRewardStatistic(
          shopInfo: json['shopInfo'] == null
              ? null
              : ShopInfo.fromJson(json['shopInfo'] as Map<String, dynamic>),
          physicalRewardStatisticVoList:
              (json['physicalRewardStatisticVoList'] as List<dynamic>?)
                  ?.map((e) => PhysicalRewardStatisticVoList.fromJson(
                      e as Map<String, dynamic>))
                  .toList(),
          luckyDrawaPhysicalRewardStatisticVoList:
              (json['luckyDrawaPhysicalRewardStatisticVoList']
                      as List<dynamic>?)
                  ?.map((e) => PhysicalRewardStatisticVoList.fromJson(
                      e as Map<String, dynamic>))
                  .toList(),
        );

Map<String, dynamic> _$ActivitiesChallengeStoreRewardStatisticToJson(
        ActivitiesChallengeStoreRewardStatistic instance) =>
    <String, dynamic>{
      'shopInfo': instance.shopInfo,
      'physicalRewardStatisticVoList': instance.physicalRewardStatisticVoList,
      'luckyDrawaPhysicalRewardStatisticVoList':
          instance.luckyDrawaPhysicalRewardStatisticVoList,
    };

PhysicalRewardStatisticVoList _$PhysicalRewardStatisticVoListFromJson(
        Map<String, dynamic> json) =>
    PhysicalRewardStatisticVoList(
      rewardId: json['rewardId'] as String?,
      name: json['name'] as String?,
      image: json['image'] as String?,
      description: json['description'] as String?,
      prizeClaimDeadline: json['prizeClaimDeadline'] as String?,
      totalCount: (json['totalCount'] as num?)?.toInt(),
      distributedCount: (json['distributedCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PhysicalRewardStatisticVoListToJson(
        PhysicalRewardStatisticVoList instance) =>
    <String, dynamic>{
      'rewardId': instance.rewardId,
      'name': instance.name,
      'image': instance.image,
      'description': instance.description,
      'prizeClaimDeadline': instance.prizeClaimDeadline,
      'totalCount': instance.totalCount,
      'distributedCount': instance.distributedCount,
    };
