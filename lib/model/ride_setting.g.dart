// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ride_setting.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RideSetting _$RideSettingFromJson(Map<String, dynamic> json) => RideSetting(
      autoPause: json['autoPause'] as bool?,
      audioTip: json['audioTip'] as bool?,
      screenAlwaysOn: json['screenAlwaysOn'] as bool?,
    );

Map<String, dynamic> _$RideSettingToJson(RideSetting instance) =>
    <String, dynamic>{
      'autoPause': instance.autoPause,
      'audioTip': instance.audioTip,
      'screenAlwaysOn': instance.screenAlwaysOn,
    };
