// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'route_rank_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RouteRankInfo _$RouteRankInfoFromJson(Map<String, dynamic> json) =>
    RouteRankInfo(
      id: (json['id'] as num?)?.toInt(),
      uid: (json['uid'] as num?)?.toInt(),
      totalMeter: (json['totalMeter'] as num?)?.toInt(),
      totalSecond: (json['totalSecond'] as num?)?.toInt(),
      totalTime: (json['totalTime'] as num?)?.toInt(),
      totalClimb: (json['totalClimb'] as num?)?.toInt(),
      gender: (json['gender'] as num?)?.toInt(),
      challengeTime: (json['challengeTime'] as num?)?.toInt(),
      bestSecond: (json['bestSecond'] as num?)?.toInt(),
      nick: json['nick'] as String?,
      faceUrl: json['faceUrl'] as String?,
      bikeInfo: json['bikeInfo'] as String?,
      kolStatus: (json['kolStatus'] as num?)?.toInt(),
    );

Map<String, dynamic> _$RouteRankInfoToJson(RouteRankInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'totalMeter': instance.totalMeter,
      'totalSecond': instance.totalSecond,
      'totalTime': instance.totalTime,
      'totalClimb': instance.totalClimb,
      'gender': instance.gender,
      'challengeTime': instance.challengeTime,
      'bestSecond': instance.bestSecond,
      'nick': instance.nick,
      'faceUrl': instance.faceUrl,
      'bikeInfo': instance.bikeInfo,
      'kolStatus': instance.kolStatus,
    };
