// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeInfo _$HomeInfoFromJson(Map<String, dynamic> json) => HomeInfo(
      clerk: json['clerk'] as bool?,
      userInfo: json['userInfo'] == null
          ? null
          : Account.fromJson(json['userInfo'] as Map<String, dynamic>),
      bikeInfo: json['bikeInfo'] == null
          ? null
          : Bike.fromJson(json['bikeInfo'] as Map<String, dynamic>),
      rideStatInfo: json['rideStatInfo'] == null
          ? null
          : RideStatInfo.fromJson(json['rideStatInfo'] as Map<String, dynamic>),
      rideMonthStatInfo: json['rideMonthStatInfo'] == null
          ? null
          : RideMonthStatInfo.fromJson(
              json['rideMonthStatInfo'] as Map<String, dynamic>),
      dayRideMeterList: (json['dayRideMeterList'] as List<dynamic>?)
          ?.map((e) => DayRideMeterList.fromJson(e as Map<String, dynamic>))
          .toList(),
    )..feedNum = (json['feedNum'] as num?)?.toInt();

Map<String, dynamic> _$HomeInfoToJson(HomeInfo instance) => <String, dynamic>{
      'feedNum': instance.feedNum,
      'clerk': instance.clerk,
      'userInfo': instance.userInfo,
      'bikeInfo': instance.bikeInfo,
      'rideStatInfo': instance.rideStatInfo,
      'rideMonthStatInfo': instance.rideMonthStatInfo,
      'dayRideMeterList': instance.dayRideMeterList,
    };

DayRideMeterList _$DayRideMeterListFromJson(Map<String, dynamic> json) =>
    DayRideMeterList(
      dayMeter: (json['dayMeter'] as num?)?.toInt(),
      dayTime: json['dayTime'] as String?,
    );

Map<String, dynamic> _$DayRideMeterListToJson(DayRideMeterList instance) =>
    <String, dynamic>{
      'dayMeter': instance.dayMeter,
      'dayTime': instance.dayTime,
    };
