// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activities_event.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ActivitiesEvent _$ActivitiesEventFromJson(Map<String, dynamic> json) =>
    ActivitiesEvent(
      id: json['id'] as String?,
      title: json['title'] as String?,
      titleEn: json['titleEn'] as String?,
      location: json['location'] as String?,
      coverImage: json['coverImage'] as String?,
      wxMiniShareImg: json['wxMiniShareImg'] as String?,
      price: json['price'] as num?,
      gpsPosition: json['gpsPosition'] as String?,
      mileage: json['mileage'] as num?,
      mileageUnit: json['mileageUnit'] as String?,
      altitude: json['altitude'] as num?,
      altitudeUnit: json['altitudeUnit'] as String?,
      avgSpeed: json['avgSpeed'] as String?,
      avgSpeedUnit: json['avgSpeedUnit'] as String?,
      estimatedTime: json['estimatedTime'] as String?,
      estimatedTimeUnit: json['estimatedTimeUnit'] as String?,
      earlyBirdEndTime: json['earlyBirdEndTime'] as String?,
      earlyBirdPrice: json['earlyBirdPrice'] as num?,
      leaderId: json['leaderId'] as num?,
      leaderName: json['leaderName'] as String?,
      insuranceId: json['insuranceId'] as String?,
      insuranceStartDate: json['insuranceStartDate'] as String?,
      insuranceDays: json['insuranceDays'] as num?,
      insurancePrice: json['insurancePrice'] as num?,
      status: json['status'] as String?,
      services: json['services'] as String?,
      servicesImg: json['servicesImg'] as String?,
      servicesList: (json['servicesList'] as List<dynamic>?)
          ?.map((e) => ActivitiesService.fromJson(e as Map<String, dynamic>))
          .toList(),
      stagesRouteList: (json['stagesRouteList'] as List<dynamic>?)
          ?.map(
              (e) => ActivitiesStagesRoute.fromJson(e as Map<String, dynamic>))
          .toList(),
      leaderList: (json['leaderList'] as List<dynamic>?)
          ?.map((e) => ActivitiesLeader.fromJson(e as Map<String, dynamic>))
          .toList(),
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      registrationStartTime: json['registrationStartTime'] as String?,
      registrationEndTime: json['registrationEndTime'] as String?,
      registeredUserHeadUrls: (json['registeredUserHeadUrls'] as List<dynamic>?)
          ?.map((e) => e as String?)
          .toList(),
      richTextDescription: json['richTextDescription'] as String?,
      richTextLiabilityWaiver: json['richTextLiabilityWaiver'] as String?,
      description: json['description'] as String?,
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      joined: json['joined'] as bool?,
    );

Map<String, dynamic> _$ActivitiesEventToJson(ActivitiesEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'titleEn': instance.titleEn,
      'location': instance.location,
      'coverImage': instance.coverImage,
      'wxMiniShareImg': instance.wxMiniShareImg,
      'price': instance.price,
      'mileage': instance.mileage,
      'mileageUnit': instance.mileageUnit,
      'altitude': instance.altitude,
      'altitudeUnit': instance.altitudeUnit,
      'avgSpeed': instance.avgSpeed,
      'avgSpeedUnit': instance.avgSpeedUnit,
      'estimatedTime': instance.estimatedTime,
      'estimatedTimeUnit': instance.estimatedTimeUnit,
      'earlyBirdEndTime': instance.earlyBirdEndTime,
      'earlyBirdPrice': instance.earlyBirdPrice,
      'leaderId': instance.leaderId,
      'leaderName': instance.leaderName,
      'insuranceId': instance.insuranceId,
      'insuranceStartDate': instance.insuranceStartDate,
      'insuranceDays': instance.insuranceDays,
      'insurancePrice': instance.insurancePrice,
      'status': instance.status,
      'services': instance.services,
      'servicesImg': instance.servicesImg,
      'servicesList': instance.servicesList,
      'stagesRouteList': instance.stagesRouteList,
      'leaderList': instance.leaderList,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'gpsPosition': instance.gpsPosition,
      'registrationStartTime': instance.registrationStartTime,
      'registrationEndTime': instance.registrationEndTime,
      'richTextDescription': instance.richTextDescription,
      'richTextLiabilityWaiver': instance.richTextLiabilityWaiver,
      'description': instance.description,
      'registeredUserHeadUrls': instance.registeredUserHeadUrls,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'joined': instance.joined,
    };

ActivitiesService _$ActivitiesServiceFromJson(Map<String, dynamic> json) =>
    ActivitiesService(
      id: json['id'] as String?,
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
    );

Map<String, dynamic> _$ActivitiesServiceToJson(ActivitiesService instance) =>
    <String, dynamic>{
      'id': instance.id,
      'icon': instance.icon,
      'name': instance.name,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
    };

ActivitiesStagesRoute _$ActivitiesStagesRouteFromJson(
        Map<String, dynamic> json) =>
    ActivitiesStagesRoute(
      id: json['id'] as String?,
      eventId: json['eventId'] as String?,
      coverImg: json['coverImg'] as String?,
      squareCoverImg: json['squareCoverImg'] as String?,
      lightCoverImg: json['lightCoverImg'] as String?,
      lightSquareCoverImg: json['lightSquareCoverImg'] as String?,
    );

Map<String, dynamic> _$ActivitiesStagesRouteToJson(
        ActivitiesStagesRoute instance) =>
    <String, dynamic>{
      'id': instance.id,
      'eventId': instance.eventId,
      'coverImg': instance.coverImg,
      'squareCoverImg': instance.squareCoverImg,
      'lightCoverImg': instance.lightCoverImg,
      'lightSquareCoverImg': instance.lightSquareCoverImg,
    };

ActivitiesLeader _$ActivitiesLeaderFromJson(Map<String, dynamic> json) =>
    ActivitiesLeader(
      id: json['id'] as String?,
      uid: (json['uid'] as num?)?.toInt(),
      eventId: json['eventId'] as String?,
      faceUrl: json['faceUrl'] as String?,
      isFriend: (json['isFriend'] as num?)?.toInt(),
      kolStatus: (json['kolStatus'] as num?)?.toInt(),
      mobile: json['mobile'] as String?,
      nick: json['nick'] as String?,
    );

Map<String, dynamic> _$ActivitiesLeaderToJson(ActivitiesLeader instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'eventId': instance.eventId,
      'faceUrl': instance.faceUrl,
      'isFriend': instance.isFriend,
      'kolStatus': instance.kolStatus,
      'mobile': instance.mobile,
      'nick': instance.nick,
    };
