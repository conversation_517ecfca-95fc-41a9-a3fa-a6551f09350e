import 'package:json_annotation/json_annotation.dart';

part 'video_system.g.dart';

@JsonSerializable()
class VideoSystem {
  String? compressed;
  String? coverImg;
  String? createBy;
  String? createTime;
  String? groupId;
  String? id;
  String? original;
  String? originalMd5;
  String? pointPositionId;
  String? raceId;
  int? type;
  String? updateBy;
  String? updateTime;

  VideoSystem({
     this.compressed,
     this.coverImg,
     this.createBy,
     this.createTime,
     this.groupId,
     this. id,
     this. original,
     this.originalMd5,
     this. pointPositionId,
     this.raceId,
     this.type,
     this. updateBy,
     this.updateTime}) {
  }
  factory VideoSystem.fromJson(Map<String, dynamic> json) => _$VideoSystemFromJson(json);

  Map<String, dynamic> toJson() => _$VideoSystemToJson(this);

}