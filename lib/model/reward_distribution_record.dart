import 'package:json_annotation/json_annotation.dart';

part 'reward_distribution_record.g.dart';

@JsonSerializable()
class RewardDistributionRecord {
  final String? address;
  final String? city;
  final int? distributionMethod;
  final String? mobile;
  final String? name;
  final String? nickname;
  final String? phone;
  final String? postCode;
  final String? prizeClaimFormId;
  final String? province;
  final String? remark;
  final String? shopId;
  final String? shoppingName;
  final String? thirdPartyAccount;
  final String? town;
  final String? userMobile;
  final int? userShoppingAddressId;

  const RewardDistributionRecord({
    this.address,
    this.city,
    this.distributionMethod,
    this.mobile,
    this.name,
    this.nickname,
    this.phone,
    this.postCode,
    this.prizeClaimFormId,
    this.province,
    this.remark,
    this.shopId,
    this.shoppingName,
    this.thirdPartyAccount,
    this.town,
    this.userMobile,
    this.userShoppingAddressId,
  });

  factory RewardDistributionRecord.fromJson(Map<String, dynamic> json) =>
      _$RewardDistributionRecordFromJson(json);

  Map<String, dynamic> toJson() => _$RewardDistributionRecordToJson(this);
}
