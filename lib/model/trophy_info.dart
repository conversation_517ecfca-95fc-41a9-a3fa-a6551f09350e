import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:new_edge/channels/common_channel.dart';
import 'package:new_edge/model/bike.dart';
import 'package:new_edge/util/image_utils.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/push.dart';
import 'package:new_edge/util/settings.dart';
import 'package:new_edge/pages/common/developer_tools.dart';
import 'package:new_edge/util/sp_utils.dart';
import 'package:json_annotation/json_annotation.dart';
import '../app.dart';
import 'ride_data_type.dart';
import 'user.dart';

part 'trophy_info.g.dart';

@JsonSerializable()
class TrophyInfo extends User {
  List<Trophy>? trophyList;
  int? year;

  TrophyInfo({
    this.trophyList,
    this.year,
  });

  factory TrophyInfo.fromJson(Map<String, dynamic> json) =>
      _$TrophyInfoFromJson(json);

  Map<String, dynamic> toJson() => _$TrophyInfoToJson(this);
}

@JsonSerializable()
class Trophy {
  String? shortTitle;
  String? thumb;
  String? trophyRecordId;

  Trophy({this.shortTitle, this.thumb, this.trophyRecordId});

  factory Trophy.fromJson(Map<String, dynamic> json) => _$TrophyFromJson(json);

  Map<String, dynamic> toJson() => _$TrophyToJson(this);
}

@JsonSerializable()
class TrophyDetail {
  String? title;
  String? image;
  String? time;
  String? description;
  int? activityType;
  String? activityId;

  TrophyDetail(
      {this.title,
      this.image,
      this.time,
      this.description,
      this.activityType,
      this.activityId});

  factory TrophyDetail.fromJson(Map<String, dynamic> json) => _$TrophyDetailFromJson(json);

  Map<String, dynamic> toJson() => _$TrophyDetailToJson(this);
}
