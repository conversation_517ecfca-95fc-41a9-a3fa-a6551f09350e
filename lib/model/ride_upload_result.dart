import 'package:json_annotation/json_annotation.dart';

part 'ride_upload_result.g.dart';

@JsonSerializable()
class RideIploadResult {
  int? postRideId;
  int? status;
  int? subStatus;

  RideIploadResult({this.postRideId, this.status, this.subStatus});

  factory RideIploadResult.fromJson(Map<String, dynamic> json) =>
      _$RideIploadResultFromJson(json);

  Map<String, dynamic> toJson() => _$RideIploadResultToJson(this);
}
