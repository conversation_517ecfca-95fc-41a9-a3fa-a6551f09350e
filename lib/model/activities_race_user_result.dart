import 'package:json_annotation/json_annotation.dart';

part 'activities_race_user_result.g.dart';

@JsonSerializable()
class ActivityRaceUserResult {
  int? ageGroupRank;
  String? brandName;
  String? configuration;
  String? coverImg;
  String? faceUrl;
  int? genderRank;
  String? groupId;
  String? modelYear;
  String? nick;
  String? raceId;
  int? rank;
  int? resultValue;
  String? status;
  String? seriesName;
  int? uid;
  String? userBikeName;
  String? certificateImg;
  String? prizeImgUrl;

  ActivityRaceUserResult(
      {this.ageGroupRank,
      this.brandName,
      this.configuration,
      this.coverImg,
      this.faceUrl,
      this.genderRank,
      this.groupId,
      this.modelYear,
      this.nick,
      this.raceId,
      this.rank,
      this.resultValue,
      this.status,
      this.seriesName,
      this.uid,
      this.userBikeName,
      this.certificateImg,
      this.prizeImgUrl});

  factory ActivityRaceUserResult.fromJson(Map<String, dynamic> json) =>
      _$ActivityRaceUserResultFromJson(json);

  Map<String, dynamic> toJson() => _$ActivityRaceUserResultToJson(this);
}
