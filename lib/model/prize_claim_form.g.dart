// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'prize_claim_form.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PrizeClaimForm _$PrizeClaimFormFromJson(Map<String, dynamic> json) =>
    PrizeClaimForm(
      awardId: json['awardId'] as String?,
      awardState: (json['awardState'] as num?)?.toInt(),
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      id: json['id'] as String?,
      infoVirtualVo: json['infoVirtualVo'] == null
          ? null
          : InfoVirtualVo.fromJson(
              json['infoVirtualVo'] as Map<String, dynamic>),
      isDeleted: (json['isDeleted'] as num?)?.toInt(),
      mailVo: json['mailVo'] == null
          ? null
          : MailVo.fromJson(json['mailVo'] as Map<String, dynamic>),
      placeType: json['placeType'] as String?,
      placeTypeId: json['placeTypeId'] as String?,
      prizeType: (json['prizeType'] as num?)?.toInt(),
      storeVo: json['storeVo'] == null
          ? null
          : StoreVo.fromJson(json['storeVo'] as Map<String, dynamic>),
      tenantId: (json['tenantId'] as num?)?.toInt(),
      uid: (json['uid'] as num?)?.toInt(),
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
    );

Map<String, dynamic> _$PrizeClaimFormToJson(PrizeClaimForm instance) =>
    <String, dynamic>{
      'awardId': instance.awardId,
      'awardState': instance.awardState,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'id': instance.id,
      'infoVirtualVo': instance.infoVirtualVo,
      'isDeleted': instance.isDeleted,
      'mailVo': instance.mailVo,
      'placeType': instance.placeType,
      'placeTypeId': instance.placeTypeId,
      'prizeType': instance.prizeType,
      'storeVo': instance.storeVo,
      'tenantId': instance.tenantId,
      'uid': instance.uid,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
    };

InfoVirtualVo _$InfoVirtualVoFromJson(Map<String, dynamic> json) =>
    InfoVirtualVo(
      distributionStatus: (json['distributionStatus'] as num?)?.toInt(),
      distributionTime: json['distributionTime'] as String?,
      id: json['id'] as String?,
      name: json['name'] as String?,
      nickname: json['nickname'] as String?,
      phone: json['phone'] as String?,
      prizeClaimFormId: json['prizeClaimFormId'] as String?,
      remark: json['remark'] as String?,
      thirdPartyAccount: json['thirdPartyAccount'] as String?,
      userMobile: json['userMobile'] as String?,
    );

Map<String, dynamic> _$InfoVirtualVoToJson(InfoVirtualVo instance) =>
    <String, dynamic>{
      'distributionStatus': instance.distributionStatus,
      'distributionTime': instance.distributionTime,
      'id': instance.id,
      'name': instance.name,
      'nickname': instance.nickname,
      'phone': instance.phone,
      'prizeClaimFormId': instance.prizeClaimFormId,
      'remark': instance.remark,
      'thirdPartyAccount': instance.thirdPartyAccount,
      'userMobile': instance.userMobile,
    };

MailVo _$MailVoFromJson(Map<String, dynamic> json) => MailVo(
      address: json['address'] as String?,
      city: json['city'] as String?,
      distributionStatus: (json['distributionStatus'] as num?)?.toInt(),
      distributionTime: json['distributionTime'] as String?,
      id: json['id'] as String?,
      mobile: json['mobile'] as String?,
      nickname: json['nickname'] as String?,
      postCode: json['postCode'] as String?,
      postCompany: json['postCompany'] as String?,
      postCompanyCode: json['postCompanyCode'] as String?,
      postNum: json['postNum'] as String?,
      prizeClaimFormId: json['prizeClaimFormId'] as String?,
      province: json['province'] as String?,
      remark: json['remark'] as String?,
      shoppingName: json['shoppingName'] as String?,
      town: json['town'] as String?,
      userMobile: json['userMobile'] as String?,
    );

Map<String, dynamic> _$MailVoToJson(MailVo instance) => <String, dynamic>{
      'address': instance.address,
      'city': instance.city,
      'distributionStatus': instance.distributionStatus,
      'distributionTime': instance.distributionTime,
      'id': instance.id,
      'mobile': instance.mobile,
      'nickname': instance.nickname,
      'postCode': instance.postCode,
      'postCompany': instance.postCompany,
      'postCompanyCode': instance.postCompanyCode,
      'postNum': instance.postNum,
      'prizeClaimFormId': instance.prizeClaimFormId,
      'province': instance.province,
      'remark': instance.remark,
      'shoppingName': instance.shoppingName,
      'town': instance.town,
      'userMobile': instance.userMobile,
    };

StoreVo _$StoreVoFromJson(Map<String, dynamic> json) => StoreVo(
      distributionFaceUrl: json['distributionFaceUrl'] as String?,
      distributionNick: json['distributionNick'] as String?,
      distributionStatus: (json['distributionStatus'] as num?)?.toInt(),
      distributionTime: json['distributionTime'] as String?,
      distributionUid: json['distributionUid'] as String?,
      fansTotal: (json['fansTotal'] as num?)?.toInt(),
      getPrizeQR: json['getPrizeQR'] as String?,
      id: json['id'] as String?,
      isFriend: (json['isFriend'] as num?)?.toInt(),
      nickname: json['nickname'] as String?,
      prizeClaimFormId: json['prizeClaimFormId'] as String?,
      remark: json['remark'] as String?,
      shopId: json['shopId'] as String?,
      shopInfo: json['shopInfo'] == null
          ? null
          : ShopInfo.fromJson(json['shopInfo'] as Map<String, dynamic>),
      userMobile: json['userMobile'] as String?,
    );

Map<String, dynamic> _$StoreVoToJson(StoreVo instance) => <String, dynamic>{
      'distributionFaceUrl': instance.distributionFaceUrl,
      'distributionNick': instance.distributionNick,
      'distributionStatus': instance.distributionStatus,
      'distributionTime': instance.distributionTime,
      'distributionUid': instance.distributionUid,
      'fansTotal': instance.fansTotal,
      'getPrizeQR': instance.getPrizeQR,
      'id': instance.id,
      'isFriend': instance.isFriend,
      'nickname': instance.nickname,
      'prizeClaimFormId': instance.prizeClaimFormId,
      'remark': instance.remark,
      'shopId': instance.shopId,
      'shopInfo': instance.shopInfo,
      'userMobile': instance.userMobile,
    };

ShopInfo _$ShopInfoFromJson(Map<String, dynamic> json) => ShopInfo(
      address: json['address'] as String?,
      businessCloseTime: json['businessCloseTime'] == null
          ? null
          : BusinessTime.fromJson(
              json['businessCloseTime'] as Map<String, dynamic>),
      businessOpenTime: json['businessOpenTime'] == null
          ? null
          : BusinessTime.fromJson(
              json['businessOpenTime'] as Map<String, dynamic>),
      cityname: json['cityname'] as String?,
      coverImageUrl: json['coverImageUrl'] as String?,
      detailContent: json['detailContent'] as String?,
      districtStartPoint: (json['districtStartPoint'] as num?)?.toInt(),
      id: json['id'] as String?,
      latLon: json['latLon'] as String?,
      name: json['name'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
      status: json['status'] as String?,
      telephone: (json['telephone'] as num?)?.toInt(),
      tenantId: (json['tenantId'] as num?)?.toInt(),
      type: json['type'] as String?,
    );

Map<String, dynamic> _$ShopInfoToJson(ShopInfo instance) => <String, dynamic>{
      'address': instance.address,
      'businessCloseTime': instance.businessCloseTime,
      'businessOpenTime': instance.businessOpenTime,
      'cityname': instance.cityname,
      'coverImageUrl': instance.coverImageUrl,
      'detailContent': instance.detailContent,
      'districtStartPoint': instance.districtStartPoint,
      'id': instance.id,
      'latLon': instance.latLon,
      'name': instance.name,
      'sort': instance.sort,
      'status': instance.status,
      'telephone': instance.telephone,
      'tenantId': instance.tenantId,
      'type': instance.type,
    };

BusinessTime _$BusinessTimeFromJson(Map<String, dynamic> json) => BusinessTime(
      hour: (json['hour'] as num?)?.toInt(),
      minute: (json['minute'] as num?)?.toInt(),
      nano: (json['nano'] as num?)?.toInt(),
      second: (json['second'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BusinessTimeToJson(BusinessTime instance) =>
    <String, dynamic>{
      'hour': instance.hour,
      'minute': instance.minute,
      'nano': instance.nano,
      'second': instance.second,
    };
