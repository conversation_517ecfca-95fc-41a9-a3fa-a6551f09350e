import 'package:json_annotation/json_annotation.dart';

part 'prize_claim_form.g.dart';

extension BusinessTimeExtension on BusinessTime {
  String get formattedTime =>
      '${hour.toString().padLeft(
          2, '0')}:${minute.toString().padLeft(2, '0')}}';
}

@JsonSerializable()
class PrizeClaimForm {
  final String? awardId;
  final int? awardState;
  final String? createBy;
  final String? createTime;
  final String? id;
  final InfoVirtualVo? infoVirtualVo;
  final int? isDeleted;
  final MailVo? mailVo;
  final String? placeType;
  final String? placeTypeId;
  final int? prizeType;
  final StoreVo? storeVo;
  final int? tenantId;
  final int? uid;
  final String? updateBy;
  final String? updateTime;

  const PrizeClaimForm({
    this.awardId,
    this.awardState,
    this.createBy,
    this.createTime,
    this.id,
    this.infoVirtualVo,
    this.isDeleted,
    this.mailVo,
    this.placeType,
    this.placeTypeId,
    this.prizeType,
    this.storeVo,
    this.tenantId,
    this.uid,
    this.updateBy,
    this.updateTime,
  });

  factory PrizeClaimForm.fromJson(Map<String, dynamic> json) =>
      _$PrizeClaimFormFromJson(json);

  Map<String, dynamic> toJson() => _$PrizeClaimFormToJson(this);
}

@JsonSerializable()
class InfoVirtualVo {
  final int? distributionStatus;
  final String? distributionTime;
  final String? id;
  final String? name;
  final String? nickname;
  final String? phone;
  final String? prizeClaimFormId;
  final String? remark;
  final String? thirdPartyAccount;
  final String? userMobile;

  const InfoVirtualVo({
    this.distributionStatus,
    this.distributionTime,
    this.id,
    this.name,
    this.nickname,
    this.phone,
    this.prizeClaimFormId,
    this.remark,
    this.thirdPartyAccount,
    this.userMobile,
  });

  factory InfoVirtualVo.fromJson(Map<String, dynamic> json) =>
      _$InfoVirtualVoFromJson(json);

  Map<String, dynamic> toJson() => _$InfoVirtualVoToJson(this);
}

@JsonSerializable()
class MailVo {
  final String? address;
  final String? city;
  final int? distributionStatus;
  final String? distributionTime;
  final String? id;
  final String? mobile;
  final String? nickname;
  final String? postCode;
  final String? postCompany;
  final String? postCompanyCode;
  final String? postNum;
  final String? prizeClaimFormId;
  final String? province;
  final String? remark;
  final String? shoppingName;
  final String? town;
  final String? userMobile;

  const MailVo({
    this.address,
    this.city,
    this.distributionStatus,
    this.distributionTime,
    this.id,
    this.mobile,
    this.nickname,
    this.postCode,
    this.postCompany,
    this.postCompanyCode,
    this.postNum,
    this.prizeClaimFormId,
    this.province,
    this.remark,
    this.shoppingName,
    this.town,
    this.userMobile,
  });

  factory MailVo.fromJson(Map<String, dynamic> json) =>
      _$MailVoFromJson(json);

  Map<String, dynamic> toJson() => _$MailVoToJson(this);
}

@JsonSerializable()
class StoreVo {
  final String? distributionFaceUrl;
  final String? distributionNick;
  final int? distributionStatus;
  final String? distributionTime;
  final String? distributionUid;
  final int? fansTotal;
  final String? getPrizeQR;
  final String? id;
  final int? isFriend;
  final String? nickname;
  final String? prizeClaimFormId;
  final String? remark;
  final String? shopId;
  final ShopInfo? shopInfo;
  final String? userMobile;

  const StoreVo({
    this.distributionFaceUrl,
    this.distributionNick,
    this.distributionStatus,
    this.distributionTime,
    this.distributionUid,
    this.fansTotal,
    this.getPrizeQR,
    this.id,
    this.isFriend,
    this.nickname,
    this.prizeClaimFormId,
    this.remark,
    this.shopId,
    this.shopInfo,
    this.userMobile,
  });

  factory StoreVo.fromJson(Map<String, dynamic> json) =>
      _$StoreVoFromJson(json);

  Map<String, dynamic> toJson() => _$StoreVoToJson(this);
}

@JsonSerializable()
class ShopInfo {
  final String? address;
  final BusinessTime? businessCloseTime;
  final BusinessTime? businessOpenTime;
  final String? cityname;
  final String? coverImageUrl;
  final String? detailContent;
  final int? districtStartPoint;
  final String? id;
  final String? latLon;
  final String? name;
  final int? sort;
  final String? status;
  final int? telephone;
  final int? tenantId;
  final String? type;

  const ShopInfo({
    this.address,
    this.businessCloseTime,
    this.businessOpenTime,
    this.cityname,
    this.coverImageUrl,
    this.detailContent,
    this.districtStartPoint,
    this.id,
    this.latLon,
    this.name,
    this.sort,
    this.status,
    this.telephone,
    this.tenantId,
    this.type,
  });

  factory ShopInfo.fromJson(Map<String, dynamic> json) =>
      _$ShopInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ShopInfoToJson(this);
}

@JsonSerializable()
class BusinessTime {
  final int? hour;
  final int? minute;
  final int? nano;
  final int? second;

  const BusinessTime({
    this.hour,
    this.minute,
    this.nano,
    this.second,
  });

  factory BusinessTime.fromJson(Map<String, dynamic> json) =>
      _$BusinessTimeFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessTimeToJson(this);
}
