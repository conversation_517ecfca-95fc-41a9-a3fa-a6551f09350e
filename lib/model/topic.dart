class TopIc {
  int? topicId;
  String? topicName;

  TopIc({this.topicId,this.topicName});

  TopIc.fromJson(Map<String, dynamic> json) {
    topicId = json['topicId'];
    topicName = json['topicName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['topicId'] = this.topicId;
    data['topicName'] = this.topicName;
    return data;
  }
}
