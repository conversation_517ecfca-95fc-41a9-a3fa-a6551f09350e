import 'package:json_annotation/json_annotation.dart';

part 'activities_race_order.g.dart';

@JsonSerializable()
class ActivitiesRaceOrder{

  String? image;
  String? name;
  String? mobile;
  num? paymentAmount;
  String? postCompany;
  String? postCompanyCode;
  String? postNum;
  num? price;
  int? quantity;
  String? skuCode;

  ActivitiesRaceOrder({
    this.image,
    this.name,
    this.mobile,
    this.paymentAmount,
    this.postCompany,
    this.postCompanyCode,
    this.postNum,
    this.price,
    this.quantity,
    this.skuCode,
  }) {}

  factory ActivitiesRaceOrder.fromJson(Map<String, dynamic> json) =>
      _$ActivitiesRaceOrderFromJson(json);

  Map<String, dynamic> toJson() => _$ActivitiesRaceOrderToJson(this);
}
