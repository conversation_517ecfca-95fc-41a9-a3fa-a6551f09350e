// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_response_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApiResponse<T> _$ApiResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    ApiResponse<T>()
      ..code = (json['code'] as num?)?.toInt()
      ..message = json['message'] as String?
      ..result = _$nullableGenericFromJson(json['result'], fromJsonT)
      ..success = json['success'] as bool?
      ..timestamp = (json['timestamp'] as num?)?.toInt();

Map<String, dynamic> _$ApiResponseToJson<T>(
  ApiResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'result': _$nullableGenericToJson(instance.result, toJsonT),
      'success': instance.success,
      'timestamp': instance.timestamp,
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);
