// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SearchInfo _$SearchInfoFromJson(Map<String, dynamic> json) => SearchInfo(
      faceUrl: json['faceUrl'] as String?,
      kolStatus: json['kolStatus'] as num?,
      toUid: json['toUid'] as num?,
      nick: json['nick'] as String?,
      city: json['city'] as String?,
      totalMeter: json['totalMeter'] as num?,
      isFriend: json['isFriend'] as num?,
      fans: json['fans'] as num?,
      follow: json['follow'] as num?,
    );

Map<String, dynamic> _$SearchInfoToJson(SearchInfo instance) =>
    <String, dynamic>{
      'faceUrl': instance.faceUrl,
      'kolStatus': instance.kolStatus,
      'toUid': instance.toUid,
      'nick': instance.nick,
      'city': instance.city,
      'totalMeter': instance.totalMeter,
      'isFriend': instance.isFriend,
      'fans': instance.fans,
      'follow': instance.follow,
    };
