import 'package:json_annotation/json_annotation.dart';

part 'spec.g.dart';

@JsonSerializable()
class Spec {
  List<BikePartProperty>? bikePartPropertyList; //	尺码
  List<BikePartImage>? bikePartImageList; //	颜色

  Spec({
    this.bikePartPropertyList,
    this.bikePartImageList,
  });

  factory Spec.fromJson(Map<String, dynamic> json) => _$SpecFromJson(json);

  Map<String, dynamic> toJson() => _$SpecToJson(this);
}

@JsonSerializable()
class BikePartProperty {
  int? id;
  int? partType;
  int? partId;
  String? propertyName;
  String? propertyValue;
  bool? select=false;

  BikePartProperty({
    this.id,
    this.partType,
    this.partId,
    this.propertyName,
    this.propertyValue,
    this.select,
  });

  factory BikePartProperty.fromJson(Map<String, dynamic> json) =>
      _$BikePartPropertyFromJson(json);

  Map<String, dynamic> toJson() => _$BikePartPropertyToJson(this);
}

@JsonSerializable()
class BikePartImage {
  int? id;
  int? partType;
  int? partId;
  String? image;
  String? linkColor;
  bool? select=false;

  BikePartImage({
    this.id,
    this.partType,
    this.partId,
    this.image,
    this.linkColor,
    this.select,
  });

  factory BikePartImage.fromJson(Map<String, dynamic> json) =>
      _$BikePartImageFromJson(json);

  Map<String, dynamic> toJson() => _$BikePartImageToJson(this);
}
