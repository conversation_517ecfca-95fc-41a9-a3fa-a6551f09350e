import 'package:json_annotation/json_annotation.dart';

import 'challenge_detail.dart';

part 'challenge_info.g.dart';

@JsonSerializable()
class ChallengeInfo {
  final String? id;
  final int? tenantId;
  final String? title;
  final String? medalId;
  final String? titleEn;
  final String? luckyDrawaId;
  final int? lotteryNum;
  final String? logo;
  final String? headBg;
  final String? richTextDescriptions;
  final String? richTextLiabilityWaiver;
  final int? mileageTarget;
  final int? altitudeTarget;
  final int? durationTarget;
  final int? timesTarget;
  final int? singleMinimumMileageLimit;
  final int? integralComplete;
  final String? registrationStartTime;
  final String? registrationEndTime;
  final String? startTime;
  final String? endTime;
  final double? price;
  final bool? autonym;
  final int? playersLimit;
  final int? status;
  final int? type;
  final String? groupChatLink;
  final String? wxMiniShareImg;
  final int? isDeleted;
  final String? createBy;
  final String? createTime;
  final String? updateBy;
  final String? updateTime;
  final bool? userSignUpFlag;
  final RunningStatus? runningStatus;

  const ChallengeInfo({
    this.id,
    this.tenantId,
    this.title,
    this.medalId,
    this.titleEn,
    this.luckyDrawaId,
    this.lotteryNum,
    this.logo,
    this.headBg,
    this.richTextDescriptions,
    this.richTextLiabilityWaiver,
    this.mileageTarget,
    this.altitudeTarget,
    this.durationTarget,
    this.timesTarget,
    this.singleMinimumMileageLimit,
    this.integralComplete,
    this.registrationStartTime,
    this.registrationEndTime,
    this.startTime,
    this.endTime,
    this.price,
    this.autonym,
    this.playersLimit,
    this.status,
    this.type,
    this.groupChatLink,
    this.wxMiniShareImg,
    this.isDeleted,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.userSignUpFlag,
    this.runningStatus,
  });

  factory ChallengeInfo.fromJson(Map<String, dynamic> json) =>
      _$ChallengeInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ChallengeInfoToJson(this);
}
