import 'package:json_annotation/json_annotation.dart';
part 'news.g.dart';

@JsonSerializable()
class News{
  num? uid;
  num? bodyId;
  String? bodyName;
  String? faceUrl;
  num? messageId;
  String? lastMessageContent;
  num? startTime;
  num? redDot;
  News({
    this.uid,
    this.bodyId,
    this.bodyName,
    this.faceUrl,
    this.messageId,
    this.lastMessageContent,
    this.startTime,
    this.redDot
  });
  factory News.fromJson(Map<String, dynamic> json) => _$NewsFromJson(json);
  Map<String, dynamic> toJson() => _$NewsToJson(this);
}