// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nearby_shop.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NearbyShop _$NearbyShopFromJson(Map<String, dynamic> json) => NearbyShop(
      address: json['address'] as String?,
      businessCloseTime: json['businessCloseTime'] == null
          ? null
          : BusinessCloseTime.fromJson(
              json['businessCloseTime'] as Map<String, dynamic>),
      businessOpenTime: json['businessOpenTime'] == null
          ? null
          : BusinessOpenTime.fromJson(
              json['businessOpenTime'] as Map<String, dynamic>),
      coverImageUrl: json['coverImageUrl'] as String?,
      detailContent: json['detailContent'] as String?,
      districtStartPoint: (json['districtStartPoint'] as num?)?.toInt(),
      id: json['id'] as String?,
      latLon: json['latLon'] as String?,
      name: json['name'] as String?,
      sort: (json['sort'] as num?)?.toInt(),
      status: json['status'] as String?,
      telephone: (json['telephone'] as num?)?.toInt(),
      type: json['type'] as String?,
    );

Map<String, dynamic> _$NearbyShopToJson(NearbyShop instance) =>
    <String, dynamic>{
      'address': instance.address,
      'businessCloseTime': instance.businessCloseTime,
      'businessOpenTime': instance.businessOpenTime,
      'coverImageUrl': instance.coverImageUrl,
      'detailContent': instance.detailContent,
      'districtStartPoint': instance.districtStartPoint,
      'id': instance.id,
      'latLon': instance.latLon,
      'name': instance.name,
      'sort': instance.sort,
      'status': instance.status,
      'telephone': instance.telephone,
      'type': instance.type,
    };

BusinessCloseTime _$BusinessCloseTimeFromJson(Map<String, dynamic> json) =>
    BusinessCloseTime(
      hour: (json['hour'] as num?)?.toInt(),
      minute: (json['minute'] as num?)?.toInt(),
      nano: (json['nano'] as num?)?.toInt(),
      second: (json['second'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BusinessCloseTimeToJson(BusinessCloseTime instance) =>
    <String, dynamic>{
      'hour': instance.hour,
      'minute': instance.minute,
      'nano': instance.nano,
      'second': instance.second,
    };

BusinessOpenTime _$BusinessOpenTimeFromJson(Map<String, dynamic> json) =>
    BusinessOpenTime(
      hour: (json['hour'] as num?)?.toInt(),
      minute: (json['minute'] as num?)?.toInt(),
      nano: (json['nano'] as num?)?.toInt(),
      second: (json['second'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BusinessOpenTimeToJson(BusinessOpenTime instance) =>
    <String, dynamic>{
      'hour': instance.hour,
      'minute': instance.minute,
      'nano': instance.nano,
      'second': instance.second,
    };
