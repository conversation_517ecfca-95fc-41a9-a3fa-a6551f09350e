// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'route_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RouteInfo _$RouteInfoFromJson(Map<String, dynamic> json) => RouteInfo(
      routeId: (json['routeId'] as num?)?.toInt(),
      routeName: json['routeName'] as String?,
      location: json['location'] as String?,
      cityCode: json['cityCode'] as String?,
      city: json['city'] as String?,
      province: json['province'] as String?,
      district: json['district'] as String?,
      address: json['address'] as String?,
      coverImg: json['coverImg'] as String?,
      squareCoverImg: json['squareCoverImg'] as String?,
      lightCoverImg: json['lightCoverImg'] as String?,
      lightSquareCoverImg: json['lightSquareCoverImg'] as String?,
      description: json['description'] as String?,
      routeMeter: (json['routeMeter'] as num?)?.toInt(),
      districtStartPoint: (json['districtStartPoint'] as num?)?.toInt(),
      routeClimb: (json['routeClimb'] as num?)?.toInt(),
      routeDown: (json['routeDown'] as num?)?.toInt(),
      verifyType: (json['verifyType'] as num?)?.toInt(),
      path: json['path'] as String?,
      altitudePath: json['altitudePath'] as String?,
      checkinAmount: (json['checkinAmount'] as num?)?.toInt(),
      createTime: json['createTime'] as String?,
      updateTime: json['updateTime'] as String?,
      ranking: (json['ranking'] as num?)?.toInt(),
      postRideId: (json['postRideId'] as num?)?.toInt(),
      userRouteCheckinInfoList:
          (json['userRouteCheckinInfoList'] as List<dynamic>?)
              ?.map((e) => RouteCheckInfo.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$RouteInfoToJson(RouteInfo instance) => <String, dynamic>{
      'routeId': instance.routeId,
      'routeName': instance.routeName,
      'location': instance.location,
      'cityCode': instance.cityCode,
      'city': instance.city,
      'province': instance.province,
      'district': instance.district,
      'address': instance.address,
      'coverImg': instance.coverImg,
      'squareCoverImg': instance.squareCoverImg,
      'lightCoverImg': instance.lightCoverImg,
      'lightSquareCoverImg': instance.lightSquareCoverImg,
      'description': instance.description,
      'routeMeter': instance.routeMeter,
      'districtStartPoint': instance.districtStartPoint,
      'routeClimb': instance.routeClimb,
      'routeDown': instance.routeDown,
      'verifyType': instance.verifyType,
      'path': instance.path,
      'altitudePath': instance.altitudePath,
      'checkinAmount': instance.checkinAmount,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'ranking': instance.ranking,
      'postRideId': instance.postRideId,
      'userRouteCheckinInfoList': instance.userRouteCheckinInfoList,
    };
