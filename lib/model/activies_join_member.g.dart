// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activies_join_member.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ActiviesJoinMember _$ActiviesJoinMemberFromJson(Map<String, dynamic> json) =>
    ActiviesJoinMember(
      id: json['id'] as String?,
      bikeInfo: json['bikeInfo'] == null
          ? null
          : Bike.fromJson(json['bikeInfo'] as Map<String, dynamic>),
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      eventId: json['eventId'] as String?,
      joinedDate: json['joinedDate'] as String?,
      status: json['status'] as String?,
      sysOrgCode: json['sysOrgCode'] as String?,
      uid: (json['uid'] as num?)?.toInt(),
      nick: json['nick'] as String?,
      userFaceUrl: json['userFaceUrl'] as String?,
      kolStatus: (json['kolStatus'] as num?)?.toInt(),
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      bikeSnapshotId: json['bikeSnapshotId'] as String?,
    );

Map<String, dynamic> _$ActiviesJoinMemberToJson(ActiviesJoinMember instance) =>
    <String, dynamic>{
      'id': instance.id,
      'bikeInfo': instance.bikeInfo,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'eventId': instance.eventId,
      'joinedDate': instance.joinedDate,
      'status': instance.status,
      'sysOrgCode': instance.sysOrgCode,
      'uid': instance.uid,
      'kolStatus': instance.kolStatus,
      'nick': instance.nick,
      'userFaceUrl': instance.userFaceUrl,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'bikeSnapshotId': instance.bikeSnapshotId,
    };
