// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shopping_address.dart';

// **************************************************************************
// CopyWithGenerator
// **************************************************************************

abstract class _$ShoppingAddressCWProxy {
  ShoppingAddress address(String? address);

  ShoppingAddress city(String? city);

  ShoppingAddress id(int? id);

  ShoppingAddress mobile(String? mobile);

  ShoppingAddress areaCode(String? areaCode);

  ShoppingAddress postCode(String? postCode);

  ShoppingAddress province(String? province);

  ShoppingAddress shoppingName(String? shoppingName);

  ShoppingAddress town(String? town);

  ShoppingAddress uid(num? uid);

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ShoppingAddress(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ShoppingAddress(...).copyWith(id: 12, name: "My name")
  /// ````
  ShoppingAddress call({
    String? address,
    String? city,
    int? id,
    String? mobile,
    String? areaCode,
    String? postCode,
    String? province,
    String? shoppingName,
    String? town,
    num? uid,
  });
}

/// Proxy class for `copyWith` functionality. This is a callable class and can be used as follows: `instanceOfShoppingAddress.copyWith(...)`. Additionally contains functions for specific fields e.g. `instanceOfShoppingAddress.copyWith.fieldName(...)`
class _$ShoppingAddressCWProxyImpl implements _$ShoppingAddressCWProxy {
  const _$ShoppingAddressCWProxyImpl(this._value);

  final ShoppingAddress _value;

  @override
  ShoppingAddress address(String? address) => this(address: address);

  @override
  ShoppingAddress city(String? city) => this(city: city);

  @override
  ShoppingAddress id(int? id) => this(id: id);

  @override
  ShoppingAddress mobile(String? mobile) => this(mobile: mobile);

  @override
  ShoppingAddress areaCode(String? areaCode) => this(areaCode: areaCode);

  @override
  ShoppingAddress postCode(String? postCode) => this(postCode: postCode);

  @override
  ShoppingAddress province(String? province) => this(province: province);

  @override
  ShoppingAddress shoppingName(String? shoppingName) =>
      this(shoppingName: shoppingName);

  @override
  ShoppingAddress town(String? town) => this(town: town);

  @override
  ShoppingAddress uid(num? uid) => this(uid: uid);

  @override

  /// This function **does support** nullification of nullable fields. All `null` values passed to `non-nullable` fields will be ignored. You can also use `ShoppingAddress(...).copyWith.fieldName(...)` to override fields one at a time with nullification support.
  ///
  /// Usage
  /// ```dart
  /// ShoppingAddress(...).copyWith(id: 12, name: "My name")
  /// ````
  ShoppingAddress call({
    Object? address = const $CopyWithPlaceholder(),
    Object? city = const $CopyWithPlaceholder(),
    Object? id = const $CopyWithPlaceholder(),
    Object? mobile = const $CopyWithPlaceholder(),
    Object? areaCode = const $CopyWithPlaceholder(),
    Object? postCode = const $CopyWithPlaceholder(),
    Object? province = const $CopyWithPlaceholder(),
    Object? shoppingName = const $CopyWithPlaceholder(),
    Object? town = const $CopyWithPlaceholder(),
    Object? uid = const $CopyWithPlaceholder(),
  }) {
    return ShoppingAddress(
      address: address == const $CopyWithPlaceholder()
          ? _value.address
          // ignore: cast_nullable_to_non_nullable
          : address as String?,
      city: city == const $CopyWithPlaceholder()
          ? _value.city
          // ignore: cast_nullable_to_non_nullable
          : city as String?,
      id: id == const $CopyWithPlaceholder()
          ? _value.id
          // ignore: cast_nullable_to_non_nullable
          : id as int?,
      mobile: mobile == const $CopyWithPlaceholder()
          ? _value.mobile
          // ignore: cast_nullable_to_non_nullable
          : mobile as String?,
      areaCode: areaCode == const $CopyWithPlaceholder()
          ? _value.areaCode
          // ignore: cast_nullable_to_non_nullable
          : areaCode as String?,
      postCode: postCode == const $CopyWithPlaceholder()
          ? _value.postCode
          // ignore: cast_nullable_to_non_nullable
          : postCode as String?,
      province: province == const $CopyWithPlaceholder()
          ? _value.province
          // ignore: cast_nullable_to_non_nullable
          : province as String?,
      shoppingName: shoppingName == const $CopyWithPlaceholder()
          ? _value.shoppingName
          // ignore: cast_nullable_to_non_nullable
          : shoppingName as String?,
      town: town == const $CopyWithPlaceholder()
          ? _value.town
          // ignore: cast_nullable_to_non_nullable
          : town as String?,
      uid: uid == const $CopyWithPlaceholder()
          ? _value.uid
          // ignore: cast_nullable_to_non_nullable
          : uid as num?,
    );
  }
}

extension $ShoppingAddressCopyWith on ShoppingAddress {
  /// Returns a callable class that can be used as follows: `instanceOfShoppingAddress.copyWith(...)` or like so:`instanceOfShoppingAddress.copyWith.fieldName(...)`.
  // ignore: library_private_types_in_public_api
  _$ShoppingAddressCWProxy get copyWith => _$ShoppingAddressCWProxyImpl(this);
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShoppingAddress _$ShoppingAddressFromJson(Map<String, dynamic> json) =>
    ShoppingAddress(
      address: json['address'] as String?,
      city: json['city'] as String?,
      id: (json['id'] as num?)?.toInt(),
      mobile: json['mobile'] as String?,
      areaCode: json['areaCode'] as String? ?? "+86",
      postCode: json['postCode'] as String?,
      province: json['province'] as String?,
      shoppingName: json['shoppingName'] as String?,
      town: json['town'] as String?,
      uid: json['uid'] as num?,
    );

Map<String, dynamic> _$ShoppingAddressToJson(ShoppingAddress instance) =>
    <String, dynamic>{
      'address': instance.address,
      'city': instance.city,
      'id': instance.id,
      'mobile': instance.mobile,
      'areaCode': instance.areaCode,
      'postCode': instance.postCode,
      'province': instance.province,
      'shoppingName': instance.shoppingName,
      'town': instance.town,
      'uid': instance.uid,
    };
