// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'challenge_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChallengeInfo _$ChallengeInfoFromJson(Map<String, dynamic> json) =>
    ChallengeInfo(
      id: json['id'] as String?,
      tenantId: (json['tenantId'] as num?)?.toInt(),
      title: json['title'] as String?,
      medalId: json['medalId'] as String?,
      titleEn: json['titleEn'] as String?,
      luckyDrawaId: json['luckyDrawaId'] as String?,
      lotteryNum: (json['lotteryNum'] as num?)?.toInt(),
      logo: json['logo'] as String?,
      headBg: json['headBg'] as String?,
      richTextDescriptions: json['richTextDescriptions'] as String?,
      richTextLiabilityWaiver: json['richTextLiabilityWaiver'] as String?,
      mileageTarget: (json['mileageTarget'] as num?)?.toInt(),
      altitudeTarget: (json['altitudeTarget'] as num?)?.toInt(),
      durationTarget: (json['durationTarget'] as num?)?.toInt(),
      timesTarget: (json['timesTarget'] as num?)?.toInt(),
      singleMinimumMileageLimit:
          (json['singleMinimumMileageLimit'] as num?)?.toInt(),
      integralComplete: (json['integralComplete'] as num?)?.toInt(),
      registrationStartTime: json['registrationStartTime'] as String?,
      registrationEndTime: json['registrationEndTime'] as String?,
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      autonym: json['autonym'] as bool?,
      playersLimit: (json['playersLimit'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      type: (json['type'] as num?)?.toInt(),
      groupChatLink: json['groupChatLink'] as String?,
      wxMiniShareImg: json['wxMiniShareImg'] as String?,
      isDeleted: (json['isDeleted'] as num?)?.toInt(),
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      userSignUpFlag: json['userSignUpFlag'] as bool?,
      runningStatus:
          $enumDecodeNullable(_$RunningStatusEnumMap, json['runningStatus']),
    );

Map<String, dynamic> _$ChallengeInfoToJson(ChallengeInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'tenantId': instance.tenantId,
      'title': instance.title,
      'medalId': instance.medalId,
      'titleEn': instance.titleEn,
      'luckyDrawaId': instance.luckyDrawaId,
      'lotteryNum': instance.lotteryNum,
      'logo': instance.logo,
      'headBg': instance.headBg,
      'richTextDescriptions': instance.richTextDescriptions,
      'richTextLiabilityWaiver': instance.richTextLiabilityWaiver,
      'mileageTarget': instance.mileageTarget,
      'altitudeTarget': instance.altitudeTarget,
      'durationTarget': instance.durationTarget,
      'timesTarget': instance.timesTarget,
      'singleMinimumMileageLimit': instance.singleMinimumMileageLimit,
      'integralComplete': instance.integralComplete,
      'registrationStartTime': instance.registrationStartTime,
      'registrationEndTime': instance.registrationEndTime,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'price': instance.price,
      'autonym': instance.autonym,
      'playersLimit': instance.playersLimit,
      'status': instance.status,
      'type': instance.type,
      'groupChatLink': instance.groupChatLink,
      'wxMiniShareImg': instance.wxMiniShareImg,
      'isDeleted': instance.isDeleted,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'userSignUpFlag': instance.userSignUpFlag,
      'runningStatus': _$RunningStatusEnumMap[instance.runningStatus],
    };

const _$RunningStatusEnumMap = {
  RunningStatus.notStarted: 0,
  RunningStatus.signUp: 1,
  RunningStatus.running: 2,
  RunningStatus.ended: 3,
};
