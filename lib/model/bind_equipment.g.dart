// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bind_equipment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BindEquipment _$BindEquipmentFromJson(Map<String, dynamic> json) =>
    BindEquipment(
      id: (json['id'] as num?)?.toInt(),
      uid: (json['uid'] as num?)?.toInt(),
      startTime: (json['startTime'] as num?)?.toInt(),
      endTime: (json['endTime'] as num?)?.toInt(),
      delFlag: (json['delFlag'] as num?)?.toInt(),
      syncFlag: (json['syncFlag'] as num?)?.toInt(),
      nick: json['nick'] as String?,
      bindType: json['bindType'] as String?,
      thirdPartyId: json['thirdPartyId'] as String?,
      token: json['token'] as String?,
      unionId: json['unionId'] as String?,
      openId: json['openId'] as String?,
      refreshToken: json['refreshToken'] as String?,
    );

Map<String, dynamic> _$BindEquipmentToJson(BindEquipment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'delFlag': instance.delFlag,
      'syncFlag': instance.syncFlag,
      'nick': instance.nick,
      'bindType': instance.bindType,
      'thirdPartyId': instance.thirdPartyId,
      'token': instance.token,
      'unionId': instance.unionId,
      'openId': instance.openId,
      'refreshToken': instance.refreshToken,
    };
