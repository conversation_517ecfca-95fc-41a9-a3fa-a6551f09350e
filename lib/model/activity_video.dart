import 'package:json_annotation/json_annotation.dart';

part 'activity_video.g.dart';

@JsonSerializable()
class ActivityVideo {
  @Json<PERSON>ey(defaultValue: '')
  final String activitiesId;
  @<PERSON><PERSON><PERSON>ey(defaultValue: '')
  final String coverUrl;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: 0)
  final int id;
  @J<PERSON><PERSON>ey(defaultValue: '')
  final String videoName;
  @JsonKey(defaultValue: '')
  final String videoUrl;

  const ActivityVideo({
    required this.activitiesId,
    required this.coverUrl,
    required this.id,
    required this.videoName,
    required this.videoUrl,
  });

  factory ActivityVideo.fromJson(Map<String, dynamic> json) =>
      _$ActivityVideoFromJson(json);

  Map<String, dynamic> toJson() => _$ActivityVideoToJson(this);
}
