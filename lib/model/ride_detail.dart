import 'package:new_edge/model/bike.dart';
import 'package:new_edge/model/ride_record.dart';
import 'package:new_edge/model/route_info.dart';
import 'package:new_edge/model/user.dart';
import 'package:json_annotation/json_annotation.dart';

import 'garmin_info.dart';

part 'ride_detail.g.dart';

@JsonSerializable()
class RideDetail {
  RideRecord? rideDetail;
  User? userInfo; //	添加时间
  Bike? bikeInfo; //		状态 0-退役 1-在役
  GarminInfo? garminInfo;
  List<RouteInfo>? routeMatchInfoModelList; //匹配路段

  RideDetail(
      {this.rideDetail,
      this.userInfo,
      this.bikeInfo,
      this.garminInfo,
      this.routeMatchInfoModelList});

  factory RideDetail.fromJson(Map<String, dynamic> json) =>
      _$RideDetailFromJson(json);

  Map<String, dynamic> toJson() => _$RideDetailToJson(this);
}
