// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'challenge_lucky_drawa.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChallengeLuckyDrawa _$ChallengeLuckyDrawaFromJson(Map<String, dynamic> json) =>
    ChallengeLuckyDrawa(
      channelType: json['channelType'] as String?,
      channelTypeId: json['channelTypeId'] as String?,
      cover: json['cover'] as String?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      endTime: json['endTime'] as String?,
      gainPrizeList: (json['gainPrizeList'] as List<dynamic>?)
          ?.map((e) => PrizeInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      id: json['id'] as String?,
      isDeleted: (json['isDeleted'] as num?)?.toInt(),
      name: json['name'] as String?,
      numberOfLotteryDraws: (json['numberOfLotteryDraws'] as num?)?.toInt(),
      prizeList: (json['prizeList'] as List<dynamic>?)
              ?.map((e) => PrizeInfo.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      rtDescription: json['rtDescription'] as String?,
      startTime: json['startTime'] as String?,
      tenantId: (json['tenantId'] as num?)?.toInt(),
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      useState: json['useState'] as bool?,
    );

Map<String, dynamic> _$ChallengeLuckyDrawaToJson(
        ChallengeLuckyDrawa instance) =>
    <String, dynamic>{
      'channelType': instance.channelType,
      'channelTypeId': instance.channelTypeId,
      'cover': instance.cover,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'endTime': instance.endTime,
      'gainPrizeList': instance.gainPrizeList,
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'name': instance.name,
      'numberOfLotteryDraws': instance.numberOfLotteryDraws,
      'prizeList': instance.prizeList,
      'rtDescription': instance.rtDescription,
      'startTime': instance.startTime,
      'tenantId': instance.tenantId,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'useState': instance.useState,
    };
