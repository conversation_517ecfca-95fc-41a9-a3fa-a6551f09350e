// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'join_challenge.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JoinChallenge _$Join<PERSON>hallenge<PERSON>romJson(Map<String, dynamic> json) =>
    JoinChallenge(
      logo: json['logo'] as String?,
      altitudeProgress: (json['altitudeProgress'] as num?)?.toInt(),
      altitudeTarget: (json['altitudeTarget'] as num?)?.toInt(),
      challengeId: json['challengeId'] as String?,
      durationProgress: (json['durationProgress'] as num?)?.toInt(),
      durationTarget: (json['durationTarget'] as num?)?.toInt(),
      endTime: json['endTime'] as String?,
      id: json['id'] as String?,
      isTargetCompleted: (json['isTargetCompleted'] as num?)?.toInt(),
      mileageProgress: (json['mileageProgress'] as num?)?.toInt(),
      mileageTarget: (json['mileageTarget'] as num?)?.toInt(),
      startTime: json['startTime'] as String?,
      state: json['state'] as String?,
      timesProgress: (json['timesProgress'] as num?)?.toInt(),
      timesTarget: (json['timesTarget'] as num?)?.toInt(),
      title: json['title'] as String?,
      uid: (json['uid'] as num?)?.toInt(),
    );

Map<String, dynamic> _$JoinChallengeToJson(JoinChallenge instance) =>
    <String, dynamic>{
      'logo': instance.logo,
      'altitudeProgress': instance.altitudeProgress,
      'altitudeTarget': instance.altitudeTarget,
      'challengeId': instance.challengeId,
      'durationProgress': instance.durationProgress,
      'durationTarget': instance.durationTarget,
      'endTime': instance.endTime,
      'id': instance.id,
      'isTargetCompleted': instance.isTargetCompleted,
      'mileageProgress': instance.mileageProgress,
      'mileageTarget': instance.mileageTarget,
      'startTime': instance.startTime,
      'state': instance.state,
      'timesProgress': instance.timesProgress,
      'timesTarget': instance.timesTarget,
      'title': instance.title,
      'uid': instance.uid,
    };
