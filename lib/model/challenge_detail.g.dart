// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'challenge_detail.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChallengeDetail _$ChallengeDetailFromJson(Map<String, dynamic> json) =>
    ChallengeDetail(
      accomplishHeadPortraits:
          (json['accomplishHeadPortraits'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      accomplishNum: (json['accomplishNum'] as num?)?.toInt(),
      altitudeTarget: (json['altitudeTarget'] as num?)?.toInt(),
      autonym: json['autonym'] as bool?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      durationTarget: (json['durationTarget'] as num?)?.toInt(),
      endTime: json['endTime'] as String?,
      groupChatLink: json['groupChatLink'] as String?,
      headBg: json['headBg'] as String?,
      id: json['id'] as String?,
      integralComplete: (json['integralComplete'] as num?)?.toInt(),
      isDeleted: (json['isDeleted'] as num?)?.toInt(),
      logo: json['logo'] as String?,
      lotteryNum: (json['lotteryNum'] as num?)?.toInt(),
      luckyDrawaId: json['luckyDrawaId'] as String?,
      luckyDrawaInfoVo: json['luckyDrawaInfoVo'] == null
          ? null
          : LuckyDrawaInfoVo.fromJson(
              json['luckyDrawaInfoVo'] as Map<String, dynamic>),
      medalId: json['medalId'] as String?,
      medalInfoNew: json['medalInfoNew'] == null
          ? null
          : MedalInfo.fromJson(json['medalInfoNew'] as Map<String, dynamic>),
      mileageTarget: (json['mileageTarget'] as num?)?.toInt(),
      playersLimit: (json['playersLimit'] as num?)?.toInt(),
      price: (json['price'] as num?)?.toInt(),
      prizeList: (json['prizeList'] as List<dynamic>?)
          ?.map((e) => PrizeInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      registrationEndTime: json['registrationEndTime'] as String?,
      registrationRecordId: json['registrationRecordId'] as String?,
      registrationStartTime: json['registrationStartTime'] as String?,
      richTextDescriptions: json['richTextDescriptions'] as String?,
      richTextLiabilityWaiver: json['richTextLiabilityWaiver'] as String?,
      runningStatus: (json['runningStatus'] as num?)?.toInt(),
      signUpHeadPortraits: (json['signUpHeadPortraits'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      signUpNum: (json['signUpNum'] as num?)?.toInt(),
      singleMinimumMileageLimit:
          (json['singleMinimumMileageLimit'] as num?)?.toInt(),
      startTime: json['startTime'] as String?,
      status: (json['status'] as num?)?.toInt(),
      successPrizeToStoreFlag: json['successPrizeToStoreFlag'] as bool?,
      tenantId: (json['tenantId'] as num?)?.toInt(),
      timesTarget: (json['timesTarget'] as num?)?.toInt(),
      title: json['title'] as String?,
      titleEn: json['titleEn'] as String?,
      type: (json['type'] as num?)?.toInt(),
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      userAltitudeTarget: (json['userAltitudeTarget'] as num?)?.toInt(),
      userDurationTarget: (json['userDurationTarget'] as num?)?.toInt(),
      userIsComplete: json['userIsComplete'] as bool?,
      userMileageTarget: (json['userMileageTarget'] as num?)?.toInt(),
      userSignUpFlag: json['userSignUpFlag'] as bool?,
      userTimesTarget: (json['userTimesTarget'] as num?)?.toInt(),
      allowJoin: json['allowJoin'] as bool?,
      overRangeTips: json['overRangeTips'] as String?,
      wxMiniShareImg: json['wxMiniShareImg'] as String?,
    );

Map<String, dynamic> _$ChallengeDetailToJson(ChallengeDetail instance) =>
    <String, dynamic>{
      'accomplishHeadPortraits': instance.accomplishHeadPortraits,
      'accomplishNum': instance.accomplishNum,
      'altitudeTarget': instance.altitudeTarget,
      'autonym': instance.autonym,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'durationTarget': instance.durationTarget,
      'endTime': instance.endTime,
      'groupChatLink': instance.groupChatLink,
      'headBg': instance.headBg,
      'id': instance.id,
      'integralComplete': instance.integralComplete,
      'isDeleted': instance.isDeleted,
      'logo': instance.logo,
      'lotteryNum': instance.lotteryNum,
      'luckyDrawaId': instance.luckyDrawaId,
      'luckyDrawaInfoVo': instance.luckyDrawaInfoVo,
      'medalId': instance.medalId,
      'medalInfoNew': instance.medalInfoNew,
      'mileageTarget': instance.mileageTarget,
      'playersLimit': instance.playersLimit,
      'price': instance.price,
      'prizeList': instance.prizeList,
      'registrationEndTime': instance.registrationEndTime,
      'registrationRecordId': instance.registrationRecordId,
      'registrationStartTime': instance.registrationStartTime,
      'richTextDescriptions': instance.richTextDescriptions,
      'richTextLiabilityWaiver': instance.richTextLiabilityWaiver,
      'runningStatus': instance.runningStatus,
      'signUpHeadPortraits': instance.signUpHeadPortraits,
      'signUpNum': instance.signUpNum,
      'singleMinimumMileageLimit': instance.singleMinimumMileageLimit,
      'startTime': instance.startTime,
      'status': instance.status,
      'successPrizeToStoreFlag': instance.successPrizeToStoreFlag,
      'tenantId': instance.tenantId,
      'timesTarget': instance.timesTarget,
      'title': instance.title,
      'titleEn': instance.titleEn,
      'type': instance.type,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'userAltitudeTarget': instance.userAltitudeTarget,
      'userDurationTarget': instance.userDurationTarget,
      'userIsComplete': instance.userIsComplete,
      'userMileageTarget': instance.userMileageTarget,
      'userSignUpFlag': instance.userSignUpFlag,
      'allowJoin': instance.allowJoin,
      'overRangeTips': instance.overRangeTips,
      'userTimesTarget': instance.userTimesTarget,
      'wxMiniShareImg': instance.wxMiniShareImg,
    };

LuckyDrawaInfoVo _$LuckyDrawaInfoVoFromJson(Map<String, dynamic> json) =>
    LuckyDrawaInfoVo(
      channelType: json['channelType'] as String?,
      channelTypeId: json['channelTypeId'] as String?,
      cover: json['cover'] as String?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      endTime: json['endTime'] as String?,
      gainPrizeList: (json['gainPrizeList'] as List<dynamic>?)
          ?.map((e) => PrizeInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      id: json['id'] as String?,
      isDeleted: (json['isDeleted'] as num?)?.toInt(),
      name: json['name'] as String?,
      numberOfLotteryDraws: (json['numberOfLotteryDraws'] as num?)?.toInt(),
      prizeList: (json['prizeList'] as List<dynamic>?)
          ?.map((e) => PrizeInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
      rtDescription: json['rtDescription'] as String?,
      startTime: json['startTime'] as String?,
      tenantId: (json['tenantId'] as num?)?.toInt(),
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      useState: json['useState'] as bool?,
    );

Map<String, dynamic> _$LuckyDrawaInfoVoToJson(LuckyDrawaInfoVo instance) =>
    <String, dynamic>{
      'channelType': instance.channelType,
      'channelTypeId': instance.channelTypeId,
      'cover': instance.cover,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'endTime': instance.endTime,
      'gainPrizeList': instance.gainPrizeList,
      'id': instance.id,
      'isDeleted': instance.isDeleted,
      'name': instance.name,
      'numberOfLotteryDraws': instance.numberOfLotteryDraws,
      'prizeList': instance.prizeList,
      'rtDescription': instance.rtDescription,
      'startTime': instance.startTime,
      'tenantId': instance.tenantId,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'useState': instance.useState,
    };

PrizeInfo _$PrizeInfoFromJson(Map<String, dynamic> json) => PrizeInfo(
      autonymFlag: json['autonymFlag'] as bool?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      deliveryFee: json['deliveryFee'] as num?,
      description: json['description'] as String?,
      id: json['id'] as String?,
      image: json['image'] as String?,
      initialQuantity: (json['initialQuantity'] as num?)?.toInt(),
      isDeleted: (json['isDeleted'] as num?)?.toInt(),
      logo: json['logo'] as String?,
      name: json['name'] as String?,
      sponsor: json['sponsor'] as String?,
      pickupEndTime: json['pickupEndTime'] as String?,
      pickupStartTime: json['pickupStartTime'] as String?,
      placeType: json['placeType'] as String?,
      placeTypeId: json['placeTypeId'] as String?,
      prizeClaimDeadline: json['prizeClaimDeadline'] as String?,
      prizeClaimFormId: json['prizeClaimFormId'] as String?,
      prizeGetState:
          $enumDecodeNullable(_$PrizeGetStateEnumMap, json['prizeGetState']),
      prizeGetMethod:
          $enumDecodeNullable(_$PrizeGetMethodEnumMap, json['prizeGetMethod']),
      prizeType: $enumDecodeNullable(_$PrizeTypeEnumMap, json['prizeType']),
      thirdPartyName: json['thirdPartyName'] as String?,
      repertoryNum: (json['repertoryNum'] as num?)?.toInt(),
      scheduledTime: json['scheduledTime'] as String?,
      tenantId: (json['tenantId'] as num?)?.toInt(),
      type: $enumDecodeNullable(_$CollectTypeEnumMap, json['type']),
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      winRate: (json['winRate'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PrizeInfoToJson(PrizeInfo instance) => <String, dynamic>{
      'autonymFlag': instance.autonymFlag,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'deliveryFee': instance.deliveryFee,
      'description': instance.description,
      'id': instance.id,
      'image': instance.image,
      'initialQuantity': instance.initialQuantity,
      'isDeleted': instance.isDeleted,
      'logo': instance.logo,
      'name': instance.name,
      'sponsor': instance.sponsor,
      'pickupEndTime': instance.pickupEndTime,
      'pickupStartTime': instance.pickupStartTime,
      'placeType': instance.placeType,
      'placeTypeId': instance.placeTypeId,
      'prizeClaimDeadline': instance.prizeClaimDeadline,
      'prizeClaimFormId': instance.prizeClaimFormId,
      'prizeType': _$PrizeTypeEnumMap[instance.prizeType],
      'prizeGetState': _$PrizeGetStateEnumMap[instance.prizeGetState],
      'repertoryNum': instance.repertoryNum,
      'scheduledTime': instance.scheduledTime,
      'tenantId': instance.tenantId,
      'type': _$CollectTypeEnumMap[instance.type],
      'prizeGetMethod': _$PrizeGetMethodEnumMap[instance.prizeGetMethod],
      'thirdPartyName': instance.thirdPartyName,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'winRate': instance.winRate,
    };

const _$PrizeGetStateEnumMap = {
  PrizeGetState.toGet: 0,
  PrizeGetState.registered: 1,
  PrizeGetState.got: 2,
};

const _$PrizeGetMethodEnumMap = {
  PrizeGetMethod.store: 1,
  PrizeGetMethod.mail: 2,
  PrizeGetMethod.virtual: 3,
};

const _$PrizeTypeEnumMap = {
  PrizeType.complete: 0,
  PrizeType.lottery: 1,
};

const _$CollectTypeEnumMap = {
  CollectType.physicalMail: 1,
  CollectType.physicalStore: 2,
  CollectType.physicalMailStore: 3,
  CollectType.third: 4,
};

MedalInfo _$MedalInfoFromJson(Map<String, dynamic> json) => MedalInfo(
      shortTitle: json['shortTitle'] as String?,
      description: json['description'] as String?,
      thumb: json['thumb'] as String?,
      trophyRecordId: json['trophyRecordId'] as String?,
    );

Map<String, dynamic> _$MedalInfoToJson(MedalInfo instance) => <String, dynamic>{
      'shortTitle': instance.shortTitle,
      'thumb': instance.thumb,
      'trophyRecordId': instance.trophyRecordId,
      'description': instance.description,
    };
