import 'package:flutter/material.dart';
import 'package:new_edge/widgets/city_pickers/city_pickers.dart';
import 'package:new_edge/widgets/city_pickers/meta/area.dart';

Future<Result> selectAreaCode(
    {required BuildContext context,
    String? currentAreaCode = '+86',
    List<int>? excludeCityIds,
    bool useEn = false}) async {
  Color tagBgColor = Color.fromRGBO(255, 255, 255, 0);
  Color tagBgActiveColor = Color(0xffeeeeee);
  Color tagFontColor = Color(0xff666666);
  Color tagFontActiveColor = Color(0xff242424);
  double tagBarFontSize = 10;
  double cityItemFontSize = 16;
  double topIndexHeight = 40;
  double topIndexFontSize = 13;
  Color topIndexFontColor = Color(0xffc0c0c0);
  Color topIndexBgColor = Color(0xfff3f4f5);
  Color itemSelectFontColor = Color(0xFFEE3A23);
  Color itemSelectBgColor = Colors.blueGrey;
  Color itemFontColor = Colors.black;

  Map? provincesData = await Data.loadProvinceData();
  Map? citiesData = await Data.loadCitiesData();

  var hotTag = useEn  ? 'Hot' : '热';
  return await CityPickers.showCitiesSelector(
    context: context,
    title: useEn? 'Select country or area' : '选择国家或地区',
    locationCode: currentAreaCode,
    provincesData: provincesData as Map<String, dynamic>?,
    citiesData: citiesData as Map<String, dynamic>?,
    tagBarPadding: EdgeInsets.symmetric(vertical: 4),
    excludeCityIds: excludeCityIds,
    useEn: useEn,
    hotCities: [
      HotCity(id: 86, name: '中国大陆', tag: hotTag),
      HotCity(id: 852, name: '中国香港', tag: hotTag),
      HotCity(id: 853, name: '中国澳门', tag: hotTag),
      HotCity(id: 886, name: '中国台湾', tag: hotTag),
      HotCity(id: 44, name: '英国', tag: hotTag),
      HotCity(id: 1, name: '美国', tag: hotTag),
      HotCity(id: 61, name: '澳大利亚', tag: hotTag),
      HotCity(id: 1, name: '加拿大', tag: hotTag),
      HotCity(id: 81, name: '日本', tag: hotTag),
    ],
    itemBuilder: (item, index) {
      const commonStyle = TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
      );

      return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(
          useEn ? (item.enName ?? item.name) : '${item.name}',
          style: commonStyle,
        ),
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: Text(
            '+${item.code}',
            style: commonStyle,
          ),
        ),
      ]);
    },
    sideBarStyle: BaseStyle(
      fontSize: tagBarFontSize,
      color: tagFontColor,
      backgroundColor: tagBgColor,
//          backgroundActiveColor: tagBgActiveColor,
//          activeColor: tagFontActiveColor
    ),
    cityItemStyle: BaseStyle(
        fontSize: cityItemFontSize,
        color: itemFontColor,
        activeColor: itemSelectFontColor),
    topStickStyle: BaseStyle(
        fontSize: topIndexFontSize,
        color: topIndexFontColor,
        backgroundColor: topIndexBgColor,
        height: topIndexHeight),
    topStickBuilder: (tag) {
      return Container(
        color: Colors.white,
        height: double.infinity,
        child: Row(
          children: <Widget>[
            Container(
              width: 4,
              height: 13,
              margin: EdgeInsets.fromLTRB(16, 0, 12, 0),
              decoration: BoxDecoration(
                color: Color(0xFFEE3A23),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              '${tag == '热' ? '热门' : tag}',
              style: TextStyle(
                color: Color(0xFFEE3A23),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    },
  );
}
