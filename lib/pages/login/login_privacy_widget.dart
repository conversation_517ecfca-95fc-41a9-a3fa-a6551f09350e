import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/pages/common/privacy_dialog.dart';
import 'package:new_edge/util/settings.dart';

class LoginPrivacyWidget extends StatelessWidget {
  final ValueChanged<bool>? onSelect;
  final bool selected;
  LoginPrivacyWidget({Key? key, this.onSelect, this.selected = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () {
            onSelect?.call(!selected);
          },
          child: Container(
            width: 42,
            height: 42,
            padding: EdgeInsets.all(8 + 3),
            child: Image.asset(
              selected
                  ? DImages.formatPathPng('selected')
                  : DImages.formatPathPng('unselected'),
              fit: BoxFit.cover,
            ),
          ),
        ),
        buildLoginPrivacyBottom(context)
      ],
    );
  }

  static Widget buildLoginPrivacyBottom(BuildContext context) {
    return RichText(
      text: TextSpan(
          style: TextStyle(
            fontSize: 12,
            color: Colors.black,
          ),
          children: [
            TextSpan(
              text: "我已阅读并同意",
            ),
            TextSpan(
                text: "《用户协议》",
                recognizer: TapGestureRecognizer()
                  ..onTap = () async {
                    PrivacyUtils.startUserAgreement(context);
                  },
                style: TextStyle(color: DColor.fffb691d)),
            TextSpan(
              text: "和",
            ),
            TextSpan(
                text: "《隐私协议》",
                recognizer: TapGestureRecognizer()
                  ..onTap = () async {
                    PrivacyUtils.startPrivacyPolicy(context);
                  },
                style: TextStyle(color: DColor.fffb691d)),
          ]),
    );
  }
}
