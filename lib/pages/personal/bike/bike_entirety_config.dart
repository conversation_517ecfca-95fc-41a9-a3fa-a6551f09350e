import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:new_edge/bloc/bike_bloc.dart';
import 'package:new_edge/config/bike_config.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';

import 'package:new_edge/io/bike.dart';
import 'package:new_edge/model/api_response/api_edge_response_entity.dart';
import 'package:new_edge/model/bike_entire.dart';
import 'package:new_edge/model/bike_entire_bean.dart';
import 'package:new_edge/model/brand.dart';
import 'package:new_edge/model/model.dart';
import 'package:new_edge/model/series.dart';
import 'package:new_edge/model/spec.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/sp_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'accessories/bike_accessories_select.dart';
import 'bike_editor.dart';
import 'bike_preset_improve.dart';

class BikeEntiretyConfigPage extends StatefulWidget {
  Brand? brand;
  Series? series;
  Model? model; //img
  BikePartImage? framesetColor; //颜色id
  BikePartProperty? framesetSize; //尺寸id
  int bikeType; //自行车类型 0-山地车 1-公路车',
  int? userBikeId = 0; //自行车id
  double? totalMeter = 0.0; //历史里程
  int? modelYear; //年份
  int? operationType = BikeConfig.BIKE_INFO_SAVE; //操作类型 0-编辑整车配置，1-修改自行车信息
  String? userBikeName = '';
  late bool isBrowse; //浏览模式

  BikeEntiretyConfigPage({
    Key? key,
    this.brand,
    this.series,
    this.model,
    this.framesetColor,
    this.framesetSize,
    this.bikeType = 0,
    this.operationType,
    this.userBikeId,
    this.totalMeter,
    this.modelYear,
    this.userBikeName,
  }) : super(key: key);

  @override
  _BikeEntiretyConfigPageState createState() => _BikeEntiretyConfigPageState();
}

class _BikeEntiretyConfigPageState extends State<BikeEntiretyConfigPage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    widget.isBrowse = SpUtil.getBool("isbrowse");
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BikeBloc, BikeEntire>(builder: (context, entire) {
      return Scaffold(
        appBar: AppBar(
          title: Text('配置信息',
              style: TextStyle(
                  color: DColor.ff242424, fontSize: 18, fontFamily: "PF")),
          centerTitle: true,
          actions: [
            !widget.isBrowse
                ? TextButton(
                    child: Container(
                        width: 58,
                        height: 28,
                        decoration: BoxDecoration(
                            color: DColor.fffb691d,
                            borderRadius: BorderRadius.circular(4)),
                        child: Center(
                            child: Text(
                          '下一步',
                          style: TextStyle(color: DColor.white, fontSize: 13),
                        ))),
                    onPressed: () {
                      //下一步
                      _saveEntireBike(entire);
                    },
                  )
                : Container(),
          ],
          leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff242424,
              ),
              onPressed: () => Navigator.pop(context)),
        ),
        body: Container(
          width: double.infinity,
          padding: EdgeInsets.only(left: 15, right: 15, top: 15, bottom: 25),
          color: DColor.white,
          child: Stack(
            children: [
              ListView(
                children: [
                  _topWidget(entire),
                  SizedBox(
                    height: 30,
                  )
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  void _saveEntireBike(BikeEntire entire) async {
    var result = await Connectivity().checkConnectivity();
    if (result.contains(ConnectivityResult.none)) {
      showToast("网络异常，请检查网络连接后重试", context);
      return;
    }
    try {
      showLoadingDialog(context);
      // 保存用户整车信息
      ApiEdgeResponse<BikeEntireBean> result =
          await BikeApi(MyHttp.dio).saveEntireBike(
        frameId: entire.bikeFramesetVo?.framesetId ?? 0,
        rdLeftShiftId: entire.bikeRdLeftShiftVo?.rdLeftShiftId ?? 0,
        rdRightShiftId: entire.bikeRdRightShiftVo?.rdRightShiftId ?? 0,
        frontDerailleurId: entire.bikeFrontDerailleurVo?.frontDerailleurId ?? 0,
        rearDerailleurId: entire.bikeRearDerailleurVo?.rearDerailleurId ?? 0,
        chainringId: entire.bikeChainringVo?.chainringId ?? 0,
        cranksetId: entire.bikeCranksetVo?.cranksetId ?? 0,
        chainId: entire.bikeChainVo?.chainId ?? 0,
        frontBrakeId: entire.bikeFrontBrakeVo?.frontBrakeId ?? 0,
        rearBrakeId: entire.bikeRearBrakeVo?.rearBrakeId ?? 0,
        frontBrakeRotorId: entire.bikeFrontBrakeRotorVo?.frontBrakeRotorId ?? 0,
        rearBrakeRotorId: entire.bikeRearBrakeRotorVo?.rearBrakeRotorId ?? 0,
        frontWheelId: entire.bikeFrontWheelVo?.frontWheelId ?? 0,
        frontTireId: entire.bikeFrontTireBrandVo?.frontTireBrandId ?? 0,
        rearWheelId: entire.bikeRearWheelVo?.rearWheelId ?? 0,
        rearTireId: entire.bikeRearTireBrandVo?.rearTireBrandId ?? 0,
        saddleId: entire.bikeSaddleVo?.saddleId ?? 0,
        seatpostId: entire.bikeSeatpostVo?.seatpostId ?? 0,
        stemId: entire.bikeStemVo?.stemId ?? 0,
        rdHandlebarId: entire.bikeRdHandlebarVo?.rdHandlebarId ?? 0,
        baseHandlebarId: entire.bikeBaseHandlebarVo?.baseHandlebarId ?? 0,
        elbowResteId: entire.bikeElbowResteVo?.elbowResteId ?? 0,
        extensionHandleId: entire.bikeExtensionHandleVo?.extensionHandleId ?? 0,
        pedalsId: entire.bikePedalsVo?.pedalsId ?? 0,
        cassetteId: entire.bikeCassetteVo!.cassetteId!,
        mtLeftShiftId: entire.bikeMtLeftMtshifterVo?.mtLeftMtshifterId ?? 0,
        mtRightShiftId: entire.bikeMtRightMtshifterVo?.mtRightMtshifterId ?? 0,
        mtHandlebarId: entire.bikeMtHandlebarVo?.mtHandlebarId ?? 0,
        rearSuspensionId:
            entire.bikeMtRearSuspensionVo?.mtRearSuspensionId ?? 0,
        frontForkId: entire.bikeMtForkVo?.mtForkId ?? 0,
        leftBrakeId: entire.bikeMtLeftBrakeLeverVo?.mtLeftBrakeLeverId ?? 0,
        rightBrakeId: entire.bikeMtRightBrakeLeverVo?.mtRightBrakeLeverId ?? 0,
        userBikeId: widget.userBikeId!,
        framesetSizeId: entire.bikeFramesetVo?.sizeId ?? 0,
        framesetColorId: entire.bikeFramesetVo?.colorId ?? 0,
        maxCassetteTeethId: entire.maxCassetteTeethId ?? 0,
        //飞轮大盘尺寸id
        minCassetteTeethId: entire.minCassetteTeethId ?? 0,
        //小轮大盘尺寸id
        frontBrakeRotorSizeId: entire.frontBrakeRotorSizeId ?? 0,
        //前碟片碟片尺寸id
        rearBrakeRotorSizeId: entire.rearBrakeRotorSizeId ?? 0,
        //后碟片碟片尺寸id
        cranksetLengthId: entire.cranksetLengthId ?? 0,
        //牙盘曲柄长度id
        chainringBigTeethId: entire.chainringBigTeethId ?? 0,
        //盘片大齿数id
        chainringMiddleTeethId: entire.chainringMiddleTeethId ?? 0,
        //盘片中齿数id
        chainringSmallTeethId: entire.chainringSmallTeethId ?? 0,
        //盘片小齿数id
        frontTireBrandSizeId: entire.frontTireBrandSizeId ?? 0,
        //前外胎尺寸id
        rearTireBrandSizeId: entire.rearTireBrandSizeId ?? 0, //后外胎尺寸id
      );
      Navigator.of(context).pop();

      if (!result.isSuccess() || result.info == null) {
        showToast(result.msg ?? "车辆新增失败", context);
        return;
      }
      BikeEntireBean bikeEntireBean = result.info!;
      if (widget.operationType == BikeConfig.BIKE_INFO_SAVE) {
        pushPage(
            context,
            BikePresetImprove(
              brand: widget.brand,
              series: widget.series,
              model: widget.model,
              userEntireBikeId: bikeEntireBean?.userEntireBikeId ?? 0,
              framesetColor: widget.framesetColor,
              framesetSize: widget.framesetSize,
              modelYear: widget.modelYear ?? 0,
            ));
      } else {
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (context) => BikeEditor(
                      brand: widget.brand,
                      series: widget.series,
                      model: widget.model,
                      userBikeId: widget.userBikeId ?? 0,
                      totalMeter: widget.totalMeter ?? 0,
                      framesetColor: widget.framesetColor,
                      framesetSize: widget.framesetSize,
                      modelYear: widget.modelYear ?? 0,
                      userBikeName: widget.userBikeName ?? '',
                    )));
        // Navigator.of(context).pop();
      }
    } on DioException catch (e) {
      showToast("车辆新增失败", context);
      Navigator.of(context).pop();
    }
  }

  Widget _topWidget(BikeEntire entire) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 15),
            child: CachedNetworkImage(
              imageUrl: widget.brand?.brandLogo ?? '',
              errorWidget: (context, url, error) => Container(
                alignment: Alignment.centerLeft,
                child: Text(widget.brand?.brandName ?? '',
                    style: TextStyle(color: DColor.hitText, fontSize: 16)),
              ),
              height: 14,
              fit: BoxFit.fitHeight,
              color: DColor.ff808080,
            ),
          ),
          SizedBox(
            height: 25,
          ),
          CachedNetworkImage(
            imageUrl: widget.model?.configurationImg ?? '',
            errorWidget: (context, url, error) => Image.asset(
              DImages.formatPathPng('shandiche_white'),
              width: 233,
              height: 155.5,
              fit: BoxFit.fill,
            ),
            width: 233,
            height: 155.5,
            fit: BoxFit.fill,
          ),
          SizedBox(
            height: 5,
          ),
          Container(
            width: 280,
            child: Text(
                '${widget.brand?.brandName ?? ''} '
                '${widget.series?.seriesName ?? ''} '
                '${widget.model?.configuration ?? ''} '
                '${widget.modelYear ?? ''}',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: DColor.ff242424,
                    fontSize: 14,
                    fontWeight: FontWeight.w500)),
          ),
          Padding(
            padding: EdgeInsets.all(2),
            child: Text(
                '${widget.framesetColor?.linkColor ?? ''} ${widget.framesetSize?.propertyValue ?? ''}',
                style: TextStyle(color: DColor.hitText, fontSize: 13)),
          ),
          widget.operationType == BikeConfig.BIKE_INFO_SAVE
              ? Container(
                  margin: EdgeInsets.only(top: 8, bottom: 10),
                  padding: EdgeInsets.only(
                      left: 13.5, right: 13.5, top: 3.5, bottom: 3.5),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      color: DColor.white,
                      borderRadius: BorderRadius.circular(8)),
                  child: Text(
                    '已根据车架信息自动补充官方整车配置，若有变动，请自行调整。',
                    style: TextStyle(color: DColor.ff242424, fontSize: 11),
                    maxLines: 2,
                  ),
                )
              : Container(),
          SizedBox(
            height: 20,
          ),
          _buildContent(context, entire),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, BikeEntire entire) {
    return entire != null
        ? ListView(
            shrinkWrap: true, //为true可以解决子控件必须设置高度的问题
            physics: NeverScrollableScrollPhysics(), //禁用滑动事件
            children: [
              _groupWidget('车架组',
                  BikeConfig.getframeList(widget.bikeType!, entire), entire),
              _groupWidget('传动系统',
                  BikeConfig.driveSystemList(widget.bikeType!, entire), entire),
              _groupWidget('制动系统',
                  BikeConfig.brakeSystemList(widget.bikeType!, entire), entire),
              _groupWidget('牙盘组', BikeConfig.chainList, entire),
              _groupWidget('轮组', BikeConfig.wheelList, entire),
              _groupWidget('其他组件',
                  BikeConfig.otherList(widget.bikeType!, entire)!, entire),
            ],
          )
        : Container();
  }

  //组
  Widget _groupWidget(String title, List<int> items, BikeEntire entire) {
    return Container(
      padding: EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            child: Text(title,
                style: TextStyle(
                    color: DColor.ff242424,
                    fontSize: 18,
                    fontWeight: FontWeight.bold)),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10, bottom: 10),
            child: ListView.builder(
                itemBuilder: (context, index) {
                  return _partWidget(items[index], entire);
                },
                itemCount: items.length,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics()),
          )
        ],
      ),
    );
  }

  //部件
  Widget _partWidget(int partType, BikeEntire entire) {
    return MaterialButton(
      onPressed: () {
        if (widget.bikeType == BikeConfig.TRIATHLON_OF_BIKE_TYPE ||
            widget.isBrowse) {
          return;
        }
        if (partType == BikeConfig.BIKE_PART_TYPE_1) {
          //车架不可更换
          showToast('车架不可更换', context);
          return;
        }
        if (partType == BikeConfig.BIKE_PART_TYPE_6 &&
            entire.bikeCranksetVo?.spiderType == '一体结构') {
          //牙盘爪类型为一体结构时，“盘片”不可更换
          showToast('盘片不可更换', context);
          return;
        }
        if (partType == BikeConfig.BIKE_PART_TYPE_27 &&
            widget.bikeType == BikeConfig.GONLU_OF_BIKE_TYPE) {
          //当“车架-车架类型=公路车”，前叉不可更换
          showToast('前叉不可更换', context);
          return;
        }
        if (partType == BikeConfig.BIKE_PART_TYPE_26 &&
            entire.bikeFramesetVo?.rearSuspensionType == '软尾（不可更换）') {
          //当“车架-后避震属性=软尾（不可更换）”，“后避震”不可更换
          showToast('后避震不可更换', context);
          return;
        }
        if (partType == BikeConfig.BIKE_PART_TYPE_20 &&
            entire.bikeStemVo?.integratedDisassembled != '非一体把') {
          //当“把立-是否一体把=一体把”，“弯把”不可更换
          showToast('弯把不可更换', context);
          return;
        }
        if (partType == BikeConfig.BIKE_PART_TYPE_25 &&
            entire.bikeStemVo?.integratedDisassembled != '非一体把') {
          //当“把立-是否一体把=一体把”，“把横”不可更换
          showToast('把横不可更换', context);
          return;
        }
        pushPage(
            context,
            BikeAccessoriesSelectPage(
              partType: partType,
              bikeType: widget.bikeType,
              framesetId: widget.model?.id ?? 0,
              bikeEntire: entire,
            ));
      },
      padding: EdgeInsets.only(top: 8, bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(BikeConfig.getTitle(partType)!,
                style: TextStyle(color: DColor.ff242424, fontSize: 15)),
          ),
          Expanded(
            flex: 4,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  child: Text(
                    BikeConfig.getValue(partType, entire),
                    style: TextStyle(color: DColor.ff242424, fontSize: 15),
                    maxLines: 2,
                    softWrap: true,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.right,
                  ),
                ),
                (widget.bikeType == BikeConfig.TRIATHLON_OF_BIKE_TYPE ||
                        widget.isBrowse)
                    ? Container()
                    : SizedBox(
                        width: 10,
                      ),
                (widget.bikeType == BikeConfig.TRIATHLON_OF_BIKE_TYPE ||
                        widget.isBrowse)
                    ? Container()
                    : Image.asset(
                        'assets/images/jiantou_right_grey.png',
                        width: 14,
                        height: 14,
                        color: DColor.hitText,
                      )
              ],
            ),
          )
        ],
      ),
    );
  }
}
