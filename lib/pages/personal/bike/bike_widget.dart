import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/config/string.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/bike.dart';
import 'package:new_edge/pages/personal/bike/my_bike_list.dart';
import 'package:new_edge/util/login_utils.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/sp_utils.dart';
import 'package:new_edge/util/string_format_utils.dart';
import 'package:new_edge/widgets/slide_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';

import 'details/bike_details.dart';

//用户车辆信息显示
Widget BicycleBannerWidget(BuildContext context, Account account) {
  List<Bike> bikes = account.bikes ?? [];
  if (bikes.isNotEmpty) {
    return _buildBikeBanner(bikes);
  } else {
    return _buildEmptyBike(context);
  }
}

Widget _buildBikeBanner(List<Bike> items) {
  return Container(
      decoration: BoxDecoration(
        color: DColor.white,
        borderRadius: BorderRadius.all(Radius.circular(4)),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF000000).withOpacity(0.08),
            spreadRadius: 1, // 调整阴影扩散
            blurRadius: 5,  // 调整模糊半径
            offset: Offset(0, 1), // 调整阴影偏移，使其四周均匀分布
          ),
        ],
      ),
      child: SlideBannerWidget(
        height: 155,
        autoplay: false,
        delegate: SlideChildDelegate(
              (BuildContext context, int index) {
            Bike bike = items[index];
            return BikeWidget(
              bike: bike,
              onSelectedBike: (bike) {
                LoginUtils.setILogin(() {
                  pushNamedPage(context, '/bike/list');
                }, context);
              },
            );
          },
          onIndexChanged: (index) {},
          childCount: items.length,
        ),
      ),
  );
}

Widget _buildEmptyBike(BuildContext context) {
  return Container(
      height: 155,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: DColor.white,
        borderRadius: BorderRadius.all(Radius.circular(4)),
      ),
      child: Stack(children: [
        Positioned(
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            child: Image.asset(DImages.formatPathPng('bg_item_no_bike'),
                height: 143, fit: BoxFit.fitHeight)),
        SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text("还没有添加过自行车",
                    style: TextStyle(color: DColor.ff808080, fontSize: 14)),
                SizedBox(height: 25),
                SubmitButton(
                  text: '去添加',
                  onPressed: () {
                    LoginUtils.setILogin(() {
                      pushNamedPage(context, '/bike/list');
                    }, context);
                  },
                )
              ],
            ))
      ]));
}

class BikeWidget extends StatefulWidget {
  final Bike? bike;
  final int? uid;
  final ValueChanged<Bike?>? onSelectedBike;

  BikeWidget({Key? key, this.bike, this.uid, this.onSelectedBike})
      : super(key: key);

  @override
  _BikeWidgetState createState() => _BikeWidgetState();
}

class _BikeWidgetState extends State<BikeWidget> {
  Bike? _bike;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _bike = widget.bike;
  }

  @override
  void didUpdateWidget(BikeWidget oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
    setState(() {
      _bike = widget.bike;
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.onSelectedBike != null) {
          widget.onSelectedBike?.call(widget.bike);
        } else {
          if (widget.bike?.status == 1) {
            SpUtil.putBool("isbrowse", false); //是否为浏览模式
            pushPage(
                context,
                BikeDetailsPage(
                  bike: widget.bike,
                  uid: widget.bike?.uid,
                ));
          }
        }
      },
      child: _buildBikeItem(context),
    );
  }

  Widget _buildBikeItem(BuildContext context) {
    return Material(
        color: DColor.white,
        shadowColor: Color(0xFF000000).withOpacity(0.16),
        elevation: 5,
        child: Container(
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(4)),
            ),
            child: Stack(children: [
              Positioned(right: 15, top: 10, child: _buildBasicInfo(context)),
              Positioned(bottom: 10, right: 15, child: _buildDataInfo(context)),
              _buildBikeImage(context),
            ])));
  }

  Widget _buildBikeImage(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(bottom: 6),
        child: SizedBox(
            height: 144,
            width: 190,
            child: CachedNetworkImage(
              alignment: Alignment.centerRight,
              imageUrl: _bike?.colorImg ?? '',
              errorWidget: (context, url, error) => Image.asset(
                alignment: Alignment.centerRight,
                'assets/images/shandiche_white.png',
                fit: BoxFit.cover,
              ),
              fit: BoxFit.cover,
            )));
  }

  Widget _buildBasicInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Stack(
          children: [
            Text(_bike?.brandName ?? "",
                style: TextStyle(
                    color: Color(0xFFEAEAEA),
                    height: 1,
                    fontFamily: DFamily.bebasNeue,
                    fontWeight: FontWeight.bold,
                    fontSize: 51)),

            // SizedBox(
            //     // height: 80,
            //     width: 200,
            //     child: CachedNetworkImage(
            //       imageUrl:_bike?.brandLogo ?? "",
            //       color: DColor.ffeaeaea,
            //       placeholder: (context, url) => Image.asset(
            //         DImages.formatPathPng('img_placeholder_bg'),
            //         fit: BoxFit.cover,
            //       ),
            //       errorWidget: (context, url, error) => Image.asset(
            //         DImages.formatPathPng('img_placeholder_bg'),
            //         fit: BoxFit.cover,
            //       ),
            //       fit: BoxFit.cover,
            //     )),
            Positioned(
                top: 0,
                right: 0,
                child: Text(_bike?.modelYear ?? "",
                    style: TextStyle(
                        color: DColor.ff232323,
                        fontFamily: DFamily.bebasNeue,
                        fontWeight: FontWeight.bold,
                        fontSize: 15)))
          ],
        ),
        SizedBox(
            width: 160,
            child: Text(
                '${_bike?.seriesName ?? ''} '
                '${_bike?.modelName ?? ''} ',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end,
                style: TextStyle(
                    color: DColor.ff232323,
                    fontSize: 13,
                    fontFamily: DFamily.dinBold,
                    fontWeight: FontWeight.w500)))
      ],
    );
  }

  Widget _buildDataInfo(BuildContext context) {
    return Row(children: [
      _buildDataItem("mileage_none", "距离",
          formatMToKm(_bike?.totalMeter ?? 0) ?? "0", "KM")
    ]);
  }

  Widget _buildDataItem(
      String assertName, String title, String value, String unit) {
    return Column(
      children: [
        Row(children: [
          Image.asset(
            color: DColor.ff232323,
            DImages.formatPathPng(assertName),
            height: 11,
          ),
          SizedBox(
            width: 2,
          ),
          Text(
            " $title",
            style: TextStyle(
                fontSize: 8,
                color: DColor.ff808080,
                fontFamily: DFamily.dinBold),
          ),
        ]),
        SizedBox(height: 5),
        RichText(
          text: TextSpan(
            children: <TextSpan>[
              TextSpan(
                text: '$value',
                style: TextStyle(
                  color: DColor.ff232323,
                  fontSize: 15,
                  fontFamily: DFamily.dinBold,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                  text: ' $unit',
                  style: TextStyle(
                    color: DColor.ff232323,
                    fontSize: 9,
                    fontFamily: DFamily.dinBold,
                    fontWeight: FontWeight.bold,
                  )),
            ],
          ),
        )
      ],
    );
  }
}
