import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:new_edge/bloc/account_bloc.dart';
import 'package:new_edge/bloc/follow_bloc.dart';
import 'package:new_edge/config/bike_config.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/io/bike.dart';
import 'package:new_edge/io/home.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/brand.dart';
import 'package:new_edge/model/series.dart';
import 'package:new_edge/pages/personal/bike/bike_widget.dart';
import 'package:new_edge/picker/constants/constants.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/sp_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'bike_frame_series.dart';
import 'bike_other_frame_brand.dart';

class BikeFrameBrandPage extends StatefulWidget {
  final bool showMyBike;

  BikeFrameBrandPage({this.showMyBike = false}) {}

  @override
  _BikeFrameBrandPageState createState() => _BikeFrameBrandPageState();
}

class _BikeFrameBrandPageState extends State<BikeFrameBrandPage> {
  List<Brand> _brand = [];
  var _loadStatus = FitLoadStatus.loading;
  RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  var _networkReachbility = true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration(milliseconds: 50), () {
      _refreshController.requestRefresh();
    });
    // _refreshData();

    if (widget.showMyBike) {
      SpUtil.putBool("isbrowse", true);
    }
  }

  void _refreshData(Account localAccount) async {
    var result = await Connectivity().checkConnectivity();
    if (result.contains(ConnectivityResult.none)) {
      showToast("你的网络不给力，请稍后重试", context);
      setState(() {
        _networkReachbility = false;
      });

      _refreshController.refreshCompleted();
      return;
    } else {
      setState(() {
        _loadStatus = FitLoadStatus.loading;
        _networkReachbility = true;
      });
    }
    try {
      // showLoadingDialog(context);
      List<Brand> newBrand = await BikeApi(MyHttp.dio)
          .getBrandList(bikePartNo: BikeConfig.BIKE_PART_TYPE_1);
      setState(() {
        _brand = newBrand;
        _loadStatus = FitLoadStatus.loadSuccess;
      });
    } on DioException catch (e) {
      showToast(e.message.toString(), context);
      setState(() {
        _loadStatus = FitLoadStatus.loadError;
      });
    } finally {
      // Navigator.pop(context);
    }

    HomeApi(MyHttp.dio).getHomeInfo().then((result) async {
      setState(() {
        // _rideStatInfo = result.rideStatInfo;
        // _followNum = result.userInfo.followTotal;
        // _fansNum = result.userInfo.fansTotal;
        // _kolStatus = result.userInfo.kolStatus;
      });
      var eventFollow = FollowCountUpdated(result.userInfo!.followTotal);
      Constants.followCount = result.userInfo!.followTotal!;
      BlocProvider.of<FollowBloc>(context).add(eventFollow);
      _refreshController.refreshCompleted();
      //更新account
      Account.loginAccount!.pushMessageType = result.userInfo!.pushMessageType;
      Account.loginAccount!.feedVisableType = result.userInfo!.feedVisableType;
      Account.loginAccount!.feedBikeType = result.userInfo!.feedBikeType;
      Account.loginAccount!.rideDataType = result.userInfo!.rideDataType;
      Account.loginAccount!.kolStatus = result.userInfo?.kolStatus ?? 0;
      Account.loginAccount!.bikeNum = result.userInfo?.bikeNum ?? 2;
      Account.loginAccount!.faceUrl = result.userInfo!.faceUrl;
      Account.loginAccount!.nick = result.userInfo!.nick;
      SpUtil.putString('loginAccount', jsonEncode(Account.loginAccount));
      var account = Account.loginAccount!.clone();
      account.bikes = localAccount.bikes;
      account.defaultbike = localAccount.defaultbike;
      var event = AccountUpdated(account);
      BlocProvider.of<AccountBloc>(context).add(event);

      List<Brand> newBrand = await BikeApi(MyHttp.dio)
          .getBrandList(bikePartNo: BikeConfig.BIKE_PART_TYPE_1);
      account.bikes!.forEach((bike) {
        newBrand.forEach((brand) {
          if (bike.brandName == brand.brandName) {
            bike.brandLogo = brand.brandLogo;
          }
        });
      });
    }).catchError((error) {
      print("catchError ");
      if (error is DioException) {
        showToast(error.message.toString(), context);
      }
      _refreshController.refreshCompleted();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountBloc, Account>(builder: (context, account) {
      return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: Visibility(
                visible: !widget.showMyBike,
                child: IconButton(
                  icon: Image.asset(
                    DImages.formatPathPng(
                      'jiantou_left_white',
                    ),
                    height: 17,
                    width: 17,
                    color: DColor.ff232323,
                  ),
                  onPressed: () {
                    FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                    Navigator.pop(context, false);
                  },
                )),
            title: Text(widget.showMyBike ? "我的车辆" : "选择车辆",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: SafeArea(
          bottom: false,
          child: SmartRefresher(
            onRefresh: () {
              _refreshData(account);
            },
            enablePullDown: true,
            enablePullUp: false,
            controller: _refreshController,
            child: SingleChildScrollView(
              child: Container(
                color: DColor.white,
                padding: EdgeInsets.all(15),
                child: Column(
                  children: [
                    Visibility(
                      visible: widget.showMyBike,
                      child: _buildMyBike(account),
                    ),
                    _buildList(account),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildMyBike(Account localAccount) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Padding(
          padding: EdgeInsets.only(left: 15, top: 15),
          child: Text('我的自行车',
              style: TextStyle(
                  color: DColor.ff232323,
                  fontFamily: "PF",
                  fontSize: 16,
                  fontWeight: FontWeight.normal))),
      SizedBox(
        height: 10,
      ),
      BicycleBannerWidget(context, localAccount),
      SizedBox(
        height: 15,
      )
    ]);
  }

  Widget _buildList(Account localAccount) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('浏览自行车',
            style: TextStyle(
                color: DColor.ff232323,
                fontFamily: "PF",
                fontSize: 16,
                fontWeight: FontWeight.normal)),
        SizedBox(
          height: 6,
        ),
        //车架品牌
        _brand.isNotEmpty
            ? _listWidget()
            : buildLoadStateWidget(_loadStatus, () {
                _refreshData(localAccount);
              }, emptyTip: "暂无数据~"),
        // otherBrandWidget() //其他品牌
      ],
    );
  }

  Widget otherBrandWidget() {
    return Container(
      alignment: Alignment.centerRight,
      child: MaterialButton(
        onPressed: () {
          pushPage(context, OtherFrameBrandPage());
        },
        padding: EdgeInsets.all(5),
        child: Text('其他品牌',
            style: TextStyle(
                color: DColor.hitText,
                fontSize: 14,
                fontWeight: FontWeight.normal)),
      ),
    );
  }

  Widget _listWidget() {
    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(), // 禁止GridView滚动
      itemCount: _brand.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: 2,
      ),
      itemBuilder: (BuildContext context, int index) {
        return BrandWidget(
          brand: _brand[index],
        );
      },
    );
  }
}

class BrandWidget extends StatelessWidget {
  Brand? brand;

  BrandWidget({Key? key, this.brand}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    pushToSeriesPage(bool ishave) async {
      await pushPage(
          context,
          BikeFrameSeriesPage(
            brand: brand,
            isHaveTriathlon: ishave,
          ));
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () async {
        try {
          showLoadingDialog(context);
          List<Series> newSeries = await BikeApi(MyHttp.dio).getSeriesList(
              brandId: brand!.brandId,
              partType: BikeConfig.BIKE_PART_TYPE_1,
              bikeType: BikeConfig.TRIATHLON_OF_BIKE_TYPE,
              frontNo: 0,
              frontId: 0,
              frontId2: 0);

          await pushToSeriesPage(newSeries.isNotEmpty);
        } on DioException catch (e) {
          pushToSeriesPage(true);
        } finally {
          Navigator.pop(context);
        }
      },
      child: Container(
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: DColor.white,
          borderRadius: BorderRadius.all(Radius.circular(4)),
          boxShadow: [
            BoxShadow(
              color: Color(0xFF000000).withOpacity(0.06),
              spreadRadius: 1, // 调整阴影扩散
              blurRadius: 4, // 调整模糊半径
              offset: Offset(0, 2), // 调整阴影偏移，使其四周均匀分布
            ),
          ],
        ),
        padding: EdgeInsets.all(5),
        child: Align(
          // color: DColor.fffb691d,
          // height: 12,
          child: CachedNetworkImage(
            alignment: Alignment.center,
            imageUrl: brand?.brandLogo ?? '',
            errorWidget: (context, url, error) => Text(brand?.brandName ?? '',
                style: TextStyle(color: DColor.hitText, fontSize: 12)),
            height: 14,
            color: DColor.ff242424,
            fit: BoxFit.scaleDown,
          ),
        ),
      ),
    );
  }
}
