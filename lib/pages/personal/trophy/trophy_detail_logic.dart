import 'package:get/get.dart';
import 'package:new_edge/io/trophy.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/trophy_info.dart';
import 'package:new_edge/pages/base/simple_controller.dart';
import 'package:new_edge/request/MyHttp.dart';

class TrophyDetailLogic
    extends SimpleController<ApiResponse<TrophyDetail>, TrophyDetail> {
  final String? trophyRecordId = Get.arguments['trophyRecordId'] as String;

  TrophyDetailLogic() {}

  @override
  Future<ApiResponse<TrophyDetail>> loadData() {
    return TrophyApi(MyHttp.dio).getRecord(id: trophyRecordId);
  }

  @override
  TrophyDetail? getValue(ApiResponse<TrophyDetail>? m) {
    return m?.result;
  }
}
