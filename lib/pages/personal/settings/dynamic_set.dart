import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';

import 'package:new_edge/io/user.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/pages/personal/settings/settings.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/modal_pop_utils.dart';
import 'package:new_edge/util/toast.dart';

class DynamicSetPage extends StatefulWidget {
  @override
  _DynamicSetPageState createState() => _DynamicSetPageState();
}

class _DynamicSetPageState extends State<DynamicSetPage> {
  bool _feedBikeType = Account.loginAccount!.feedBikeType == 0;
  String? _mVisible;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    setState(() {
      _mVisible = Account.loginAccount!.feedVisableType == 0
          ? 'all'
//          : Account.loginAccount.feedVisableType == 1
//          ? '好友可见'
          : 'onlyself';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DColor.white,
      appBar: AppBar(
        title: Text("动态设置",
            style: TextStyle(
                color: DColor.ff242424, fontSize: 18, fontFamily: "PF")),
        centerTitle: true,
        leading: IconButton(
            icon: Image.asset(
              DImages.formatPathPng(
                'jiantou_left_white',
              ),
              height: 17,
              width: 17,
              color: DColor.ff242424,
            ),
            onPressed: () => Navigator.pop(context)),
      ),
      body: Container(
        width: double.infinity,
        padding: EdgeInsets.all(15),
        child: Column(
          children: [
            SettItem(
              title: '数据动态默认可见性',
              content: _mVisible == "all"
                  ? "公开"
                  : _mVisible == "onlyself"
                      ? "私密"
                      : "好友可见",
              onPressed: () async {
                ModalPopUtils.showModalPop(context, (value) async {
                  try {
                    await UserApi(MyHttp.dio).updateFeedVisableType(
                        feedVisableType: value == "all"
                            ? 0
                            : value == "onlyself"
                                ? 2
                                : 1);
                    setState(() {
                      _mVisible = value;
                    });
                    _mVisible == "all"
                        ? Account.loginAccount!.feedVisableType = 0
                        : Account.loginAccount!.feedVisableType = 1;
                  } on DioException catch (e) {
                    showToast(e.message.toString(), context);
                  }
                }, modals: [
                  PopModal(
                      tag: "all", content: "所有人可见", color: DColor.ff242424),
                  // PopModal(
                  //     tag: "onlyfans",
                  //     content: "仅粉丝可见",
                  //     color: DColor.ff242424),
                  PopModal(
                      tag: "onlyself", content: "仅自己可见", color: DColor.ff242424)
                ]);
              },
            ),
            SwitchListTile(
              contentPadding: EdgeInsets.only(left: 0, right: 0),
              activeColor: DColor.fffb691d,
              onChanged: (value) async {
                try {
                  await UserApi(MyHttp.dio)
                      .updateFeedBikeType(feedBikeType: _feedBikeType ? 1 : 0);
                  if (_feedBikeType) {
                    Account.loginAccount!.feedBikeType = 1;
                  } else {
                    Account.loginAccount!.feedBikeType = 0;
                  }
                  setState(() {
                    _feedBikeType = !_feedBikeType;
                  });
                } on DioException catch (e) {
                  showToast(e.message.toString(), context);
                }
              },
              value: _feedBikeType,
              title: Text(
                "数据动态显示关联自行车",
                style: TextStyle(color: DColor.ff242424, fontSize: 15),
              ),
            )
          ],
        ),
      ),
    );
  }
}
