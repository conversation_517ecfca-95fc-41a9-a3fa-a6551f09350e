import 'package:cached_network_image/cached_network_image.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swiper_view/flutter_swiper_view.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/photo_alum.dart';

class SeePicturePage extends StatelessWidget {
  final List<PhotoAlum>? pic;
  final int? indexes;

  SeePicturePage({this.pic, this.indexes});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Swiper(
            index: indexes!,
            itemBuilder: (context, index) {
              return ExtendedImage.network(
                pic![index].imgUrl?? '',
                clearMemoryCacheWhenDispose: true,
                fit: BoxFit.fitWidth,
              );
              // return CachedNetworkImage(
              //   imageUrl: pic[index].imgUrl,
              //   fit: BoxFit.fitWidth,
              // );
            },
            loop: pic!.length != 1,
            itemCount: pic!.length,
            pagination: SwiperCustomPagination(
                builder: (BuildContext context, SwiperPluginConfig config) {
              return Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  height: 50,
                  margin: EdgeInsets.only(right: 13, bottom: 6),
                  alignment: Alignment.center,
                  child: Text(
                    "${config.activeIndex + 1}/${config.itemCount}",
                    style: TextStyle(
                        fontSize: 20,
                        color: Colors.white,
                        fontWeight: FontWeight.w500),
                  ),
                ),
              );
            }),
          ),
          Positioned(
            top: 34,
            left: 22,
            child: IconButton(
                icon: Image.asset(
                  DImages.formatPathPng(
                    'jiantou_left_white',
                  ),
                  height: 17,
                  width: 17,
                  color: DColor.ff242424,
                ),
                onPressed: () => Navigator.pop(context)),
          ),
        ],
      ),
    );
  }
}
