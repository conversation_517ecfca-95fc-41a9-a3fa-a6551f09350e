import 'dart:math';

import 'package:date_format/date_format.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:new_edge/app.dart';
import 'package:new_edge/bloc/account_bloc.dart';
import 'package:new_edge/channels/ufile_channel.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/config/string.dart';

import 'package:new_edge/io/user.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/pages/personal/qrcode/personal_qrcode_detail_page.dart';
import 'package:new_edge/pages/personal/settings/personal_info_name_edit.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/file_utils.dart';
import 'package:new_edge/util/image_utils.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/modal.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/avatar.dart';
import 'package:new_edge/widgets/city_modal.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/date_modal.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:intl/intl.dart';

class EdgeIdEditPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _EdgeIdEditPageState();
  }
}

class _EdgeIdEditPageState extends State<EdgeIdEditPage> {
  final _controller = TextEditingController();
  final _fousNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountBloc, Account>(builder: (context, account) {
      return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
              ),
              onPressed: () => Navigator.pop(context, false)),
        ),
        body: Container(
            padding: EdgeInsets.only(left: 20, right: 20),
            color: DColor.white,
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 40),
                Text(
                  '填写新的一直号',
                  style: TextStyle(
                      color: DColor.ff242424,
                      fontSize: 22,
                      fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 20),
                Padding(
                    padding: EdgeInsets.only(left: 20, right: 20),
                    child: Text(
                      textAlign: TextAlign.center,
                      '一直号长度限6-20位，建议避免包含姓名、生日等涉及个人隐私信息。',
                      style: TextStyle(color: DColor.ff242424, fontSize: 15),
                    )),
                SizedBox(height: 30),
                _buildNameEdit(),
                SizedBox(height: 80),
                Container(
                    width: 300,
                    child: SubmitButton(
                        enable: _controller.text.length > 5,
                        text: "确认",
                        onPressed: () {
                          showLoadingDialogWithFuture(
                                  context,
                                  UserApi(MyHttp.dio)
                                      .updateEdgeId(edgeId: _controller.text))
                              .then((result) {
                            if (result.isSuccess()) {
                              BuildContext context =
                                  navigatorKey.currentState!.overlay!.context;

                              var account = Account.loginAccount!.clone();

                              String edgeIdLatestEditTime = DateFormat('yyyy.MM.dd HH:mm')
                                  .format(DateTime.now());
                              account.edgeId = _controller.text;
                              account.edgeIdLatestEditTime = edgeIdLatestEditTime;

                              var event = AccountUpdated(account);

                              BlocProvider.of<AccountBloc>(context).add(event);
                              Account.updateAccount(account);
                              showToast("保存成功", context);
                              Navigator.pop(context);
                            } else {
                              showToast(result.msg ?? "", context);
                            }
                          });
                        })),
              ],
            )),
      );
    });
  }

  Widget _buildNameEdit() {
    return CommonCard(
      padding: EdgeInsets.only(left: 5, right: 5),
      shadowColor: Color(0xFFEEEEEE),
      child: TextField(
          controller: _controller,
          focusNode: _fousNode,
          keyboardType: TextInputType.text,
          cursorWidth: 2,
          autofocus: true,
          cursorColor: DColor.primary,
          cursorRadius: Radius.circular(1),
          onChanged: (text) {
            setState(() {

            });
          },
          maxLength: 20,
          style: TextStyle(fontSize: 14, color: DColor.ff242424),
          decoration: InputDecoration(
              counterText: "",
              focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: DColor.white, width: 0.5)),
              enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: DColor.fffbfbfb, width: 0.5)),
              hintText: "请输入您的一直号",
              hintStyle: TextStyle(fontSize: 14, color: DColor.hitText),
              prefixIcon: Material(
                color: Colors.transparent,
                child: MaterialButton(
                    onPressed: () {},
                    child: Text(
                      '一直号',
                      style: TextStyle(color: DColor.ff808080, fontSize: 14),
                    )),
              ),
              contentPadding: EdgeInsets.only(left: 11, right: 11))),
    );
  }
}
