import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/util/login_utils.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/config/string.dart';
import 'package:new_edge/model/route_info.dart';
import 'package:new_edge/pages/route/route_detail.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/string_format_utils.dart';

class RouteItem extends StatefulWidget {
  final RouteInfo? route;

  const RouteItem({Key? key, this.route}) : super(key: key);

  @override
  _RouteItemState createState() => _RouteItemState();
}

class _RouteItemState extends State<RouteItem> {
  @override
  Widget build(BuildContext context) {
    var sizeHeight = (MediaQuery.of(context).size.width - 30) * 0.38;
    return GestureDetector(
      onTap: () async {
        LoginUtils.setILogin(() {
          pushPage(
              context,
              RouteDetailPage(
                routeInfo: widget.route,
              ));
        }, context);
      },
      child: Container(
        // padding: EdgeInsets.only(top: 10),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Container(
            color: DColor.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  child: Stack(
                    children: [
                      Image.asset(
                        DImages.formatPathPng('img_placeholder_bg'),
                        width: MediaQuery.of(context).size.width,
                        height: sizeHeight,
                        fit: BoxFit.cover,
                      ),
                      CachedNetworkImage(
                        imageUrl: '${widget.route!.coverImg}',
                        width: MediaQuery.of(context).size.width,
                        height: sizeHeight,
                        fit: BoxFit.cover,
                      ),
                      Visibility(
                        visible: widget.route!.verifyType == 1 ? true : false,
                        child: Positioned(
                          right: 14,
                          top: 14,
                          child: ClipRRect(
                            child: Container(
                              height: 20,
                              alignment: Alignment.center,
                              padding: EdgeInsets.only(left: 8, right: 8),
                              child: Row(
                                children: [
                                  Image.asset(
                                    DImages.formatPathPng('img_vip'),
                                    width: 12,
                                    height: 12,
                                    fit: BoxFit.fill,
                                  ),
                                  SizedBox(
                                    width: 4,
                                  ),
                                  Text(
                                    DString.official_certification,
                                    style: TextStyle(
                                        color: DColor.white, fontSize: 11),
                                  )
                                ],
                              ),
                              color: DColor.fffb691d,
                            ),
                            borderRadius: BorderRadius.circular(400),
                          ),
                        ),
                      )
                    ],
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(10, 0, 0, 0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text('${widget.route!.routeName}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                color: DColor.ff242424,
                                fontSize: 14,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding:
                      EdgeInsets.only(left: 10, top: 0, right: 5, bottom: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Image.asset(
                        DImages.formatPathPng('icon_route_meter'),
                        width: 12,
                        height: 12,
                        fit: BoxFit.fill,
                      ),
                      Text(
                        ' ${formatMToKm(widget.route!.routeMeter)}km  ',
                        style: TextStyle(
                          color: DColor.ff797979,
                          fontSize: 13,
                        ),
                      ),
                      Image.asset(
                        DImages.formatPathPng('icon_route_climb'),
                        width: 12,
                        height: 12,
                        fit: BoxFit.fill,
                      ),
                      Text(
                        ' ${widget.route!.routeClimb == 0 ? '' : '${widget.route!.routeClimb}m'}',
                        style: TextStyle(
                          color: DColor.ff797979,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  String formatDistance(int distance) {
    if (distance == -1 || distance == 0) {
      return '';
    }
    if (distance >= 1000) {
      return '距起点${formatMToKm(distance)}KM';
    }
    return '距起点${distance}M';
  }
}
