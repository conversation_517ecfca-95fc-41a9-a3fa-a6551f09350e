import 'package:cached_network_image/cached_network_image.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/config/string.dart';

import 'package:new_edge/io/route.dart';
import 'package:new_edge/model/ride_record.dart';
import 'package:new_edge/model/route_checkInfo.dart';
import 'package:new_edge/model/route_info.dart';
import 'package:new_edge/pages/route/route_chart.dart';
import 'package:new_edge/pages/route/route_detail.dart';
import 'package:new_edge/pages/route/route_map.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/string_format_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/submit_button.dart';

class RouteChallengePage extends StatefulWidget {
  RideRecord? record;
  RouteInfo? routeInfo;

  RouteChallengePage({Key? key, this.record, this.routeInfo}) : super(key: key);

  @override
  _RouteChallengePageState createState() => _RouteChallengePageState();
}

class _RouteChallengePageState extends State<RouteChallengePage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    loadData();
  }

  loadData() async {
    try {
      showLoadingDialog(context);

      RouteInfo detail = await RouteApi(MyHttp.dio).getUserRouteCheckinInfoByPostRide(
          routeId: widget.routeInfo?.routeId,
          postRideId: widget.routeInfo?.postRideId);
      setState(() {
        widget.routeInfo = detail;
      });

      RouteApi(MyHttp.dio)
          .getRoutePositionDetail(routeId: widget.routeInfo?.routeId)
          .then((detail) {
        setState(() {
          widget.routeInfo!.coverImg = detail.coverImg;
          widget.routeInfo!.lightSquareCoverImg = detail.lightSquareCoverImg;
        });
      }).catchError((error) {
        if (error is DioException) {}
      });
    } on DioException catch (e) {
      showToast(e.message.toString(), context);
    } finally {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "${widget.routeInfo!.routeName}",
          style: TextStyle(color: DColor.ff242424, fontSize: 18),
        ),
      ),
      body: SafeArea(
        child: buildWidget(),
      ),
    );
  }

  Widget buildWidget() {
    var sizeHeight = MediaQuery.of(context).size.width * 0.65;
    return Container(
      height: MediaQuery.of(context).size.height,
      color: DColor.ffF6F6F6,
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(0, 10, 0, 15),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 15, right: 15),
                    child: Stack(
                      children: [
                        GestureDetector(
                          onTap: () {
                            pushPage(
                                context,
                                RouteMapPage.fromRouteInfo(
                                  routeInfo: widget.routeInfo,
                                ));
                          },
                          child: Container(
                            child: ClipRRect(
                              child: CachedNetworkImage(
                                imageUrl:
                                    '${widget.routeInfo?.coverImg ?? ''}',
                                errorWidget: (context, url, error) =>
                                    Image.asset(
                                        DImages.formatPathPng(
                                          'img_placeholder_bg',
                                        ),
                                        width:
                                            MediaQuery.of(context).size.width,
                                        height: sizeHeight,
                                        fit: BoxFit.fill),
                                width: MediaQuery.of(context).size.width,
                                height: sizeHeight,
                                fit: BoxFit.fill,
                              ),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                        Visibility(
                          visible:
                              widget.routeInfo!.verifyType == 1 ? true : false,
                          child: Positioned(
                            right: 14,
                            top: 14,
                            child: ClipRRect(
                              child: Container(
                                height: 20,
                                alignment: Alignment.center,
                                padding: EdgeInsets.only(left: 8, right: 8),
                                child: Row(
                                  children: [
                                    Image.asset(
                                      DImages.formatPathPng('img_vip'),
                                      width: 12,
                                      height: 12,
                                      fit: BoxFit.fill,
                                    ),
                                    SizedBox(
                                      width: 4,
                                    ),
                                    Text(
                                      DString.official_certification,
                                      style: TextStyle(
                                          color: DColor.white, fontSize: 11),
                                    )
                                  ],
                                ),
                                color: DColor.fffb691d,
                              ),
                              borderRadius: BorderRadius.circular(400),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.fromLTRB(15, 0, 15, 0),
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: DColor.white,
                      borderRadius: BorderRadius.all(Radius.circular(4)),
                    ),
                    child: Column(
                      children: [
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    RideDataView(
                                      title: "里程",
                                      data: (widget.record!.meter ?? 0.0) == 0
                                          ? '--'
                                          : (widget.record!.meter! / 1000.0)
                                              .toStringAsFixed(1),
                                      unit: "KM",
                                    ),
                                    RideDataView(
                                      title: "运动均速",
                                      data: (widget.record!.speed ?? 0.0) == 0
                                          ? '--'
                                          : formatMSToKMH(widget.record!.speed!),
                                      unit: "KM/H",
                                    ),
                                  ]),
                              // SizedBox(
                              //   height: 17,
                              // ),
                              Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    RideDataView(
                                      title: "运动时间",
                                      data: formatRideSeconds(
                                          widget.record!.second!),
                                      unit: "",
                                    ),
                                    RideDataView(
                                      title: "累计爬升",
                                      data: widget.record!.climb!
                                          .toStringAsFixed(1),
                                      unit: "M",
                                    ),
                                  ])
                            ]),
                        Container(
                          child: SubmitButton(
                              text: '查看路段排行',
                              onPressed: () {
                                pushPage(
                                    context,
                                    RouteDetailPage(
                                        routeInfo: widget.routeInfo));
                              }),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      Container(
                        margin: EdgeInsets.fromLTRB(30,15,15,15),
                        width: 30,
                        alignment: Alignment.center,
                        child: Text('计圈',
                            style: TextStyle(
                                color: DColor.ff242424,
                                fontSize: 14,
                                fontWeight: FontWeight.normal)),
                      ),
                      SizedBox(width: 50,),
                      Expanded(
                        flex: 98,

                        child: Text('用时',
                            style: TextStyle(
                                color: DColor.ff242424,
                                fontSize: 14,
                                fontWeight: FontWeight.normal)),
                      ),
                      Container(
                        margin: EdgeInsets.fromLTRB(15,15,30,15),
                        alignment: Alignment.center,
                        width: 65,
                        child: Text('均速',
                            style: TextStyle(
                                color: DColor.ff242424,
                                fontSize: 14,
                                fontWeight: FontWeight.normal)),
                      ),
                    ],
                  ),
                  (widget.routeInfo != null &&
                          widget.routeInfo?.userRouteCheckinInfoList != null &&
                          widget.routeInfo!.userRouteCheckinInfoList!.length > 0)
                      ? ListView.builder(
                          shrinkWrap: true, //为true可以解决子控件必须设置高度的问题
                          physics: NeverScrollableScrollPhysics(), //禁用滑动事件
                          itemCount: widget
                              .routeInfo?.userRouteCheckinInfoList?.length,
                          itemBuilder: (BuildContext context, int index) {
                            return PerformanceItemView(
                              record: widget.record,
                              route: widget
                                  .routeInfo?.userRouteCheckinInfoList![index],
                              itemPosition: index,
                            );
                          })
                      : FitLoadEmpty(
                          tip: '暂无数据~',
                          image: DImages.formatPathPng('img_record_empty')),
                  SizedBox(
                    height: 50,
                  ),
                ],
              ),
            ),
          ),
          // Positioned(
          //   bottom: 0,
          //   left: 0,
          //   right: 0,
          //   child: Container(
          //     height: 49,
          //     child: SubmitButton(
          //         text: '查看路段排行',
          //         onPressed: () {
          //           pushPage(
          //               context, RouteDetailPage(routeInfo: widget.routeInfo));
          //         }),
          //   ),
          // )
        ],
      ),
    );
  }
}

class PerformanceItemView extends StatefulWidget {
  RideRecord? record;
  RouteCheckInfo? route;
  int? itemPosition;

  PerformanceItemView({Key? key, this.record, this.route, this.itemPosition})
      : super(key: key);

  @override
  _PerformanceItemViewState createState() => _PerformanceItemViewState();
}

class _PerformanceItemViewState extends State<PerformanceItemView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 5, left: 15, right: 15),
      child: Stack(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              pushPage(
                  context,
                  RouteChartPage(
                    rideRecord: widget.record,
                    routeCheckInfo: widget.route,
                    itemPosition: widget.itemPosition,
                  ));
            },

            child:  Container(
              height: 45,
              decoration: BoxDecoration(
                color: DColor.white,
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              child:

            Row(
              children: [
                Container(
                  width: 30,
                  margin: EdgeInsets.fromLTRB(15,0,15,0),
                  alignment: Alignment.center,
                  child: Text('${widget.itemPosition! + 1}',
                      style: TextStyle(
                          color: DColor.ff242424,
                          fontSize: 20,
                          fontFamily: "BN",
                          fontWeight: FontWeight.w500 )),
                ),
                SizedBox(width: 50,),
                Expanded(
                  flex: 98,

                  child: Text('${formatRideSeconds(widget.route?.routeSecond ?? 0)}',
                      style: TextStyle(
                          color: DColor.ff242424,
                          fontSize: 20,
                          fontFamily: "BN",
                          fontWeight: FontWeight.w500)),
                ),
                Container(
                  margin: EdgeInsets.fromLTRB(15, 0, 15, 0),
                  alignment: Alignment.center,
                  width: 65,
                  child: Text('${widget.route!.averageVelocity!.toStringAsFixed(1) + 'KM/H' }',
                      style: TextStyle(
                          color: DColor.ff242424,
                          fontSize: 20,
                          fontFamily: "BN",
                          fontWeight: FontWeight.w500)),
                ),
              ],
            )
              ,)

          ,
          ),
          // Positioned(
          //   left: 0,
          //   top: 0,
          //   child: Container(
          //     padding: EdgeInsets.only(left: 5, right: 5, top: 3, bottom: 0),
          //     decoration: BoxDecoration(
          //       image: DecorationImage(
          //           fit: BoxFit.fill,
          //           image: AssetImage(DImages.formatPathPng('xuhao'))),
          //     ),
          //     child: Container(
          //       child: Center(
          //         child: Text(
          //           '${widget.itemPosition + 1}',
          //           style: TextStyle(
          //               color: DColor.white,
          //               fontSize: 14,
          //               fontFamily: "DIN",
          //               fontWeight: FontWeight.bold),
          //           textAlign: TextAlign.center,
          //         ),
          //       ),
          //     ),
          //   ),
          // )
        ],
      ),
    );
  }

  Widget buildTitleView(String data, String title) {
    return Column(
      children: [
        Text(
          data,
          style: TextStyle(
              fontSize: 39,
              color: DColor.ff242424,
              fontFamily: "DIN",
              fontWeight: FontWeight.bold),
        ),
        Text(
          title,
          style: TextStyle(
              fontSize: 12,
              color: DColor.ff242424,
              fontWeight: FontWeight.normal),
        ),
      ],
    );
  }
}

//骑行数据距离、运动速度、运动时间等显示组件
class RideDataView extends StatelessWidget {
  final String? data;
  final String? unit;
  final String? title;

  // final String icon;

  const RideDataView({Key? key, this.data, this.unit, this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // width: 135,
      child: Row(
        children: [
          // SizedBox(width: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title!,
                style: TextStyle(
                    fontSize: 14,
                    color: DColor.ff808080,
                    fontWeight: FontWeight.normal),
              ),
              SizedBox(height: 5),
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: data!,
                      style: TextStyle(
                        fontSize: 30,
                        color: DColor.ff242424,
                        fontFamily: "DIN",
                      ),
                    ),
                    TextSpan(
                      text: unit!,
                      style: TextStyle(
                        fontSize: 20,
                        color: DColor.ff242424,
                        fontFamily: "DIN",
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
