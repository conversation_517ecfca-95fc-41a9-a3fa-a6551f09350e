import 'package:get/get.dart';
import 'package:new_edge/io/user.dart';
import 'package:new_edge/model/search_info.dart';
import 'package:new_edge/pages/base/simple_list_controller.dart';
import 'package:new_edge/request/MyHttp.dart';

class SearchRiderListLogic extends SimpleListController<SearchInfo> {
  var searchKey = "".obs;

  Future<void> search(String key) async{
    searchKey.value = key;
    if (key.isNotEmpty) {
      refreshData();
    }
  }

  Future<List<SearchInfo>> loadUserRecommends() {
    return UserApi(MyHttp.dio).getUserRecommend();
  }

  @override
  Future<List<SearchInfo>> loadData() async {
    if (searchKey.value.isEmpty) {
      return [];
    }
    return UserApi(MyHttp.dio).getSearchInfo(info: searchKey.value);
  }

  @override
  bool autoRefresh() {
    return false;
  }
}
