import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:get/get.dart';
import 'package:new_edge/bloc/account_bloc.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/search_info.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/personal/qrcode/personal_qrcode_detail_logic.dart';
import 'package:new_edge/pages/personal/qrcode/personal_qrcode_detail_page.dart';
import 'package:new_edge/pages/route/search_rider/contact_rider_logic.dart';
import 'package:new_edge/pages/route/search_rider/search_rider_item.dart';
import 'package:new_edge/util/dialog.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:permission_handler/permission_handler.dart';

class ContactRiderPage extends StatefulWidget {
  ContactRiderPage() {}

  @override
  _ContactRiderPageState createState() => _ContactRiderPageState();
}

class _ContactRiderPageState extends RefreshState<ContactRiderPage> {
  final logic = Get.put(ContactRiderLogic());
  final list = Get.find<ContactRiderLogic>().list;

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  void initState() {
    super.initState();

    logic.refreshData();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return _buildContent();
  }

  Widget _buildContent() {
    return Container(
        padding: EdgeInsets.all(20),
        child: FutureBuilder<PermissionStatus>(
          future: Permission.contacts.request(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              final status = snapshot.data!;
              if (status.isGranted) {
                return Obx(() => _buildContactContent());
              } else if (status.isDenied || status.isPermanentlyDenied) {
                return _buildPermissionRequest();
              }
            }
            return _buildPermissionRequest();
          },
        ));
  }

  Widget _buildPermissionRequest() {
    return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(DImages.formatPathPng("bg_contact")), // 替换为您的图片路径
            fit: BoxFit.fitWidth,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text("连接通讯录",
                style: TextStyle(
                    fontSize: 25,
                    color: DColor.ff232323,
                    fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Padding(
                padding: EdgeInsets.only(
                  left: 30,
                  right: 30,
                ),
                child: Text("您的好友正在使用EDGE一直，通过连接你的手机联系人查看他们的活动。",
                    style: TextStyle(fontSize: 17, color: DColor.ff232323))),
            SizedBox(
              height: 25,
            ),
            SizedBox(
              width: 300,
              child: SubmitButton(
                  text: "安全连接",
                  textSize: 16,
                  onPressed: () async {
                    PermissionStatus status =
                        await Permission.contacts.request();
                    if (status == PermissionStatus.granted) {
                      //TODO
                      setState(() {});
                    } else {
                      showPlatformAlertDialog(
                        context,
                        positiveText: "去授权",
                        title: "缺少权限",
                        content: "无法查看好友信息, 请到系统设置中开启联系人权限.",
                        onPositive: () async {
                          openAppSettings();
                        },
                      );
                    }
                  }),
            )
          ],
        ));
  }

  Widget _buildContactContent() {
    if (list.isEmpty) {
      return buildLoadStateWidgetFullEmpty(
        logic.loadStatus.value,
        () {
          refreshController.requestRefresh();
        },
        "bg_contact",
        emptyTip: "没有匹配到任何联系人",
      );
    }
    return buildRefreshWidget(
        enablePullUp: false,
        builder: () => _buildContactRiderList(),
        onRefresh: () {
          logic.refreshData();
        },
        refreshController: refreshController);
  }

  Widget _buildContactRiderList() {
    return Container(
      color: DColor.white,
      child: CustomScrollView(
        slivers: [
          SliverList(
            delegate:
                SliverChildBuilderDelegate((BuildContext context, int index) {
              SearchInfo item = list[index];
              return SearchRiderItem(item);
            }, childCount: list.length),
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
