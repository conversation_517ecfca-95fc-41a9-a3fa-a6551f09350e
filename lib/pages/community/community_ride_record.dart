import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/config/string.dart';

import 'package:new_edge/io/ride.dart';
import 'package:new_edge/model/community_ride_record.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/modal.dart';
import 'package:new_edge/util/string_format_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/title_unit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class CommunityRideRecordPage extends StatefulWidget {
  @override
  _CommunityRideRecordPageState createState() =>
      _CommunityRideRecordPageState();
}

class _CommunityRideRecordPageState extends State<CommunityRideRecordPage>
    with SingleTickerProviderStateMixin {
  static final Animatable<double> _easeInTween =
      CurveTween(curve: Curves.easeIn);
  static final Animatable<double> _halfTween =
      Tween<double>(begin: 0.0, end: 0.5);
  Duration _kExpand = Duration(milliseconds: 200);

  // bool _isExpanded = false;
  late AnimationController _controller;
  RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  var _networkReachbility = true;
  var _loadStatus = FitLoadStatus.loading;
  var _pageNum = 1;
  var _pageSize = 20;
  List<CommunityRideRecord> rideRecord = [];
  int selectPosition = 0;
  List<int> yearList = [];

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = AnimationController(duration: _kExpand, vsync: this);
    _iconTurns = _controller.drive(_halfTween.chain(_easeInTween));
    loadYears();
  }

  // void _handleTap() {
  //   if (_isExpanded) {
  //     _controller.forward();
  //   } else {
  //     _controller.reverse().then<void>((void value) {
  //       if (!mounted) return;
  //       setState(() {
  //         // Rebuild without widget.children.
  //       });
  //     });
  //   }
  // }

  late Animation<double> _iconTurns;

  void _selectBodyYear() {
    // setState(() {
    //   _isExpanded=true;
    // });
    showBodyYearSelectModal(
      context,
      initialData: yearList[selectPosition],
      years: yearList,
      onDataChanged: (value) {
        for (int i = 0; i < yearList.length; i++) {
          if (yearList[i] == value) {
            setState(() {
              // _isExpanded=false;
              selectPosition = i;
            });
            refreshData();
            break;
          }
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // _handleTap();
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "选择数据",
          style: TextStyle(color: DColor.ff242424, fontSize: 18),
        ),
        leading: IconButton(
            icon: Image.asset(
              DImages.formatPathPng(
                'jiantou_left_white',
              ),
              height: 17,
              width: 17,
              color: DColor.ff232323,
            ),
            onPressed: () => Navigator.pop(context)),
        actions: [
          yearList.isNotEmpty
              ? TextButton(
                  onPressed: () {
                    _selectBodyYear();
                    // _handleTap();
                  },
                  child: Row(
                    children: [
                      Text(
                        "${yearList[selectPosition]}年",
                        style: TextStyle(
                          color: DColor.ff232323,
                          fontSize: 14,
                        ),
                      ),
                      SizedBox(
                        width: 5,
                      ),
                      RotationTransition(
                        turns: _iconTurns,
                        child: Image.asset(
                          color: Colors.black,
                          DImages.formatPathPng('icon_down'),
                          width: 9,
                          height: 9,
                          fit: BoxFit.cover,
                        ),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                    ],
                  ))
              : TextButton(
                  onPressed: () {},
                  child: Row(
                    children: [
                      Text(
                        "${DateTime.now().year}年",
                        style: TextStyle(color: DColor.ff232323),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                    ],
                  ),
                )
        ],
      ),
      body: _refreshWidget(),
    );
  }

  Widget _refreshWidget() {
    return Container(
      color: DColor.ffF6F6F6,
      child: SmartRefresher(
        onRefresh: refreshData,
        onLoading: loadMore,
        enablePullDown: true,
        enablePullUp: true,
        footer: CustomFooter(
          builder: (BuildContext? context, LoadStatus? mode) {
            Widget body;
            TextStyle commonTextStyle = TextStyle(
              color: DColor.ff232323,
              fontSize: 12,
            );
            if (mode == LoadStatus.idle) {
              body = Text("");
            } else if (mode == LoadStatus.loading) {
              body = CupertinoActivityIndicator();
            } else if (mode == LoadStatus.failed) {
              body = Text(
                "获取数据失败",
                style: commonTextStyle,
              );
            } else if (mode == LoadStatus.canLoading) {
              body = Text("", style: commonTextStyle);
            } else {
              body = Text(
                '',
                style: commonTextStyle,
              );
            }
            return Container(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        child: rideRecord.length > 0
            ? buildList()
            : _networkReachbility
                ? _buildLoadStateWidget()
                : FitLoadError(
                    icon: DImages.formatPathPng('img_network_error'),
                    msg: " 当前网络不可用",
                    tip: " ",
                    onPressedRefresh: refreshData),
      ),
    );
  }

  //绘制表格
  Widget buildList() {
    return ListView.builder(
      itemCount: rideRecord.length,
      itemBuilder: (BuildContext context, int index) {
        return Padding(
          padding: EdgeInsets.only(left: 15, right: 15, top: 8, bottom: 0),
          child: RideRecordItemForChoose(
            record: rideRecord[index],
            recordSelect: (bean) {
              Navigator.pop(context, bean);
            },
          ),
        );
      },
    );
  }

  Widget _buildLoadStateWidget() {
    if (yearList.isEmpty || _loadStatus == FitLoadStatus.loadSuccess) {
      return FitLoadEmpty(
        tip: '暂时没有骑行记录',
        image: DImages.formatPathPng('img_record_empty'),
      );
    } else if (_loadStatus == FitLoadStatus.loadError) {
      return FitLoadError(
          icon: DImages.formatPathPng('img_record_empty'),
          msg: "暂时没有骑行记录",
          tip: " ",
          onPressedRefresh: refreshData);
    } else {
      return Container();
    }
  }

  void loadYears() async {
    var result = await Connectivity().checkConnectivity();
    if (result.contains(ConnectivityResult.none)) {
      showToast("你的网络不给力，请稍后重试", context);
      setState(() {
        _networkReachbility = false;
      });
      _refreshController.refreshCompleted();
      return;
    } else {
      setState(() {
        _loadStatus = FitLoadStatus.loading;
        _networkReachbility = true;
      });
    }
    try {
      List<int> datas = await RideApi(MyHttp.dio).getRideYearStatList();
      setState(() {
        yearList = datas;
      });
      refreshData();
    } on DioException catch (e) {
      setState(() {
        _loadStatus = FitLoadStatus.loadError;
      });
      // showToast(e.message.toString(), context);
      _refreshController.refreshCompleted();
    }
  }

  void refreshData() async {
    var result = await Connectivity().checkConnectivity();
    if (result.contains(ConnectivityResult.none)) {
      showToast("你的网络不给力，请稍后重试", context);
      setState(() {
        _networkReachbility = false;
      });
      _refreshController.refreshCompleted();
      return;
    } else {
      setState(() {
        _loadStatus = FitLoadStatus.loading;
        _networkReachbility = true;
      });
    }
    setState(() {
      _pageNum = 1;
    });
    try {
      List<CommunityRideRecord> newData = await RideApi(MyHttp.dio)
          .getRideBriefListByYear(
              pageNum: _pageNum,
              pageSize: _pageSize,
              year: yearList.isNotEmpty
                  ? yearList[selectPosition]
                  : DateTime.now().year);
      setState(() {
        rideRecord = newData;
        _loadStatus = FitLoadStatus.loadSuccess;
      });
      if (newData.length < _pageSize) {
        _refreshController.loadNoData();
      } else {
        _refreshController.resetNoData();
      }
      _refreshController.refreshCompleted();
    } on DioException catch (e) {
      setState(() {
        _loadStatus = FitLoadStatus.loadError;
      });
      // showToast(e.message.toString(), context);
      _refreshController.refreshCompleted();
    }
  }

  void loadMore() async {
    try {
      List<CommunityRideRecord> newData = await RideApi(MyHttp.dio)
          .getRideBriefListByYear(
              pageNum: (_pageNum + 1),
              pageSize: _pageSize,
              year: yearList.isNotEmpty
                  ? yearList[selectPosition]
                  : DateTime.now().year);
      ;
      if (newData.isEmpty || newData.length == 0) {
        _refreshController.loadNoData();
      } else {
        setState(() {
          rideRecord.addAll(newData);
          _pageNum++;
        });
        if (newData.length < _pageSize) {
          _refreshController.loadNoData();
        }
        _refreshController.loadComplete();
      }
    } on DioException catch (e) {
      showToast(e.message.toString(), context);
      _refreshController.loadFailed();
    }
  }
}

//动态骑行记录 路线、速度、日期时间等数据显示组件
class RideRecordItemForChoose extends StatelessWidget {
  CommunityRideRecord? record;
  ValueChanged<CommunityRideRecord?>? recordSelect;

  RideRecordItemForChoose({Key? key, this.record, this.recordSelect})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CommonCard(
      padding: EdgeInsets.zero,
      color: Colors.white,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (recordSelect != null) {
            recordSelect!(record);
          }
        },
        child: Container(
          height: 85,
          alignment: Alignment.center,
          width: MediaQuery.of(context).size.width,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 126,
                alignment: Alignment.center,
                child: (record?.coverImg ?? "").length > 0
                    ? CachedNetworkImage(
                        imageUrl: record!.coverImg!,
                        errorWidget: (context, url, error) => Image.asset(
                          DImages.formatPathPng('img_placeholder_bg'),
                          height: 86,
                          width: 126,
                          fit: BoxFit.fitWidth,
                        ),
                        height: 86,
                        width: 126,
                        fit: BoxFit.fitWidth,
                      )
                    : Image.asset(
                        record!.type == null || record!.type == 0
                            ? "assets/images/img_placeholder_bg.png"
                            : "assets/images/img_indoor_placeholder.png",
                        fit: BoxFit.cover,
                      ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            '${formatDateForWhenTime(record?.startTime)} 骑行',
                            style: TextStyle(
                              color: DColor.ff232323,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        verticalDirection: VerticalDirection.down,
                        children: [
                          Expanded(
                            // Expanded包一下，可以撑满剩余空间
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: CommunityEditRideDataView(
                                title: "里程",
                                data: (record!.meter ?? 0.0) == 0
                                    ? '--'
                                    : (record!.meter! / 1000.0)
                                        .toStringAsFixed(1),
                                unit: "KM",
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 1,
                            height: 15,
                            child: DecoratedBox(
                              decoration: BoxDecoration(color: Colors.grey),
                            ),
                          ),
                          SizedBox(width: 15),
                          Expanded(
                            // Expanded包一下，可以撑满剩余空间
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: CommunityEditRideDataView(
                                title: "爬升",
                                data: (record!.climb ?? 0.0) == 0
                                    ? '--'
                                    : record!.climb!.toStringAsFixed(0),
                                unit: "M",
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //   children: [
          //     Container(
          //       width: 126,
          //       alignment: Alignment.center,
          //       child: (record?.coverImg ?? "").length > 0
          //           ? CachedNetworkImage(
          //         imageUrl: record.coverImg,
          //         errorWidget: (context, url, error) => Image.asset(
          //           DImages.formatPathPng('img_placeholder_bg'),
          //           height: 85,
          //           width: 126,
          //           fit: BoxFit.fitWidth,
          //         ),
          //         height: 85,
          //         width: 126,
          //         fit: BoxFit.fitWidth,
          //       )
          //           : Image.asset(
          //         record.type == null || record.type == 0
          //             ? "assets/images/img_placeholder_bg.png"
          //             : "assets/images/img_indoor_placeholder.png",
          //         fit: BoxFit.cover,
          //       ),
          //     ),
          //     Expanded(
          //       child: Padding(
          //         padding: EdgeInsets.only(
          //           left: 10,
          //           right: 10,
          //           top: 10,
          //           bottom: 10,
          //         ),
          //         child: Column(
          //           crossAxisAlignment: CrossAxisAlignment.center,
          //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //           children: [
          //
          //             Row(
          //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //               crossAxisAlignment: CrossAxisAlignment.center,
          //               mainAxisSize: MainAxisSize.max,
          //               verticalDirection: VerticalDirection.down,
          //               children: [
          //                 Expanded(
          //                   // Expanded包一下，可以撑满剩余空间
          //                   child: Align(
          //                     alignment: Alignment.centerLeft,
          //                     child: RideDataView(
          //                       title: "里程",
          //                       data: (record.meter ?? 0.0) == 0
          //                           ? '--'
          //                           : (record.meter / 1000.0)
          //                           .toStringAsFixed(1),
          //                       unit: " km",
          //                     ),
          //                   ),
          //                 ),
          //                 Container(
          //                   width: 1,
          //                   height: 17,
          //                   color: DColor.ff808080,
          //                 ),
          //                 Expanded(
          //                   // Expanded包一下，可以撑满剩余空间
          //                   child: Align(
          //                     alignment: Alignment.centerLeft,
          //                     child: Padding(
          //                       padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
          //                       child: RideDataView(
          //                         title: "爬升",
          //                         data: (record.climb ?? 0.0) == 0
          //                             ? '--'
          //                             : record.climb.toStringAsFixed(0),
          //                         unit: "m",
          //                       ),
          //                     ),
          //                   ),
          //                 ),
          //               ],
          //             ),
          //           ],
          //         ),
          //       ),
          //     ),
          //   ],
          // ),
        ),
      ),
    );
  }
}

//动态骑行记录 路线、速度、日期时间等数据显示组件
class RideRecordItem extends StatelessWidget {
  CommunityRideRecord? record;
  ValueChanged<CommunityRideRecord?>? recordSelect;

  RideRecordItem({Key? key, this.record, this.recordSelect}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 6, 0, 0),
      child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              if (recordSelect != null) {
                recordSelect!(record);
              }
            },
            child: Container(
              height: 60,
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width,
              color: DColor.ffF6F6F6,
              child: Row(
                children: [
                  Container(
                    width: 88,
                    alignment: Alignment.center,
                    child: (record?.coverImg ?? "").length > 0
                        ? CachedNetworkImage(
                            imageUrl: record!.coverImg!,
                            errorWidget: (context, url, error) => Image.asset(
                              DImages.formatPathPng('img_placeholder_bg'),
                              height: 60,
                              width: 88,
                              fit: BoxFit.fitWidth,
                            ),
                            height: 60,
                            width: 88,
                            fit: BoxFit.fitWidth,
                          )
                        : Image.asset(
                            record!.type == null || record!.type == 0
                                ? "assets/images/img_placeholder_bg.png"
                                : "assets/images/img_indoor_placeholder.png",
                            fit: BoxFit.cover,
                          ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(
                        right: 10,
                        top: 10,
                        bottom: 10,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisSize: MainAxisSize.max,
                              verticalDirection: VerticalDirection.down,
                              children: [
                                Expanded(
                                  // Expanded包一下，可以撑满剩余空间
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Padding(
                                      padding: EdgeInsets.fromLTRB(13, 0, 0, 0),
                                      child: RideDataView(
                                        title: "里程",
                                        data: (record!.meter ?? 0.0) == 0
                                            ? '--'
                                            : (record!.meter! / 1000.0)
                                                .toStringAsFixed(1),
                                        unit: " km",
                                      ),
                                    ),
                                  ),
                                ),
                                Container(
                                  width: 1,
                                  height: 17,
                                  color: DColor.ff808080,
                                ),
                                Expanded(
                                  // Expanded包一下，可以撑满剩余空间
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Padding(
                                      padding: EdgeInsets.fromLTRB(15, 0, 0, 0),
                                      child: RideDataView(
                                        title: "爬升",
                                        data: (record!.climb ?? 0.0) == 0
                                            ? '--'
                                            : record!.climb!.toStringAsFixed(0),
                                        unit: "m",
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )),
    );

// return
//     CommonCard(
//         padding: EdgeInsets.only(left: 5, top: 5, bottom: 5, right: 15),
//         child: Row(children: [
//           ClipRRect(
//             borderRadius: BorderRadius.circular(5),
//             child: Container(
//               height: 85,
//               width: 125,
//               child:  CachedNetworkImage(imageUrl: record!.coverImg!),
//             ),
//           ),
//           SizedBox(
//             width: 10,
//           ),
//           Expanded(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               children: [
//                 Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
//                   Expanded(
//                       child: Text(
//                         formatDateForBriefTime(record!.startTime ?? 0),
//                         style: TextStyle(
//                             fontSize: 14,
//                             color: DColor.ff242424,
//                             fontWeight: FontWeight.normal),
//                       )),
//                   // Visibility(
//                   //     child: Image.asset(
//                   //       DImages.formatPathPng('selected'),
//                   //       height: 15,
//                   //       width: 15,
//                   //       fit: BoxFit.cover,
//                   //     ))
//                 ]),
//                 SizedBox(
//                   height: 30,
//                 ),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     _buildData("", formatMToKm(record!.meter!) ?? "", "km"),
//                     _buildData("", record!.climb?.toString() ?? "0", "m"),
//                     _buildData("", formatSecToMin(record!.second) ?? "", "min"),
//                   ],
//                 ),
//               ],
//             ),
//           )
//         ]));
  }
}


Widget _buildData(String title, String value, String unit) {
  return Text.rich(
    TextSpan(
      children: [
        TextSpan(
          text: title,
          style: TextStyle(fontSize: 12, color: DColor.ff808080),
        ),
        TextSpan(
          text: " $value",
          style: TextStyle(
              fontSize: 20,
              color: DColor.ff242424,
              fontFamily: "DIN",
              fontWeight: FontWeight.normal),
        ),
        TextSpan(
          text: " $unit",
          style: TextStyle(
              fontSize: 14,
              color: DColor.ff242424,
              fontFamily: "DIN",
              fontWeight: FontWeight.normal),
        ),
      ],
    ),
  );
}

//骑行记录详情显示
class RideRecordBigItem extends StatelessWidget {
  CommunityRideRecord? record;

  RideRecordBigItem({Key? key, this.record}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var sizeHeight = (MediaQuery.of(context).size.width - 30) / 1.468;

    return Container(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: EdgeInsets.only(left: 0, right: 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              //骑行数据展示，包括里程、爬升、时间等信息
              Container(
                padding: EdgeInsets.only(top: 10, bottom: 10),
                decoration: BoxDecoration(
                  color: DColor.ffF6F6F6,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Expanded(
                      // Expanded包一下，可以撑满剩余空间
                      child: RideDataMapView(
                        title: "里程",
                        data: (record?.meter ?? 0.0) == 0
                            ? '--'
                            : ((record?.meter ?? 0) / 1000.0)
                                .toStringAsFixed(1),
                        unit: " km",
                      ),
                    ),

                    // RideDataView(
                    //   title: "运动均速",
                    //   data: (record.speed ?? 0.0) == 0
                    //       ? '--'
                    //       : formatMSToKMH(record.speed),
                    //   unit: "KM/H",
                    // ),
                    Container(
                      width: 1,
                      height: 17,
                      color: DColor.ff808080,
                    ),

                    Expanded(
                      // Expanded包一下，可以撑满剩余空间
                      child: RideDataMapView(
                        title: "爬升",
                        data: (record?.climb ?? 0.0) == 0
                            ? '--'
                            : record?.climb?.toStringAsFixed(0),
                        unit: "m",
                      ),
                    ),

                    Container(
                      width: 1,
                      height: 17,
                      color: DColor.ff808080,
                    ),
                    Expanded(
                      // Expanded包一下，可以撑满剩余空间
                      child: RideDataMapView(
                        title: "时间",
                        data: formatRideSeconds(record?.second??0),
                        unit: "",
                      ),
                    ),
                  ],
                ),
              ),
              ClipRRect(
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(4),
                    bottomRight: Radius.circular(4)),
                child: Stack(
                  children: [
                    (record?.coverImg ?? "").isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: record?.coverMapImg ?? "",
                            errorWidget: (context, url, error) => Image.asset(
                              DImages.formatPathPng('img_placeholder_bg_large'),
                              width:
                                  (MediaQuery.of(context).size.width - 30) * 1,
                              height: (MediaQuery.of(context).size.width - 30) *
                                  0.66,
                              fit: BoxFit.cover,
                            ),
                            width: (MediaQuery.of(context).size.width - 30) * 1,
                            height:
                                (MediaQuery.of(context).size.width - 30) * 0.66,
                            fit: BoxFit.cover,
                          )
                        : Image.asset(
                            record?.type == null || record?.type == 0
                                ? "assets/images/img_placeholder_bg_large.png"
                                : "assets/images/img_map_placeholder.png",
                            fit: BoxFit.fitWidth,
                          ),
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: (record?.logo != null && record?.logo != '')
                          ? CachedNetworkImage(
                              imageUrl: record?.logo ?? '',
                              errorWidget: (context, url, error) => Image.asset(
                                DImages.formatPathPng('img_logo_empty'),
                                height: 18,
                                fit: BoxFit.fitHeight,
                              ),
                              height: 18,
                              fit: BoxFit.fitHeight,
                            )
                          : Container(),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

//骑行数据显示组件
class RideDataView extends StatelessWidget {
  final String? data;
  final String? unit;
  final String? title;

  const RideDataView({Key? key, this.data, this.unit, this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        title!,
        textAlign: TextAlign.left,
        style: TextStyle(
            fontSize: 13,
            color: DColor.ff808080,
            fontFamily: "PF",
            fontWeight: FontWeight.normal),
      ),
      SizedBox(width: 10),
      Text.rich(TextSpan(children: [
        TextSpan(
          text: data!,
          style: TextStyle(
              fontSize: 18,
              color: DColor.ff242424,
              fontFamily: "DIN",
              fontWeight: FontWeight.normal),
        ),
        TextSpan(
          text: unit!,
          style: TextStyle(
              fontSize: 13,
              color: DColor.ff242424,
              fontFamily: "DIN",
              fontWeight: FontWeight.normal),
        )
      ])),
    ]));
  }
}

//骑行数据显示组件
class RideDataMapView extends StatelessWidget {
  final String? data;
  final String? unit;
  final String? title;

  const RideDataMapView({Key? key, this.data, this.unit, this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(left: 13),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            title!,
            textAlign: TextAlign.left,
            style: TextStyle(
                fontSize: 14,
                color: DColor.ff808080,
                fontFamily: "PF",
                fontWeight: FontWeight.normal),
          ),
          SizedBox(height: 2),
          Text.rich(TextSpan(children: [
            TextSpan(
              text: data!,
              style: TextStyle(
                  fontSize: 19,
                  color: DColor.ff242424,
                  fontFamily: "DIN",
                  fontWeight: FontWeight.bold),
            ),
            TextSpan(
              text: unit!,
              style: TextStyle(
                  fontSize: 19,
                  color: DColor.ff242424,
                  fontFamily: "DIN",
                  fontWeight: FontWeight.bold),
            )
          ])),
        ]));
  }
}

class RideDataIconView extends StatelessWidget {
  final String? data;
  final String? unit;
  final String? icon;

  const RideDataIconView({Key? key, this.data, this.unit, this.icon})
      : super(key: key);

  Widget itemRichText() {
    return Text.rich(
      TextSpan(children: [
        TextSpan(
          text: data!,
          style: TextStyle(
              fontSize: 17,
              color: DColor.hitText,
              fontFamily: "DIN",
              fontWeight: FontWeight.w500),
        ),
        TextSpan(
          text: unit!,
          style: TextStyle(
              fontSize: 9,
              color: DColor.hitText,
              fontFamily: "DIN",
              fontWeight: FontWeight.w500),
        )
      ]),
      textAlign: TextAlign.center,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 20,
        alignment: Alignment.center,
        child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                icon!,
                height: 10,
                width: 10,
                fit: BoxFit.fill,
              ),
              SizedBox(
                height: 2,
              ),
            ],
          ),
          SizedBox(width: 5),
          itemRichText()
        ]));
  }
}

class CommunityEditRideDataView extends StatelessWidget {
  final String? data;
  final String? unit;
  final String? title;

  const CommunityEditRideDataView({Key? key, this.data, this.unit, this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
      Text(
        title!,
        textAlign: TextAlign.left,
        style: TextStyle(
            fontSize: 12,
            color: DColor.ff808080,
            fontFamily: "PF",
            fontWeight: FontWeight.normal),
      ),
      SizedBox(width: 10),
      Text.rich(TextSpan(children: [
        TextSpan(
          text: data!,
          style: TextStyle(
              fontSize: 18,
              color: DColor.ff242424,
              fontFamily: DFamily.dinBold,
              fontWeight: FontWeight.bold),
        ),
        TextSpan(
          text: "  $unit",
          style: TextStyle(
              fontSize: 14,
              color: DColor.ff242424,
              fontFamily: DFamily.dinBold),
        )
      ])),
    ]));
  }
}
