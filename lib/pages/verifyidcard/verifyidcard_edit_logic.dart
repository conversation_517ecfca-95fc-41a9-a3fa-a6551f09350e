import 'dart:convert';

import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:new_edge/bloc/account_bloc.dart';
import 'package:new_edge/io/home.dart';
import 'package:new_edge/io/user.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/api_response/api_edge_response_entity.dart';
import 'package:new_edge/model/country_info.dart';
import 'package:new_edge/model/entry_form_msg.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/sp_utils.dart';
import 'package:new_edge/widgets/load_state.dart';

import 'verifyidcard_edit_state.dart';

class VerifyIdCardEditLogic extends GetxController {
  final VerifyIdCardEditState state = VerifyIdCardEditState();

  var loadStatus = FitLoadStatus.loading.obs;
  var verifySuccess = false.obs;

  void refreshData() async {
    EntryFormMsg? responseData;

    loadStatus.value = FitLoadStatus.loading;
    try {
      responseData = (await _loadData()).info;

      if (responseData != null) {
        state.localItem.value = responseData.copyWith();
        state.remoteItem.value = responseData.copyWith();
      }

      /// 刷新完成
      loadStatus.value = FitLoadStatus.loadSuccess;
    } catch (error) {
      print("出错 $error");
      loadStatus.value = FitLoadStatus.loadError;
    }
  }

  Future<ApiEdgeResponse<EntryFormMsg>> _loadData() {
    return UserApi(MyHttp.dio).getEntryFormMsg();
  }

  void notifyAccountUpdate(BuildContext context) {
    Account.loginAccount!.idType = state.localItem.value.idType;
    Account.loginAccount!.idNumber = state.localItem.value.idNumber;
    Account.loginAccount!.firstName = state.localItem.value.firstName;
    Account.loginAccount!.lastName = state.localItem.value.lastName;
    if (state.showNationality()) {
      Account.loginAccount!.nationality = state.localItem.value.countryCode;
    }
    if (state.showBirthday()) {
      Account.loginAccount!.birthday = state.localItem.value.birthday;
    }
    if (state.showGender()) {
      Account.loginAccount!.gender = state.localItem.value.gender;
    }

    SpUtil.putString('loginAccount', jsonEncode(Account.loginAccount));
    var account = Account.loginAccount!.clone();
    var event = AccountUpdated(account);
    BlocProvider.of<AccountBloc>(context).add(event);
  }

  void updateAgree(bool isAgree) {
    state.isAgree.value = isAgree;
  }

  void updateCardNo(String card) {
    state.localItem.update((val) {
      val?.idNumber = card;
    });
  }

  void updateFirstName(String name) {
    state.localItem.update((val) {
      val?.firstName = name;
    });
  }

  void updateLastName(String name) {
    state.localItem.update((val) {
      val?.lastName = name;
    });
  }

  void updateCardType(CardType type) {
    state.localItem.update((val) {
      val?.idType = type.number;
    });
  }

  void updateBirthday(DateTime birthday) {
    state.localItem.update((val) {
      val?.birthday = formatDate(birthday, [yyyy, mm, dd]);
    });
  }

  void updateGender(GenderType gender) {
    state.localItem.update((val) {
      val?.gender = gender.number;
    });
  }

  void updateCountryInfo(CountryInfo countryInfo) {
    state.localItem.update((val) {
      val?.countryCode = countryInfo.countryCode;
      val?.countryChName = countryInfo.countryChName;
      val?.countryEnName = countryInfo.countryEnName;
    });
  }

  void updateBloodType(String title) {
    state.localItem.update((val) {
      val?.blood = title;
    });
  }

  void updateEmail(String email) {
    state.localItem.update((val) {
      val?.email = email;
    });
  }

  void updateWeight(int weight) {
    state.localItem.update((val) {
      val?.weight = weight;
    });
  }

  void updateHeight(int height) {
    state.localItem.update((val) {
      val?.height = height;
    });
  }

  void updateUrgentName(String name) {
    state.localItem.update((val) {
      val?.emergencyContact = name;
    });
  }

  void updateUrgentMobile(String mobile) {
    state.localItem.update((val) {
      val?.ecMobile = mobile;
    });
  }

  void updateUrgentAreaCode(String code) {
    state.localItem.update((val) {
      val?.ecAreaCode = code;
    });
  }

  void updateUrgentType(String title) {
    state.localItem.update((val) {
      val?.ecRelationship = title;
    });
  }

  Future<ApiEdgeResponse?> authentication(bool isUpdate) {
    EntryFormMsg item = state.localItem.value;
    return UserApi(MyHttp.dio)
        .updateEntryFormMsg(
            idType: isUpdate
                ? null
                : (CardType.getType(item.idType) ?? CardType.IdCard).number,
            idNumber: isUpdate ? null : item.idNumber,
            firstName: isUpdate ? null : item.firstName,
            lastName: isUpdate ? null : item.lastName,
            birthday: state.showBirthday() ? item.birthday : null,
            gender: state.showGender()
                ? (GenderType.getType(item.gender) ?? GenderType.Man).number
                : null,
            countryCode: state.showNationality() ? item.countryCode : null,
            countryEnName: state.showNationality() ? item.countryEnName : null,
            countryChName: state.showNationality() ? item.countryChName : null,
            email: item.email,
            emergencyContact: item.emergencyContact,
            ecAreaCode: item.ecAreaCode ?? "+86",
            ecMobile: item.ecMobile,
            ecRelationship: item.ecRelationship,
            blood: item.blood,
            weight: item.weight,
            height: item.height)
        .then((value) {
      if (value.isSuccess()) {
        state.remoteItem.value = state.localItem.value.copyWith();
        state.verifySuccess.value = true;
      }
      return value;
    });
  }
}
