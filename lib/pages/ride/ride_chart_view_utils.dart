import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/model/ride_record_chart.dart';
import 'package:new_edge/util/string_format_utils.dart';

LineChartData buildChartDataView(
  RideRecordChart primaryChart,
  ValueChanged<int>? valueChanged, {
  List<int> showingIndicators = const [],
  RideRecordChart? secondaryChart,
  double? minX,
  double? maxX,
}) {
  LineChartBarData primaryChartData = LineChartBarData(
    showingIndicators: showingIndicators,
    spots: primaryChart.spots,
    color: primaryChart.lineColor,
    barWidth: 0,
    isStrokeCapRound: false,
    isCurved: true,
    dotData: FlDotData(
      show: false,
    ),
    belowBarData: BarAreaData(
      show: true,
      color: primaryChart.lineColor,
    ),
  );
  LineChartBarData? secondaryChartData;
  if (secondaryChart != null) {
    double convertToPrimaryRange(double y) {
      return primaryChart.minY +
          ((y - secondaryChart.minY) /
                  (secondaryChart.maxY - secondaryChart.minY)) *
              (primaryChart.maxY - primaryChart.minY);
    }

    List<FlSpot> scaledSpots = secondaryChart.spots
        .map((spot) => FlSpot(spot.x, convertToPrimaryRange(spot.y)))
        .toList();

    secondaryChartData = LineChartBarData(
      showingIndicators: showingIndicators,
      spots: scaledSpots,
      color: secondaryChart.lineColor,
      barWidth: 2,
      isStrokeCapRound: false,
      dotData: FlDotData(
        show: false,
      ),
      belowBarData: BarAreaData(show: false),
    );
  }

  List<LineChartBarData> lineBarsData = [primaryChartData];
  if (secondaryChartData != null) {
    lineBarsData.add(secondaryChartData);
  }

  return LineChartData(
    showingTooltipIndicators: (showingIndicators ?? []).map((index) {
      if (index >= primaryChartData.spots.length) {
        index = primaryChartData.spots.length - 1;
      }
      List<LineBarSpot> showingSpots = [];
      showingSpots
          .add(LineBarSpot(primaryChartData, 0, primaryChartData.spots[index]));
      if (secondaryChartData != null) {
        showingSpots.add(LineBarSpot(
            secondaryChartData, 1, secondaryChartData.spots[index]));
      }
      return ShowingTooltipIndicators(showingSpots);
    }).toList(),
    /**
     * enabled 为true的时候，触摸会自动跟随
     * 如果设置了touchCallback，则enabled必须设置成false，否则不会固定tooltip(或者handleBuiltInTouches设置成false)
     * 但设置了touchCallback 会导致外部的GestureDetector中onScale,onLongPress等事件无法回调
     */
    lineTouchData: LineTouchData(
      enabled: valueChanged == null,
      touchCallback: valueChanged != null
          ? (event, response) {
              if (response == null || response.lineBarSpots == null) {
                return;
              }
              final spotIndex = response.lineBarSpots!.first.spotIndex;
              valueChanged(spotIndex);
            }
          : null,
      getTouchedSpotIndicator:
          (LineChartBarData barData, List<int> spotIndexes) {
        return spotIndexes.map((index) {
          return TouchedSpotIndicatorData(
            FlLine(
              color: barData.color,
            ),
            FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) =>
                  FlDotCirclePainter(
                radius: 4,
                color: barData.color!,
                strokeWidth: 1,
                strokeColor: Colors.black26,
              ),
            ),
          );
        }).toList();
      },
      touchTooltipData: LineTouchTooltipData(
        getTooltipColor: (group) => primaryChart.lineColor,
        tooltipRoundedRadius: 6,
        tooltipPadding: EdgeInsets.all(5),
        getTooltipItems: (List<LineBarSpot> lineBarsSpot) {
          return lineBarsSpot.map((lineBarSpot) {
            String getText(String tag, double y, String? unit) {
              String value =
                  (tag == "HeartRate" || tag == "Cadence" || tag == "Power")
                      ? (y ~/ 1).toString()
                      : y.toString();
              return "$value ${unit ?? ""}";
            }

            String content;
            if (lineBarSpot.barIndex == 0) {
              content = getText(
                  primaryChart.tag, lineBarSpot.y, primaryChart.yAxisUnit);
            } else {
              content = getText(
                  secondaryChart!.tag,
                  secondaryChart!.spots[lineBarSpot.spotIndex].y,
                  secondaryChart!.yAxisUnit);
            }
            return LineTooltipItem(
              "$content",
              TextStyle(color: DColor.white, fontSize: 12),
            );
          }).toList();
        },
      ),
    ),
    clipData: FlClipData.all(),
    gridData: FlGridData(
      horizontalInterval: primaryChart.horizontalInterval,
      verticalInterval: primaryChart.verticalInterval,
      show: false,
      drawVerticalLine: true,
      getDrawingHorizontalLine: (value) {
        if (value > primaryChart.maxY - primaryChart.horizontalInterval) {
          return FlLine(
            color: DColor.ffd9d9d9,
            strokeWidth: 1,
          );
        }
        return FlLine(
          color: DColor.ffd9d9d9,
          strokeWidth: 1,
        );
      },
      getDrawingVerticalLine: (value) {
        if (primaryChart.maxX - value < 0.1) {
          return FlLine(
            color: DColor.ffd9d9d9,
            strokeWidth: 1,
          );
        }
        return FlLine(
          color: DColor.ffd9d9d9,
          strokeWidth: 1,
        );
      },
    ),
    titlesData: FlTitlesData(
      show: true,
      topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
      rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
      leftTitles: AxisTitles(
          sideTitles: SideTitles(
        showTitles: true,
        interval: primaryChart.horizontalInterval,
        getTitlesWidget: (value, titleMeta) {
          String text = "";
          if (value == primaryChart.minY) {
            text = primaryChart.yAxisUnit ?? "";
          } else {
            text = value.toInt().toString();
          }
          return Padding(
              padding: EdgeInsets.only(top: 3.0),
              child: Text(text,
                  style: TextStyle(
                      color: Color(0xff747B81),
                      fontWeight: FontWeight.normal,
                      fontSize: 6)));
        },
      )),
      bottomTitles: AxisTitles(
          sideTitles: SideTitles(
        showTitles: true,
        interval: primaryChart.verticalInterval,
        getTitlesWidget: (value, titleMeta) {
          String text = "";
          if (primaryChart.xUnit == 'KM') {
            if (value == 0.0) {
              text = 'KM';
            } else {
              if (value.toInt() != value) {
                // 有小数
                text = value.toStringAsFixed(1);
              }
              text = value.toInt().toString();
            }
          } else {
            if (value == 0.0) {
              text = 'S';
            } else {
              text = formatRideSecondsForChart(value.floor());
            }
          }
          return Padding(
              padding: EdgeInsets.only(top: 3.0),
              child: Text(text,
                  style: TextStyle(
                      color: Color(0xff747B81),
                      fontWeight: FontWeight.normal,
                      fontSize: 6)));
        },
      )),
    ),
    borderData: FlBorderData(
        show: true,
        border: Border(
            left: BorderSide(color: Color(0xff37434d), width: 1),
            bottom: BorderSide(color: Color(0xff37434d), width: 1),
            right: BorderSide(color: Color(0xff37434d), width: 1))),
    minX: minX ?? primaryChart.minX,
    maxX: maxX ?? primaryChart.maxX,
    minY: primaryChart.minY,
    maxY: primaryChart.maxY * 1.1,
    extraLinesData: primaryChart.averageY == 0
        ? null
        : ExtraLinesData(horizontalLines: [
            HorizontalLine(
              y: primaryChart.averageY ?? 0,
              color: primaryChart.lineColor,
              strokeWidth: 1,
              // dashArray: [3, 3],
            ),
          ]),
    lineBarsData: lineBarsData,
  );
}
