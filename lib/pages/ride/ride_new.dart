import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';

import 'package:animations/animations.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:new_edge/bloc/ride_status_bloc.dart';
import 'package:new_edge/channels/map/ride_map.dart';
import 'package:new_edge/channels/sport_record_channel.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/constant/constant.dart';
import 'package:new_edge/db/ride_dio.dart';
import 'package:new_edge/io/amap.dart';

import 'package:new_edge/io/ride.dart';
import 'package:new_edge/io/sport.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/ride_record.dart';
import 'package:new_edge/model/ride_setting.dart';
import 'package:new_edge/model/sport_name.dart';
import 'package:new_edge/pages/common/developer_tools.dart';
import 'package:new_edge/pages/common/web_view.dart';
import 'package:new_edge/pages/ride/count_down.dart';
import 'package:new_edge/pages/ride/ride_detail.dart';
import 'package:new_edge/pages/ride/ride_setting.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/dialog.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/settings.dart';
import 'package:new_edge/util/string_format_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/slide_button.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:rxdart/rxdart.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class RideNewPage extends StatefulWidget {
  final bool showCountDown;
  final bool isRiding;

  const RideNewPage({
    Key? key,
    this.showCountDown = false,
    this.isRiding = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return RideNewPageState();
  }
}

class RideNewPageState extends State<RideNewPage> {
  late FlutterTts flutterTts;

  BehaviorSubject<String> _speedBehaviorSubject =
  BehaviorSubject<String>.seeded("0.0");
  BehaviorSubject<int?> _distanceBehaviorSubject =
  BehaviorSubject<int?>.seeded(0);
  BehaviorSubject<int?> _durationBehaviorSubject =
  BehaviorSubject<int?>.seeded(0);
  BehaviorSubject<String> _avgSpeedBehaviorSubject =
  BehaviorSubject<String>.seeded("0.0");
  BehaviorSubject<int?> _gpsBehaviorSubject = BehaviorSubject<int?>.seeded(0);
  TextEditingController textController1 = TextEditingController();

  SportRecord _sportRecord = SportRecord();

  late RideSetting _setting;

  RideMapController _mapController = RideMapController();

  Timer? _timer;

  var _isShowMap = false;
  var _isRiding = false;
  var _isStarted = false;

  // var _isPressingStop = false;
  int _lowGPSTime = 0; // 记录gps信号弱出现的时间
  int _tipLowGPSTime = 0; // 记录提示gps信号弱出现的时间
  late int initStatemilliseconds;
  bool _showCountDown = false;
  late bool _idRidingWhenOpened;
  SportName? _sportName;

  @override
  void initState() {
    super.initState();
    print('initState执行');
    initStatemilliseconds = new DateTime.now().millisecondsSinceEpoch;
    loadSportsName();
    initRideSetting();
    flutterTts = FlutterTts();
    setupScreenAlwaysOn(_setting);
    SportRecordChannel.setAutoPause(_setting.autoPause);
    SportRecordChannel.setAutoRide(DeveloperTools.isAutoRide());
    _showCountDown = widget.showCountDown;
    _idRidingWhenOpened = widget.isRiding;
    if (_idRidingWhenOpened) {
      startRide();
      setState(
            () {
          _isStarted = true;
          _idRidingWhenOpened = false;
        },
      );
    }
  }

  void loadSportsName() async {
    var result = await Connectivity().checkConnectivity();
    if (result.contains(ConnectivityResult.none)) {
      showToast("网络异常，请检查网络连接后重试", context);
      return;
    }
    showLoadingDialog(context);
    await SportApi(MyHttp.dio)
        .getSportName()
        .then((value) => setState(() {
      Navigator.pop(context);
      _sportName = value;
    }))
        .catchError((error) {
      Navigator.pop(context);
    });
  }

  @override
  void dispose() {
    flutterTts.stop();
    _mapController.dispose();
    // 如果离开页面,检查运动状态,如果不是骑行中就销毁
    SportRecordChannel.isStart().then((value) {
      if (!value) {
        SportRecordChannel.closeService();
      }
    });
    _timer?.cancel();
    super.dispose();
  }

  void startRide() {
    SportRecordChannel.start();
    Future.delayed(Duration(milliseconds: 200), () {
      print('startRide调用_loadSampleDataFormNaive');
      _loadSampleDataFormNaive();
      startTimer();
    });
  }

  void startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      // 如果不在当前页面就不刷新
      if (!mounted) {
        return;
      }
      // print('startTimer调用_loadSampleDataFormNaive');
      _loadSampleDataFormNaive();
    });
  }

  void initRideSetting() {
    String text = localSettings.getString(LocalSettings.RIDE_SETTING, null);
    if (text.isNotEmpty) {
      _setting = RideSetting.fromJson(jsonDecode(text));
    } else {
      _setting = RideSetting();
      _setting.audioTip = true;
      _setting.autoPause = true;
      _setting.screenAlwaysOn = false;
    }
  }

  void setupScreenAlwaysOn(RideSetting setting) {
    try {
      if (setting.screenAlwaysOn!) {
        WakelockPlus.enable();
      } else {
        WakelockPlus.disable();
      }
    } catch (e) {}
  }

  _loadSampleDataFormNaive() async {
    SportRecord? sportRecord = await SportRecordChannel.getSampleData();
    if (sportRecord == null) {
      return;
    }
    _sportRecord = sportRecord;
    final avgSpeed = sportRecord.second! > 0
        ? (sportRecord.meter! / 1000.0) / (sportRecord.second! / 3600.0)
        : 0;
    _avgSpeedBehaviorSubject.add(avgSpeed.toStringAsFixed(1));
    _distanceBehaviorSubject.add(sportRecord.meter);
    _durationBehaviorSubject.add(sportRecord.second);
    // print('_loadSampleDataFormNaive 200执行');
    if (sportRecord.second == 0) {
      _speedBehaviorSubject.add("0.0");
    } else {
      if (sportRecord.status == 1) {
        if (sportRecord.currentSpeed! >= 0) {
          _speedBehaviorSubject
              .add(sportRecord.currentSpeed!.toStringAsFixed(1));
        } else {
          // 丢点时显示
          _speedBehaviorSubject.add("--");
        }
      } else {
        _speedBehaviorSubject.add("0.0");
      }
    }
    _gpsBehaviorSubject.add(sportRecord.gpsAccuracy);
    // 处理gps提示
    _handerGPS(sportRecord.gpsAccuracy);
    // 自动暂停和恢复
    if (sportRecord.isPause() != _isRiding) {
      setState(() {
        _isRiding = sportRecord.isPause();
      });
    }
    var event = RideStatusUpdated(sportRecord.status);
    BlocProvider.of<RideStatusBloc>(context).add(event);
  }

  _handerGPS(int? hAccuracy) {
    bool isLowGPS = false;
    if (defaultTargetPlatform == TargetPlatform.android && hAccuracy == 0) {
      isLowGPS = true;
    } else if (defaultTargetPlatform == TargetPlatform.iOS &&
        hAccuracy! >= 100) {
      isLowGPS = true;
    }
    /*toast提示3秒&语音提示
      1.首次触发语音提示：信号为0的状态持续超过15秒时，语音提示GPS信号弱；持续状态未超过15秒时不提示；
      2.非首次触发语音提示：信号为0的状态持续超过15秒，且距离上次触发语音提示已超过2分钟时，再次语音提示；未超过2分钟的情况不提示；
    */
    if (isLowGPS) {
      var newLowGPSTime = DateTime.now().millisecondsSinceEpoch;
      if (_lowGPSTime == 0) {
        // 记录第一次出现的时间
        _lowGPSTime = newLowGPSTime;
        return;
      } else {
        if (newLowGPSTime - _lowGPSTime < 15 * 1000) {
          // 小于15秒，不提示
          return;
        } else {
          // 记录新的时间
          _lowGPSTime = newLowGPSTime;
          if (_tipLowGPSTime == 0) {
            // 第一次提示
            _tipLowGPS();
            _tipLowGPSTime = newLowGPSTime;
          } else {
            // 非首次触发语音提示，距离上次触发语音提示已超过2分钟时，再次语音提示
            if (newLowGPSTime - _tipLowGPSTime > 2 * 60 * 1000) {
              _tipLowGPS();
              _tipLowGPSTime = newLowGPSTime;
            }
          }
        }
      }
    }
  }

  _tipLowGPS() {
    if (_setting.audioTip!) {
      _audioTip();
    }
    showToast("当前信号弱，请至空旷地区定位", context);
  }

  Future _audioTip() async {
    try {
      flutterTts.setLanguage("zh-CN");
      await flutterTts.speak("当前信号弱，请至空旷地区定位");
    } catch (e) {
      print(e);
    }
  }

  Future<void> _handleStop() async {
    SportRecordChannel.pause();
    print('_handleStop调用_loadSampleDataFormNaive');
    _loadSampleDataFormNaive();
    print("_handleStop被触发");
    if (_isStarted == true) {
      if ((_sportRecord.meter! < 200 || _sportRecord.second! < 60) &&
          _isStarted == true) {
        var title = "";
        if (_sportRecord.meter! < 200) {
          title = "骑行距离太短无法保存记录，继续骑行吗？";
        } else {
          title = "骑行时间太短无法保存记录，继续骑行吗？";
        }
        showPlatformAlertDialog(
          context,
          title: title,
          negativeText: "继续骑行",
          positiveText: "立即结束",
          onNegative: () {
            SportRecordChannel.resume();
            Navigator.pop(context);
          },
          onPositive: () async {
            setState(() {
              _isStarted = false;
            });

            await SportRecordChannel.stop();
            await SportRecordChannel.reset();
            Navigator.pop(context);
            var event = RideStatusUpdated(0);
            BlocProvider.of<RideStatusBloc>(context).add(event);
            popToHome(context);
          },
        );
      } else {
        showPlatformAlertDialog(
          context,
          title: "确定结束骑行吗?",
          positiveText: "确定结束",
          negativeText: "继续骑行",
          onNegative: () {
            SportRecordChannel.resume();
            Navigator.pop(context);
          },
          onPositive: () async {
            RideRecord? rideRecord;
            setState(
                  () {
                _isStarted = false;
              },
            );
            try {
              showLoadingDialog(context, barrierDismissible: false);
              await SportRecordChannel.stop();

              var event = RideStatusUpdated(0);
              BlocProvider.of<RideStatusBloc>(context).add(event);

              rideRecord = await SportRecordChannel.getData();
              rideRecord.uid = Account.loginAccount!.uid;
              rideRecord.coverImg = "";
              await RideDio.saveRecordToDB(rideRecord);

              await SportRecordChannel.reset();

              // uplaod ride record
              await RideApi(MyHttp.dio).uploadRideRecord(
                meter: rideRecord.meter,
                second: rideRecord.second,
                maxSpeed: rideRecord.maxSpeed,
                climb: rideRecord.climb,
                startTime: rideRecord.startTime,
                endTime: rideRecord.endTime,
                sampleInterval: rideRecord.sampleInterval,
                rideId: rideRecord.rideId,
                province: rideRecord.province,
                city: rideRecord.city,
                content: rideRecord.content,
                altitude: rideRecord.altitude,
                pauseTime: rideRecord.pauseTime,
                locationDetail: rideRecord.locationDetail,
                wgs: rideRecord.wgs,
              );

              // update status
              await RideDio.updateRecordUploadStatus(
                  rideId: rideRecord.rideId, uploadStatus: true);
            } on DioException catch (e) {
              showToast(e.message.toString(), context);
            } catch (e) {
              showToast(e.toString(), context);
            } finally {
              pushPageAndPopToHome(
                  context,
                  RideDetailPage(
                    record: rideRecord,
                    style: RideDetailPageStyle.complete,
                  ),
                  fullscreenDialog: true);
            }
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_showCountDown) {
      return CountDownPage(
        sportName: _sportName,
        countDownFinished: () {
          setState(() {
            _showCountDown = false;
          });
          startRide();
        },
      );
    }
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Scaffold(
          extendBodyBehindAppBar: true,
          body: Container(
              color: Color(0xFFffffff),
              child: SafeArea(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            IconButton(
                              icon: Center(
                                child: Image.asset(
                                  "assets/images/icon_arrow_down.png",
                                  width: 44,
                                  height: 44,
                                ),
                              ),
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                            Expanded(
                              child: _sportName != null
                                  ? Container(
                                height: 60,
                                child: IconButton(
                                  padding: const EdgeInsets.all(0),
                                  icon: Image.network(
                                    _sportName?.photoPath ?? "",
                                    width: 130,
                                    height: 60,
                                  ),
                                  onPressed: () {
                                    if (_sportName?.jumpUrl != null &&
                                        _sportName!.jumpUrl!.isNotEmpty) {
                                      pushPage(
                                          context,
                                          WebViewPage(
                                            title: _sportName?.title ?? "",
                                            url: _sportName?.jumpUrl ?? "",
                                            showOption: true,
                                          ));
                                    }
                                  },
                                ),
                              )
                                  : Center(
                                child: RideStateWidget(
                                  visible: !_isStarted,
                                  isRiding: _isRiding,
                                ),

                                // Container(
                                //   height: 39,
                                //   child: ElevatedButton(
                                //     onPressed: () {},
                                //     shape: RoundedRectangleBorder(
                                //       borderRadius: BorderRadius.all(
                                //         Radius.circular(30),
                                //       ),
                                //     ),
                                //     child: Container(
                                //       child: Text(
                                //         _isRiding ? "骑行暂停中…" : "骑行进行中…",
                                //         style: TextStyle(
                                //           fontSize: 14,
                                //           color: DColor.white,
                                //         ),
                                //       ),
                                //     ),
                                //     elevation: 0,
                                //     color: DColor.fffb691d,
                                //     disabledColor: DColor.ff242424,
                                //     disabledElevation: 0,
                                //   ),
                                // ),
                              ),
                            ),
                            IconButton(
                                icon: Center(
                                    child: Image.asset(
                                      "assets/images/icon_setting_white.png",
                                      width: 44,
                                      height: 44,
                                    )),
                                onPressed: () async {
                                  _setting = await (pushPage(context, RideSettingPage()));
                                  setupScreenAlwaysOn(_setting);
                                  SportRecordChannel.setAutoPause(
                                      _setting.autoPause);
                                }),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 15),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(children: [
                                  Text(
                                    "GPS",
                                    style: TextStyle(
                                        fontSize: 9,
                                        color: DColor.ff242424,
                                        fontWeight: FontWeight.normal),
                                  ),
                                  SizedBox(width: 6),
                                  StreamBuilder<int?>(
                                      stream: _gpsBehaviorSubject,
                                      builder: (context, snapshot) {
                                        return GPSStatusView(
                                            accuracy: snapshot.data ?? 0);
                                      })
                                ]),
                              ]),
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "速度",
                                    style: TextStyle(
                                        fontSize: 16,
                                        color: DColor.ff808080,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  SizedBox(height: 14),
                                  StreamBuilder<String>(
                                      stream: _speedBehaviorSubject,
                                      builder: (context, snapshot) {
                                        return Text(
                                          snapshot.data ?? "0.0",
                                          style: TextStyle(
                                              fontSize: 120,
                                              color: DColor.ff242424,
                                              fontFamily: "DIN",
                                              fontWeight: FontWeight.bold),
                                        );
                                      }),
                                  Text(
                                    "KM/H",
                                    style: TextStyle(
                                        fontSize: 18,
                                        color: DColor.ff808080,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              Column(
                                // mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    padding: EdgeInsets.fromLTRB(45, 0, 45, 0),
                                    child: Stack(
                                      // mainAxisAlignment:
                                      //     MainAxisAlignment.spaceBetween,
                                      children: [
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: StreamBuilder<int?>(
                                            stream: _distanceBehaviorSubject,
                                            builder: (context, snapshot) {
                                              return RideDataView(
                                                data: ((snapshot.data ?? 0) / 1000)
                                                    .toStringAsFixed(2),
                                                unit: "里程(KM)",
                                              );
                                            },
                                          ),
                                        ),
                                        Align(
                                          alignment: Alignment.center,
                                          child: StreamBuilder<int?>(
                                            stream: _durationBehaviorSubject,
                                            builder: (context, snapshot) {
                                              return RideDataView(
                                                data: formatRideSeconds(
                                                    snapshot.data ?? 0),
                                                unit: "运动时间",
                                              );
                                            },
                                          ),
                                        ),
                                        Align(
                                          alignment: Alignment.centerRight,
                                          child: StreamBuilder<String>(
                                            stream: _avgSpeedBehaviorSubject,
                                            builder: (context, snapshot) {
                                              return RideDataView(
                                                data: snapshot.data ?? "0.0",
                                                unit: "均速(KM/H)",
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 25),
                                  Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        OpenContainer(
                                          transitionType:
                                          ContainerTransitionType.fade,
                                          openBuilder: (BuildContext context,
                                              VoidCallback _) {
                                            return RideMapView(
                                              mapController: _mapController,
                                              distanceBehaviorSubject:
                                              _distanceBehaviorSubject,
                                              avgSpeedBehaviorSubject:
                                              _avgSpeedBehaviorSubject,
                                              durationBehaviorSubject:
                                              _durationBehaviorSubject,
                                              sportName: _sportName,
                                            );
                                          },
                                          closedShape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                              Radius.circular(50),
                                            ),
                                          ),
                                          closedColor: DColor.ffF6F6F6,
                                          closedBuilder: (BuildContext context,
                                              VoidCallback openContainer) {
                                            return Container(
                                                width: 65,
                                                height: 65,
                                                child: Center(
                                                  child: Image.asset(
                                                    "assets/images/icon_map.png",
                                                    width: 46,
                                                    height: 46,
                                                  ),
                                                ));
                                          },
                                        ),
                                      ]),
                                ],
                              ),
                              Container(
                                margin: EdgeInsets.only(bottom: 20),
                                child: AnimatedCrossFade(
                                  //切换执行时间
                                  duration: const Duration(milliseconds: 500),
                                  //开始骑行
                                  firstChild: Column(
                                    mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                    children: [
                                      Container(
                                        height: 60,
                                        width: double.infinity,
                                        margin: EdgeInsets.fromLTRB(46, 0, 46, 0),
                                        child: SubmitButton(
                                            onPressed: () {
                                              setState(() {
                                                print("开始骑行按钮被触发");
                                                _isStarted = true;
                                              });
                                              presentPage(
                                                  context,
                                                  CountDownPage(
                                                    sportName: _sportName,
                                                    countDownFinished: () {
                                                      setState(() {
                                                        _showCountDown = false;
                                                      });
                                                      startRide();
                                                    },
                                                  ));
                                            },
                                            text:"开始骑行"
                                        ),
                                      ),
                                    ],
                                  ),

                                  //结束骑行按钮
                                  secondChild: Container(
                                    child: Column(
                                      mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Padding(
                                          padding:
                                          EdgeInsets.fromLTRB(46, 0, 46, 0),
                                          child: ClipRRect(
                                            child: SlideButton(
                                              height: 64,
                                              slidingChild: Align(
                                                alignment: Alignment.centerRight,
                                                child: Container(
                                                  padding: EdgeInsets.fromLTRB(
                                                      0, 0, 15, 0),
                                                  alignment: Alignment.centerRight,
                                                  child: new LayoutBuilder(builder:
                                                      (context, constraint) {
                                                    // return new Icon(Icons.access_alarms, size: constraint.biggest.height);
                                                    return new Icon(
                                                      Icons.arrow_forward_rounded,
                                                      color: DColor.white,
                                                      // size:28,
                                                      size: constraint
                                                          .biggest.height *
                                                          0.7,
                                                    );
                                                  }),
                                                ),
                                              ),
                                              backgroundChild: Center(
                                                child: Text(
                                                  "右滑结束运动",
                                                  style: TextStyle(
                                                      fontSize: 16,
                                                      color: DColor.ff4e4e4e),
                                                ),
                                              ),
                                              backgroundColor: DColor.fffef1e8,
                                              slidingBarColor: DColor.fffb691d,
                                              slideDirection: SlideDirection.RIGHT,
                                              onButtonOpened: () {
                                                print('按钮滑到底了');
                                              },
                                              onButtonClosed: () {
                                                print('onButtonClosed被调用');

                                                if (_isStarted &&
                                                    new DateTime.now()
                                                        .millisecondsSinceEpoch -
                                                        initStatemilliseconds >
                                                        500) {
                                                  _handleStop();
                                                }
                                              },
                                            ),
                                            borderRadius: BorderRadius.circular(50),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  //页面装填控制
                                  crossFadeState: _isStarted
                                      ? CrossFadeState.showSecond
                                      : CrossFadeState.showFirst,
                                ),
                              )
                            ],
                          ),
                        )
                      ])))),
    );
  }
}

class RideStateWidget extends StatelessWidget {
  final bool? visible;
  final bool? isRiding;

  const RideStateWidget({Key? key, this.visible, this.isRiding})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: Duration(milliseconds: 300),
      opacity: !visible! ? 1.0 : 0.0,
      child: Container(
        child: SubmitButton(
            onPressed: () {},
            text:isRiding! ? "骑行暂停中…" : "骑行进行中…"
        ),
      ),
    );
  }
}

class RideMapView extends StatelessWidget {
  final BehaviorSubject<int?>? distanceBehaviorSubject;
  final BehaviorSubject<int?>? durationBehaviorSubject;
  final BehaviorSubject<String>? avgSpeedBehaviorSubject;
  final RideMapController? mapController;
  final SportName? sportName;

  const RideMapView(
      {Key? key,
        this.distanceBehaviorSubject,
        this.durationBehaviorSubject,
        this.avgSpeedBehaviorSubject,
        this.mapController,
        this.sportName})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        RideMap(controller: mapController),
        Positioned(
          left: 15,
          right: 15,
          top: 8 + MediaQuery.of(context).padding.top,
          child: Container(
              decoration: BoxDecoration(
                  color: Color(0xffF3F5F7),
                  borderRadius: BorderRadius.circular(5),
                  boxShadow: [
                    BoxShadow(
                      offset: Offset(1, 2),
                      color: Color(0xFF000000).withOpacity(0.16),
                    )
                  ]),
              height: 80,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(mainAxisSize: MainAxisSize.min, children: [
                    StreamBuilder<int?>(
                        stream: distanceBehaviorSubject!,
                        builder: (context, snapshot) {
                          return Text(
                            ((snapshot.data ?? 0) / 1000).toStringAsFixed(2),
                            style: TextStyle(
                                fontSize: 30,
                                color: DColor.ff222332,
                                fontFamily: "DIN",
                                fontWeight: FontWeight.bold),
                          );
                        }),
                    Text(
                      "里程(KM)",
                      style: TextStyle(
                          fontSize: 12,
                          color: DColor.ff222332,
                          fontWeight: FontWeight.normal),
                    ),
                  ]),
                  Column(mainAxisSize: MainAxisSize.min, children: [
                    StreamBuilder<int?>(
                        stream: durationBehaviorSubject!,
                        builder: (context, snapshot) {
                          return Text(
                            formatRideSeconds(snapshot.data ?? 0)!,
                            style: TextStyle(
                                fontSize: 30,
                                color: DColor.ff222332,
                                fontFamily: "DIN",
                                fontWeight: FontWeight.bold),
                          );
                        }),
                    Text(
                      "运动时间",
                      style: TextStyle(
                          fontSize: 12,
                          color: DColor.ff222332,
                          fontWeight: FontWeight.normal),
                    ),
                  ]),
                  Column(mainAxisSize: MainAxisSize.min, children: [
                    StreamBuilder<String>(
                        stream: avgSpeedBehaviorSubject!,
                        builder: (context, snapshot) {
                          return Text(
                            snapshot.data ?? "0.0",
                            style: TextStyle(
                                fontSize: 30,
                                color: DColor.ff222332,
                                fontFamily: "DIN",
                                fontWeight: FontWeight.bold),
                          );
                        }),
                    Text(
                      "均速(KM/H)",
                      style: TextStyle(
                          fontSize: 12,
                          color: DColor.ff222332,
                          fontWeight: FontWeight.normal),
                    ),
                  ])
                ],
              )),
        ),
        Positioned(
          right: 10,
          bottom: 24,
          child: TextButton(
            // padding: EdgeInsets.all(10),
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Image.asset(
              "assets/images/guanbi_white.png",
              width: 40,
              height: 40,
            ),
          ),
        ),
        Visibility(
          visible: sportName != null,
          child: Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.only(bottom: 24),
                child: Container(
                  width: 130,
                  height: 60,
                  child: IconButton(
                      padding: const EdgeInsets.all(0),
                      icon: Image.network(sportName?.photoPath ?? ""),
                      onPressed: () {
                        if (sportName?.jumpUrl != null &&
                            sportName!.jumpUrl!.isNotEmpty) {
                          pushPage(
                              context,
                              WebViewPage(
                                title: sportName?.title ?? "",
                                url: sportName?.jumpUrl ?? "",
                                showOption: true,
                              ));
                        }
                      }),
                ),
              )),
        ),
        Positioned(
          left: 10,
          bottom: 24,
          child: TextButton(
            // padding: EdgeInsets.all(10),
            onPressed: () {
              mapController?.zoomToCurrentLocation();
            },
            child: Image.asset(
              "assets/images/wodeweizhi_white.png",
              width: 40,
              height: 40,
            ),
          ),
        )
      ],
    );
  }
}

class StopButton extends StatefulWidget {
  final VoidCallback? onPressedCompleted;
  final VoidCallback? onPresseing;
  final VoidCallback? onPresseConcel;

  const StopButton(
      {Key? key,
        this.onPressedCompleted,
        this.onPresseing,
        this.onPresseConcel})
      : super(key: key);

  @override
  StopButtonState createState() => StopButtonState();
}

class StopButtonState extends State<StopButton>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;

  @override
  void initState() {
    super.initState();
    controller =
        AnimationController(vsync: this, duration: Duration(seconds: 2));
    controller.addListener(() {
      setState(() => {});
    });
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        controller.forward();
        widget.onPresseing!();
      },
      onTapUp: (_) {
        if (controller.status == AnimationStatus.completed) {
          widget.onPressedCompleted!();
        } else {
          widget.onPresseConcel!();
        }
        setState(() {
          controller.value = 0;
        });
      },
      onTapCancel: () {
        widget.onPresseConcel!();
        setState(() {
          controller.value = 0;
        });
      },
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          SizedBox(
              height: 100.0,
              width: 100.0,
              child: CircularProgressIndicator(
                value: controller.value,
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF8C3829)),
              )),
          Container(
              height: 90,
              width: 90,
              decoration: BoxDecoration(
                  color: Color(0xFFB63D22),
                  borderRadius: BorderRadius.circular(45)),
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      "assets/images/jieshu_white.png",
                      width: 21.5,
                      height: 21.5,
                    ),
                    SizedBox(height: 7.5),
                    Text(
                      "长按结束",
                      style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFFF3F5F7),
                          fontWeight: FontWeight.normal),
                    )
                  ])),
        ],
      ),
    );
  }
}

class RideDataView extends StatelessWidget {
  final String? data;
  final String? unit;

  const RideDataView({Key? key, this.data, this.unit}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
      Text(
        data!,
        style: TextStyle(
            fontSize: 39,
            color: DColor.ff242424,
            fontFamily: "DIN",
            fontWeight: FontWeight.normal),
      ),
      SizedBox(height: 5),
      Text(
        unit!,
        style: TextStyle(
            fontSize: 12,
            color: DColor.ff808080,
            fontWeight: FontWeight.normal),
      ),
    ]);
  }
}

class GPSStatusView extends StatelessWidget {
  final int? accuracy;

  const GPSStatusView({Key? key, this.accuracy}) : super(key: key);

  int getGPSStatus() {
    if (accuracy! <= 0) {
      return 0;
    } else {
      if (accuracy! <= 10) {
        return 5;
      } else if (accuracy! <= 30) {
        return 4;
      } else if (accuracy! <= 50) {
        return 3;
      } else if (accuracy! <= 100) {
        return 2;
      } else {
        return 1;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final gpsStatus = getGPSStatus();
    if (gpsStatus == 0) {
      return Text(
        "无信号",
        style: TextStyle(
          fontSize: 9,
          color: DColor.ff242424,
          fontWeight: FontWeight.normal,
        ),
      );
    } else {
      final width = 2.0;
      final minHeight = 2.0;
      final maxHeight = 10.0;
      final step = (maxHeight - minHeight) / 5.0;
      var children = <Widget>[];
      for (var i = 0; i < 5; i++) {
        var child;
        if (i < gpsStatus) {
          child = Container(
              margin: EdgeInsets.fromLTRB(2, 0, 0, 0),
              decoration: BoxDecoration(
                color: DColor.ff3ecc49,
                borderRadius: BorderRadius.circular(5),
              ),
              width: 5,
              height: 5);
        } else {
          child = Container(
              margin: EdgeInsets.fromLTRB(2, 0, 0, 0),
              decoration: BoxDecoration(
                color: DColor.ffF6F6F6,
                borderRadius: BorderRadius.circular(5),
              ),
              width: 5,
              height: 5);
        }

        children.add(child);
        children.add(
          SizedBox(width: 1),
        );
      }

      return Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: children,
      );
    }
  }
}
