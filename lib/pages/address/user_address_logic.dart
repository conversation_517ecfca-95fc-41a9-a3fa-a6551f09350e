import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/io/user.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/api_response/api_edge_response_entity.dart';
import 'package:new_edge/model/shopping_address.dart';
import 'package:new_edge/request/MyHttp.dart';

class UserAddressLogic extends GetxController {
  var remoteAddress = ShoppingAddress().obs;
  var localAddress = ShoppingAddress().obs;
  var autoAddress = "".obs;

  @override
  void onReady() {
    super.onReady();
    refreshData();
  }

  bool isRemoteValid() {
    return remoteAddress.value.isValid();
  }

  bool isLocalValid() {
    return localAddress.value.isValid();
  }

  bool hasLocalArea() {
    String province = localAddress.value.province ?? "";
    String city = localAddress.value.city ?? "";
    String town = localAddress.value.town ?? "";
    if (province.isEmpty && city.isEmpty && town.isEmpty) {
      return false;
    }
    return true;
  }

  void updateArea(String province, String city, String town) {
    localAddress.update((val) {
      val?.province = province;
      val?.city = city;
      val?.town = town;
    });
  }

  String getLocalArea() {
    return localAddress.value.getArea();
  }

  String getFullAddress() {
    return localAddress.value.getFullAddress();
  }

  void updateAddress(String address) {
    localAddress.update((val) {
      val?.address = address;
    });
  }

  void updateAutoAddress(String address) {
    autoAddress.value = address;
  }

  void updateCity(String city) {
    localAddress.update((val) {
      val?.city = city;
    });
  }

  void updateName(String name) {
    localAddress.update((val) {
      val?.shoppingName = name;
    });
  }

  void updateMobile(String mobile) {
    localAddress.update((val) {
      val?.mobile = mobile;
    });
  }

  void updateAreaCode(String areaCode) {
    localAddress.update((val) {
      val?.areaCode = areaCode;
    });
  }

  void updatePostCode(String postCode) {
    localAddress.update((val) {
      val?.postCode = postCode;
    });
  }

  void updateProvince(String province) {
    localAddress.update((val) {
      val?.province = province;
    });
  }

  void updateTown(String town) {
    localAddress.update((val) {
      val?.town = town;
    });
  }

  Future<bool> refreshData() async {
    try {
      ShoppingAddress address = await getUserShoppingAddress();
      remoteAddress.value = address;
      localAddress.value = address;
      return true;
    } catch (error) {
      print("出错 $error");
    }
    return false;
  }

  Future<ShoppingAddress> getUserShoppingAddress() {
    return UserApi(MyHttp.dio)
        .getUserShoppingAddress(uid: Account.loginAccount!.uid);
  }

  Future<void> updateUserShoppingAddress() {
    return UserApi(MyHttp.dio)
        .updateUserShoppingAddress(
      address: localAddress.value.address,
      city: localAddress.value.city,
      mobile: localAddress.value.mobile,
      name: localAddress.value.shoppingName,
      postCode: localAddress.value.postCode,
      province: localAddress.value.province,
      town: localAddress.value.town,
    )
        .then((value) {
      remoteAddress.value = localAddress.value;
    });
  }

  Future<ApiEdgeResponse<ShoppingAddress>> addressAnalysis(String address) {
    return UserApi(MyHttp.dio)
        .addressAnalysis(addressStr: address)
        .then((value) {
      if (value.info != null) {
        localAddress.value = value.info!;
      }
      return value;
    });
  }
}
