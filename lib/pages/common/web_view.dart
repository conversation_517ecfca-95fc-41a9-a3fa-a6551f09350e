import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/channels/common_channel.dart';
import 'package:new_edge/channels/flutter_mixpay.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/constant/constant.dart';

import 'package:new_edge/io/race.dart';
import 'package:new_edge/io/route.dart';
import 'package:new_edge/model/order_info.dart';
import 'package:new_edge/model/pay_info.dart';
import 'package:new_edge/model/route_info.dart';
import 'package:new_edge/pages/route/route_map.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/toast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'developer_tools.dart';

class WebViewPage extends StatefulWidget {
  final String? url;
  final String? callbackUrl;

  final String? title;
  final bool showOption;

  WebViewPage(
      {Key? key,
      this.url,
      this.title = "",
      this.callbackUrl,
      this.showOption = true});

  @override
  State<StatefulWidget> createState() {
    return _WebViewPageState(title);
  }
}

class _WebViewPageState extends State<WebViewPage> {
  String? title;

  _WebViewPageState(this.title);

  bool loading = true;
  String? _currentUrl;

  WebViewController? _controller;

  @override
  void initState() {
    super.initState();

    // Enable hybrid composition.
    // if (Platform.isAndroid) WebView.platform = SurfaceAndroidWebView();

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel('IMINWebBridge', onMessageReceived: (message) {
        if (message.message.isNotEmpty) {
          var json = jsonDecode(message.message);
          print("Received message: $json");
          var method = json["method"];
          var args = json["args"];
          if (method == "oauthCallBack") {
            var result = args["result"];
            Navigator.pop(context, result);
          } else if (method == "pay") {
            // 支付 todo
            var orderNo = args["orderNo"];
            var payType = args["payType"];
            payFor(orderNo, payType);
          } else if (method == "jump") {
            //h5跳转原生
            var routeId = args["routeId"];
            jumpRoute(routeId);
          }
        }
      })
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
            print("progress:$progress");
            if (progress == 100) {
              setState(() {
                loading = false;
              });
            } else {
              setState(() {
                loading = true;
              });
            }
          },
          onPageStarted: (url) async {
            _currentUrl = url;
            setState(() {
              loading = true;
            });
            var tmp = await _controller!.getTitle();
            setState(() {
              title = tmp;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              loading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) async {
            print("onNavigationRequest:${request.url}");
            if (widget.callbackUrl != null &&
                request.url.startsWith(widget.callbackUrl!)) {
              Navigator.pop(context, request.url);
              return NavigationDecision.prevent;
            }
            if (request.url.startsWith("https://apps.apple.com") ||
                _isDownloadable(request.url)) {
              final Uri _url = Uri.parse(request.url);
              if (await canLaunchUrl(_url)) {
                await launchUrl(_url);
              }
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(_getCurrentUrl()!));
  }

  bool _isDownloadable(String url) {
    // 使用文件后缀名判断
    return url.endsWith('.pdf') ||
        url.endsWith('.zip') ||
        url.endsWith('.doc') ||
        url.endsWith('.docx') ||
        url.endsWith('.xls') ||
        url.endsWith('.xlsx') ||
        url.endsWith('.apk');
  }

  String? _getCurrentUrl() {
    if (_currentUrl != null) {
      return _currentUrl;
    }
    _currentUrl = widget.url!.replaceAll(" ", "");
    print("webview currentUrl is: $_currentUrl");
    return _currentUrl;
  }

  Future<String> _getAppVersion() async {
    PackageInfo packageInfo = await CommonChannel.fromPlatform();

    String appName = packageInfo.appName == null ? "" : packageInfo.appName;
    String version = packageInfo.version == null ? "" : packageInfo.version;

    if (DeveloperTools.isTestService()! && null != packageInfo.buildNumber) {
      version = version + "（" + packageInfo.buildNumber + "）";
    }

    return version;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onPop,
      child: Scaffold(
        resizeToAvoidBottomInset: Platform.isAndroid,
        appBar: AppBar(
          title: Text(
            widget.title!,
            style: TextStyle(color: DColor.ff242424, fontSize: 18),
          ),
          centerTitle: true,
          leadingWidth: 50,
          leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff242424,
              ),
              onPressed: () => Navigator.pop(context)),
          actions: <Widget>[
            !widget.showOption
                ? Container()
                : PopupMenuButton(
                    icon: Image.asset(
                      DImages.formatPathPng(
                        'icon_more',
                      ),
                      height: 20,
                      width: 20,
                      color: DColor.ff242424,
                    ),
                    itemBuilder: (BuildContext context) {
                      return [
                        PopupMenuItem(
                          child: Text("在浏览器中打开"),
                          value: "system",
                        ),
                        PopupMenuItem(
                          child: Text("刷新页面"),
                          value: "refresh",
                        ),
                        PopupMenuItem(
                          child: Text("分享到..."),
                          value: "share",
                        ),
                      ];
                    },
                    onSelected: (dynamic key) async {
                      if (key == "system") {
                        final Uri _url = Uri.parse(_getCurrentUrl() ?? "");
                        if (await canLaunchUrl(_url)) {
                          await launchUrl(_url);
                        } else {
                          showToast("打开失败", context);
                        }
                      } else if (key == "share") {
                        Share.share("${widget.title} ${_getCurrentUrl()}");
                      } else if (key == "refresh") {
                        _controller?.reload();
                      }
                    },
                  )
          ],
        ),
        body: Stack(
          children: <Widget>[
            WebViewWidget(controller: _controller!),
            Visibility(
              visible: loading,
              child: SizedBox(
                height: 2,
                child: LinearProgressIndicator(backgroundColor: DColor.primary),
              ),
            )
          ],
        ),
      ),
    );
  }

  void jumpRoute(routeId) {
    showLoadingDialog(context);
    RouteApi(MyHttp.dio)
        .getRoutePositionDetail(routeId: int.parse(routeId))
        .then((detail) {
      RouteInfo routeInfo = detail;
      if (routeInfo != null) {
        Future.delayed(Duration(milliseconds: 500), () {
          Navigator.pop(context);
          pushPage(
              context,
              RouteMapPage.fromRouteInfo(
                routeInfo: routeInfo,
              ));
        });
      }
    }).catchError((error) {
      Navigator.pop(context);
      if (error is DioException) {
        showToast(error.message.toString(), context);
      }
    });
  }

  void payFor(String? orderNo, String? payType) async {
    try {
      PayInfo payInfo = await RaceApi(MyHttp.dio)
          .createPreOrder(orderNo: orderNo, payType: payType);
      Map payResult = await (FlutterMixPay.pay(payType, payInfo)
          as FutureOr<Map<dynamic, dynamic>>);
      // print('lijian-收到map:${payResult}');
      var result_code = payResult["result_code"];
      if (result_code == PAY_SUCCESS) {
        OrderInfo orderInfo =
            await RaceApi(MyHttp.dio).qureyOrder(orderNo: orderNo);
        if (orderInfo.payStatus == 1) {
          //支付成功
          var result = {
            "result_code": payResult['result_code'],
            "result_msg": payResult['result_msg']
          };
          // _controller?.evaluateJavascript('onPayComplete(${jsonEncode(result)})')
          //     ?.then((result) {
          //   // You can handle JS result here.
          // });
          var runJavaScriptReturningResult =
              _controller?.runJavaScriptReturningResult(
                  'onPayComplete(${jsonEncode(result)})');
        } else {
          //支付失败
          var result = {"result_code": PAY_ERROR, "result_msg": "支付失败"};
          // _controller
          //     ?.evaluateJavascript('onPayComplete(${jsonEncode(result)})')
          //     ?.then((result) {
          //   // You can handle JS result here.
          // });
          var runJavaScriptReturningResult =
              _controller?.runJavaScriptReturningResult(
                  'onPayComplete(${jsonEncode(result)})');
        }
      } else {
        var result = {
          "result_code": payResult['result_code'],
          "result_msg": payResult['result_msg']
        };
        // _controller
        //     ?.evaluateJavascript('onPayComplete(${jsonEncode(result)})')
        //     ?.then((result) {
        //   // You can handle JS result here.
        // });
        var runJavaScriptReturningResult =
            _controller?.runJavaScriptReturningResult(
                'onPayComplete(${jsonEncode(result)})');
      }
    } on DioException catch (e) {
      showToast(e.message.toString(), context);
    }
  }

  Future<bool> _onPop() async {
    if (_controller == null) {
      return true;
    }
    if (await _controller!.canGoBack()) {
      await _controller!.goBack();
      return false;
    }
    return true;
  }
}
