import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:new_edge/channels/common_channel.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';

import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/settings.dart';
import 'package:json_annotation/json_annotation.dart';

part 'developer_tools.g.dart';

class DeveloperToolsPage extends StatefulWidget {
  DeveloperToolsPage({Key? key});

  @override
  State<StatefulWidget> createState() {
    return _DeveloperToolsPageState();
  }
}

class _DeveloperToolsPageState extends State<DeveloperToolsPage> {
  @override
  void initState() {
    super.initState();
    _proxyController.text = DeveloperTools.devParameter.proxyAddress!;
  }

  TextEditingController _proxyController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("开发者工具",style: TextStyle(color: DColor.ff242424, fontSize: 18),),
        backgroundColor: DColor.white,
        leading: IconButton(
            icon: Image.asset(
              DImages.formatPathPng(
                'jiantou_left_white',
              ),
              height: 17,
              width: 17,
              color: DColor.ff242424,
            ),
            onPressed: () => Navigator.pop(context)),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(0),
        child: Column(
          children: <Widget>[
            SwitchListTile(
              contentPadding: EdgeInsets.only(left: 15, right: 15),
              activeColor: DColor.fffb691d,
              onChanged: (value) {
                setState(() {
                  DeveloperTools.devParameter.userHttps = value;
                });
              },
              value: DeveloperTools.devParameter.userHttps!,
              title: Text(
                "开启HTTPS",
                style: TextStyle(color: DColor.ff242424),
              ),
            ),
            SwitchListTile(
              contentPadding: EdgeInsets.only(left: 15, right: 15),
              activeColor: DColor.fffb691d,
              onChanged: (value) {
                setState(() {
                  DeveloperTools.devParameter.proxyEnable = value;
                });
              },
              value: DeveloperTools.devParameter.proxyEnable!,
              title: Text(
                "开启HTTP代理",
                style: TextStyle(color: DColor.ff242424),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 15, right: 15),
              child: TextField(
                decoration: InputDecoration(
                  hintText: "**********:8888",
                ),
                controller: _proxyController,
                onChanged: (text) {
                  DeveloperTools.devParameter.proxyAddress = text;
                },
                style: TextStyle(color: DColor.ff242424),
              ),
            ),
            SwitchListTile(
              contentPadding: EdgeInsets.only(left: 15, right: 15),
              activeColor: DColor.fffb691d,
              onChanged: (value) {
                setState(() {
                  DeveloperTools.devParameter.testService = value;
                });
              },
              value: DeveloperTools.isTestService()!,
              title: Text(
                "开启测试环境",
                style: TextStyle(color: DColor.ff242424),
              ),
            ),
            SwitchListTile(
              contentPadding: EdgeInsets.only(left: 15, right: 15),
              activeColor: DColor.fffb691d,
              onChanged: (value) {
                setState(() {
                  DeveloperTools.devParameter.autoRide = value;
                });
              },
              value: DeveloperTools.isAutoRide()!,
              title: Text(
                "自动骑行",
                style: TextStyle(color: DColor.ff242424),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    DeveloperTools.save();
    DeveloperTools.updateProxy();
    super.dispose();
  }
}

@JsonSerializable()
class DevParameter {
  int? maxFlow;
  int? minFlow;
  bool? openDualFlow;
  bool? beauty;
  bool? showKpbs;
  bool? proxyEnable;
  bool? testService;
  bool? autoRide;
  bool? userHttps;
  bool? isFlutter;
  String? proxyAddress;

  DevParameter({
    this.maxFlow,
    this.minFlow,
    this.openDualFlow,
    this.showKpbs,
    this.proxyEnable,
    this.beauty,
    this.testService,
    this.autoRide,
    this.userHttps,
    this.isFlutter,
    this.proxyAddress,
  }) {
    if (maxFlow == null) {
      maxFlow = 3;
    }
    if (minFlow == null) {
      minFlow = 0;
    }
    if (openDualFlow == null) {
      openDualFlow = true;
    }
    if (showKpbs == null) {
      showKpbs = false;
    }
    if (testService == null) {
      testService = false;
    }
    if (autoRide == null) {
      autoRide = false;
    }
    if (proxyEnable == null) {
      proxyEnable = false;
    }
    if (beauty == null) {
      beauty = false;
    }
    if (userHttps == null) {
      userHttps = false;
    }
    if (isFlutter == null) {
      isFlutter = false;
    }
    if (proxyAddress == null) {
      proxyAddress = "**********:8888";
    }
  }

  factory DevParameter.fromJson(Map<String, dynamic> json) =>
      _$DevParameterFromJson(json);

  Map<String, dynamic> toJson() => _$DevParameterToJson(this);
}

class DeveloperTools {
  static DevParameter devParameter = DevParameter();
  static int debugMode = 0; // 0 debug,1 alpha, 2 release
  static bool get isJoyrunInner => _isJoyrunInner; // 是否悦跑圈内部

  static bool _isJoyrunInner = false;

  static setJoyrunInner(bool isJoyrunInner) {
    _isJoyrunInner = isJoyrunInner;
  }

  static bool isRelease() {
    return debugMode == 2;
  }

  static Future init() async {
    var text = localSettings.getString("DEV_PARAMETER", '');
    if (text.isNotEmpty) {
      devParameter = DevParameter.fromJson(jsonDecode(text));
    }
    updateProxy();
  }

  static updateProxy() async {
    if (devParameter.proxyEnable!) {
      MyHttp.updateDioProxy(devParameter.proxyAddress??'');
    } else {
      MyHttp.updateDioProxy("");
    }
  }

  static bool? isTestService() {
    return devParameter.testService;
  }

  static bool? isAutoRide() {
    return devParameter.autoRide;
  }

  static void save() {
    localSettings.setString("DEV_PARAMETER", jsonEncode(devParameter));
    setTestService(devParameter.testService);
  }

  static Future<void> setTestService(bool? testService) async {
    await CommonChannel.setTestService(testService??false);
  }
}
