import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/activities_joined_event.dart';
import 'package:new_edge/model/activities_race_registration_record.dart';
import 'package:new_edge/model/activities_user_challenge.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/join_challenge.dart';
import 'package:new_edge/model/joined_statistic.dart';
import 'package:new_edge/pages/games/my/my_home_state.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/widgets/load_state.dart';

class MyHomeLogic extends GetxController {
  var loadStatus = FitLoadStatus.loading.obs;
  var state = MyHomeState();

  Future<bool> refreshData() async {
    try {
      var result = await Connectivity().checkConnectivity();
      if (result.contains(ConnectivityResult.none)) {
        loadStatus.value = FitLoadStatus.loadError;
        print("当前无网络.");
        return false;
      } else {
        loadStatus.value = FitLoadStatus.loading;
      }

      var challengeList = (await _loadChallengeData())?.result?.records;
      if (challengeList != null) {
        state.challengeList.value = challengeList;
      }

      var joinedChallenges = (await _getJoinedChallengesStatistic())?.result;
      if (joinedChallenges != null) {
        state.joinedChallengeStatistic.value = joinedChallenges;
      }

      var raceList = (await _loadRaceData())?.result?.records;
      if (raceList != null) {
        state.competitionList.value = raceList;
      }
      var joinedGroups = (await _getJoinedGroupStatistic())?.result;
      if (joinedGroups != null) {
        state.joinedGroupStatistic.value = joinedGroups;
      }

      var eventList = (await _loadEventData())?.result?.records;
      if (eventList != null) {
        state.eventList.value = eventList;
      }
      var joinedEvents = (await _getJoinedEventsStatistic())?.result;
      if (joinedEvents != null) {
        state.joinedEventStatistic.value = joinedEvents;
      }
      loadStatus.value = FitLoadStatus.loadSuccess;
    } catch (error) {
      print("出错 $error");
      loadStatus.value = FitLoadStatus.loadError;
    }
    return true;
  }

  Future<ApiResponse<ApiPaging<JoinChallenge>>?>
      _loadChallengeData() {
    return ActiviesApi(MyHttp.dio)
        .listActivitiesJoinedChallenges(pageNum: 0, pageSize: 10);
  }

  Future<ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>?>
      _loadRaceData() {
    return ActiviesApi(MyHttp.dio)
        .listRaceJoinedGroup(pageNum: 0, pageSize: 10);
  }

  Future<ApiResponse<ApiPaging<ActivitiesJoinedEvent>>?> _loadEventData() {
    return ActiviesApi(MyHttp.dio)
        .listActivitiesJoinedEvents(pageNum: 0, pageSize: 10);
  }

  Future<ApiResponse<JoinedStatistic>?> _getJoinedChallengesStatistic() {
    return ActiviesApi(MyHttp.dio).getJoinedChallengesStatistic();
  }

  Future<ApiResponse<JoinedStatistic>?> _getJoinedGroupStatistic() {
    return ActiviesApi(MyHttp.dio).getJoinedGroupStatistic();
  }

  Future<ApiResponse<JoinedStatistic>?> _getJoinedEventsStatistic() {
    return ActiviesApi(MyHttp.dio).getJoinedEventsStatistic();
  }
}
