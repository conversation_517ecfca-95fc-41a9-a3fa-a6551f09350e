import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_user_challenge.dart';
import 'package:new_edge/model/join_challenge.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/challenge_user_page.dart';
import 'package:new_edge/pages/games/my/challenge/my_home_challenge_list_logic.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';

class MyHomeChallengeListPage extends StatefulWidget {
  MyHomeChallengeListPage() {}

  @override
  _MyHomeChallengeListPageState createState() =>
      _MyHomeChallengeListPageState();
}

class _MyHomeChallengeListPageState
    extends RefreshState<MyHomeChallengeListPage>
    with TickerProviderStateMixin {
  final logic = Get.put(MyChallengeHomeListLogic());
  final state = Get.find<MyChallengeHomeListLogic>().pagingState;
  final joinedStatistic = Get.find<MyChallengeHomeListLogic>().joinedStatistic;

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff232323,
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                Navigator.pop(context, false);
              },
            ),
            title: Text("我的挑战",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: Obx(() => _buildRefreshContent(context)));
  }

  Widget _buildRefreshContent(BuildContext context) {
    return buildRefreshWidget(
        enablePullUp: true,
        enablePullDown: true,
        onRefresh: () async {
          logic.refreshData();
        },
        refreshController: refreshController,
        builder: () => state.value.hasData()
            ? _buildPageItem()
            : _buildLoadStateWidget(logic.loadStatus.value, () async {
                logic.refreshData();
              }));
  }

  Widget _buildPageItem() {
    List<JoinChallenge> items = state.value.data;
    return CustomScrollView(slivers: [
      SliverPadding(
          padding: EdgeInsets.only(top: 5, left: 10, right: 10),
          sliver: SliverToBoxAdapter(child: _buildPageHeader())),
      SliverPadding(
          padding: EdgeInsets.only(left: 10, right: 10),
          sliver: SliverGrid(
            delegate:
                SliverChildBuilderDelegate((BuildContext context, int index) {
              var item = items[index];
              return ChallengeUserItem(item);
            }, childCount: items.length),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisExtent: 244,
            ),
          ))
    ]);
  }

  Widget _buildPageHeader() {
    return CommonCard(
        child: Row(
      children: [
        Image.asset(
          DImages.formatPathPng(
            "icon_challenge_black",
          ),
          height: 30,
          color: DColor.black,
        ),
        Expanded(child: _buildTotalView())
      ],
    ));
  }

  Widget _buildTotalView() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildSumItem("进行中", joinedStatistic.value.underway ?? 0),
        SizedBox(width: 6),
        _buildSumItem("已结束", joinedStatistic.value.finished ?? 0),
        SizedBox(width: 6),
        _buildSumItem("全部", joinedStatistic.value.total ?? 0),
      ],
    );
  }

  Widget _buildSumItem(
    String title,
    int count,
  ) {
    return Row(
      children: [
        Text(title, style: TextStyle(color: DColor.ff232323, fontSize: 12)),
        SizedBox(width: 6),
        Column(children: [
          SizedBox(height: 6),
          Text("$count",
              style: TextStyle(
                  color: DColor.ff242424,
                  fontFamily: DFamily.dinBold,
                  fontSize: 22)),
        ])
      ],
    );
  }

  Widget _buildLoadStateWidget(
      FitLoadStatus loadStatus, VoidCallback onRefresh) {
    if (loadStatus == FitLoadStatus.loadSuccess) {
      return _buildEmpty();
    } else if (loadStatus == FitLoadStatus.loadError) {
      return FitLoadError(
          icon: DImages.formatPathPng('img_network_error'),
          msg: "当前网络不可用",
          tip: " ",
          onPressedRefresh: () {
            onRefresh();
          });
    } else {
      return Container();
    }
  }

  Widget _buildEmpty({String tip = "还没报名任何挑战"}) {
    return Stack(
      children: [
        Image.asset(DImages.formatPathPng(
          "bg_challenge_empty",
        )),
        Positioned.fill(
            child: Align(
                alignment: Alignment.center,
                child: Text(
                  tip,
                  style: TextStyle(color: Color(0xFF808080), fontSize: 16),
                ))),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
