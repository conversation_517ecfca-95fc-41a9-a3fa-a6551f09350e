import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/activities_user_challenge.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/join_challenge.dart';
import 'package:new_edge/model/joined_statistic.dart';
import 'package:new_edge/pages/base/page_controller.dart';
import 'package:new_edge/pages/games/my/challenge/my_home_challenge_list_state.dart';
import 'package:new_edge/request/MyHttp.dart';

class MyChallengeHomeListLogic extends PagingController<
    JoinChallenge, MyHomeChallengeListState> {
  var joinedStatistic = JoinedStatistic().obs;

  @override
  MyHomeChallengeListState getState() => MyHomeChallengeListState();

  @override
  void refreshData() async {
    super.refreshData();
    var statistic = (await getJoinedChallengesStatistic())?.result;
    if (statistic != null) {
      joinedStatistic.value = statistic;
    }
  }

  @override
  Future<ApiResponse<ApiPaging<JoinChallenge>>?>
      loadData(int pagingIndex) {
    return ActiviesApi(MyHttp.dio)
        .listActivitiesJoinedChallenges(pageNum: pagingIndex);
  }

  Future<ApiResponse<JoinedStatistic>?> getJoinedChallengesStatistic() {
    return ActiviesApi(MyHttp.dio).getJoinedChallengesStatistic();
  }
}
