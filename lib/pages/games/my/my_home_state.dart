import 'package:get/get.dart';
import 'package:new_edge/model/activities_joined_event.dart';
import 'package:new_edge/model/activities_race_registration_record.dart';
import 'package:new_edge/model/activities_user_challenge.dart';
import 'package:new_edge/model/join_challenge.dart';
import 'package:new_edge/model/joined_statistic.dart';

class MyHomeState {
  var challengeList = <JoinChallenge>[].obs;
  var competitionList = <ActivitiesRaceRegistrationRecord>[].obs;
  var eventList = <ActivitiesJoinedEvent>[].obs;

  var joinedGroupStatistic = JoinedStatistic().obs;
  var joinedEventStatistic = JoinedStatistic().obs;
  var joinedChallengeStatistic = JoinedStatistic().obs;
}
