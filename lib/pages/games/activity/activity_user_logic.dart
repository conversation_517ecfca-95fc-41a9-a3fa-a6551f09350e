import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/pages/base/page_controller.dart';
import 'package:new_edge/request/MyHttp.dart';

import '../../../model/activities_joined_event.dart';
import 'activity_user_state.dart';

class ActivityUserLogic extends PagingController<
    ActivitiesJoinedEvent, ActivityUserState> {
  @override
  ActivityUserState getState() => ActivityUserState();

  @override
  Future<ApiResponse<ApiPaging<ActivitiesJoinedEvent>>?> loadData(
      int pagingIndex) {
    return ActiviesApi(MyHttp.dio).getActivitiesJoinedEvents(pageNum: pagingIndex, pageSize: 100);
  }
}
