import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_event.dart';
import 'package:new_edge/routers/router.dart';
import 'package:new_edge/widgets/series_circle_profile.dart';

class ActivityItem extends StatelessWidget {
  final ActivitiesEvent item;

  const ActivityItem({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Get.toNamed(RouteGet.activityDetail, arguments: {
            'id': item.id ?? "",
          });
        },
        child: Column(
          children: [
            Container(height: 8),
            Material(
                color: DColor.white,
                shadowColor: Color(0xFF000000).withOpacity(0.16),
                elevation: 5,
                child: Container(
                    width: MediaQuery.of(context).size.width,
                    color: Colors.white,
                    padding: EdgeInsets.only(
                        left: 10, right: 10, top: 10, bottom: 10),
                    child: Container(
                        child: Column(
                      children: [
                        Container(
                            height: 150,
                            alignment: Alignment.center,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(5.0),
                              child: SizedBox(
                                  width: double.infinity,
                                  child: CachedNetworkImage(
                                      imageUrl: item.coverImage ?? "",
                                      placeholder: (context, url) =>
                                          Image.asset(
                                            DImages.formatPathPng(
                                                'img_placeholder_bg'),
                                            fit: BoxFit.fitWidth,
                                          ),
                                      errorWidget: (context, url, error) =>
                                          Image.asset(
                                            DImages.formatPathPng(
                                                'img_placeholder_bg'),
                                            fit: BoxFit.fitWidth,
                                          ),
                                      fit: BoxFit.fitWidth)),
                            )),
                        SizedBox(
                          height: 8,
                        ),
                        Visibility(
                            visible: item.registeredUserHeadUrls?.isNotEmpty ??
                                false,
                            child: _buildRegisteredUserItem()),
                        SizedBox(
                          height: 8,
                        ),
                        _buildDetailItem()
                      ],
                    )))),
          ],
        ));
  }

  Widget _buildRegisteredUserItem() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Flexible(
          flex: 1,
          child:
              SeriesCircleProfile(imageUrls: item.registeredUserHeadUrls??[]),
        ),
        SizedBox(
          width: 0,
        ),
        Text(
          "已报名",
          style: TextStyle(fontSize: 12, color: Color(0xFF999999)),
        ),
      ],
    );
  }

  Widget _buildDetailItem() {
    DateTime raceStartTime = DateTime.parse(item.startTime ?? '');
    DateTime raceEndTime = DateTime.parse(item.endTime ?? '');
    String startTime = DateFormat('yyyy.MM.dd').format(raceStartTime);
    String endTime = DateFormat('yyyy.MM.dd').format(raceEndTime);

    DateTime registrationStartTime =
        DateTime.parse(item.registrationStartTime ?? '');
    DateTime registrationEndTime =
        DateTime.parse(item.registrationEndTime ?? '');
    bool isRegisterFinish = DateTime.now().millisecondsSinceEpoch >
        registrationEndTime.millisecondsSinceEpoch;
    bool isRegisterStart = DateTime.now().millisecondsSinceEpoch >
        registrationStartTime.millisecondsSinceEpoch;
    bool isPendingRegister = DateTime.now().millisecondsSinceEpoch <
        registrationStartTime.millisecondsSinceEpoch;
    bool isRegistering = isRegisterStart && !isRegisterFinish;
    bool isRaceFinish = DateTime.now().millisecondsSinceEpoch >
        raceEndTime.millisecondsSinceEpoch;

    String text = "";
    if (isPendingRegister) {
      text = "即将开放";
    } else if (isRegistering) {
      text = "正在报名";
    } else if (isRaceFinish) {
      text = "活动结束";
    } else if (isRegisterFinish) {
      text = "报名截止";
    }
    // 1. 未开始：未到报名开始时间
    // 2. 报名中：报名开始到结束期间
    // 3. 已截止：超过报名截止时间，未到比赛结束时间
    // 4. 已结束：超过比赛结束时间内

    return Row(children: [
      Expanded(
          child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.title ?? "",
            style: TextStyle(fontSize: 14, color: Color(0xFF383838)),
          ),
          SizedBox(height: 8),
          Text(
            "$startTime - $endTime",
            style: TextStyle(fontSize: 11, color: Color(0xFF808080)),
          ),
        ],
      )),
      SizedBox(
        width: 1,
        height: 40,
        child: DecoratedBox(
          decoration: BoxDecoration(color: Color(0xFFe5e5e5)),
        ),
      ),
      Container(
        padding: EdgeInsets.only(left: 18),
        child: Text(
          text,
          style: TextStyle(
              fontSize: 14,
              color: (isPendingRegister || isRegistering) ? DColor.primary : Color(0xFF999999)),
        ),
      )
    ]);
  }
}
