import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/activity_media_request.dart';
import 'package:new_edge/model/activity_video.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/pages/games/activity/join/video/activity_join_video_my_state.dart';
import 'package:new_edge/request/MyHttp.dart';

import '../../../../base/page_controller.dart';

class ActivityJoinVideoMyLogic
    extends PagingController<ActivityVideo, ActivityJoinVideoMyState> {
  final String? eventId;

  ActivityJoinVideoMyLogic({this.eventId}) {}

  @override
  ActivityJoinVideoMyState getState() => ActivityJoinVideoMyState();

  @override
  Future<ApiResponse<ApiPaging<ActivityVideo>>?> loadData(int pagingIndex) {
    return ActiviesApi(MyHttp.dio)
        .videos(data: ActivityMediaRequest(
        activityId: eventId??'', pageNo: pagingIndex, pageSize: 10));
  }
}
