import 'package:new_edge/io/activies.dart';
import 'package:new_edge/io/photo_system.dart';
import 'package:new_edge/model/activies_join_member.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/pages/games/activity/join/member/activity_join_member_my_state.dart';
import 'package:new_edge/request/MyHttp.dart';

import '../../../../base/page_controller.dart';

class ActivityJoinMemberMyLogic
    extends PagingController<ActiviesJoinMember, ActivityJoinMemberMyState> {
  final String? eventId;

  ActivityJoinMemberMyLogic({this.eventId}) {}

  @override
  ActivityJoinMemberMyState getState() => ActivityJoinMemberMyState();

  @override
  Future<ApiResponse<ApiPaging<ActiviesJoinMember>>?> loadData(
      int pagingIndex) {
    return ActiviesApi(MyHttp.dio).getActiviesEventsRegistrationRecord(
        pageNum: pagingIndex, eventId: eventId);
  }
}
