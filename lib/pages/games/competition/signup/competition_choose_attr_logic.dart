import 'dart:collection';

import 'package:get/get.dart';
import 'package:new_edge/model/activities_race_product_attribute.dart';
import 'package:new_edge/model/activities_race_product_sku.dart';
import 'package:new_edge/model/bike.dart';

//分类子选项，包含该选项的库存数量
class AttrSubItem {
  int cateCode;
  String name;
  ActivitiesRaceProductSku? sku;
  int quantity = 0;

  AttrSubItem({required this.cateCode, required this.name});
}

//规格选项
class AttrCategoryItem {
  int cateCode; //规格编码
  String cateName; //规格名称
  List<AttrSubItem> subItems = [];

  AttrCategoryItem({required this.cateCode, required this.cateName});
}

class CompetitionChooseAttrLogic extends GetxController {
  var attributes = <ActivitiesRaceProductAttribute>[].obs;
  var productSkus = <ActivitiesRaceProductSku>[].obs;

  var allCateItems = <AttrCategoryItem>[].obs;

  var selectedSku = Rx<ActivitiesRaceProductSku?>(null);

  var count = 1.obs;

//所有规格列表
  HashMap<int, AttrCategoryItem> allCateItemMap =
      HashMap<int, AttrCategoryItem>();

//选中的规格列表
  HashMap<int, AttrSubItem> selectSubItemMap = HashMap<int, AttrSubItem>();

  void addCount() {
    count++;
  }

  void reduceCount() {
    count--;
  }

  String? getSelectedName(int cateCode) {
    return selectSubItemMap[cateCode]?.name ?? "";
  }

  void selectedItem(AttrSubItem item) {
    selectSubItemMap[item.cateCode] = item;
    if (item.sku != null && item.quantity < count.value) {
      count.value = item.quantity;
    }
    reloadData();
  }

  ActivitiesRaceProductSku? _getSelectedSku() {
    if (selectSubItemMap.values.isEmpty) return null;
    return selectSubItemMap.values
        .firstWhere((element) => element.sku != null)
        .sku;
  }

  void updateAttribute(List<ActivitiesRaceProductAttribute> attrs) {
    attributes.value = attrs;
    reloadData();
  }

  void updateSkus(List<ActivitiesRaceProductSku> skus) {
    productSkus.value = skus;
    reloadData();
  }

  //查找上级的prefixCodes， 例如男款，白色
  List<String> getPrefixNames(int cateCode) {
    List<String> prefixCodes = [];
    for (var i = 0; i < cateCode; i++) {
      AttrSubItem? parentItem = selectSubItemMap[i];
      if (parentItem != null) {
        prefixCodes.add(parentItem.name);
      }
    }

    return prefixCodes;
  }

  AttrSubItem? getAttrSubItem(String attrName, int cateCode) {
    List<String> prefixNames = getPrefixNames(cateCode);
    prefixNames.add(attrName);
    String prefixCode = prefixNames.join('_');

    //根据拼接的code，查询sku
    List<ActivitiesRaceProductSku> filterSkus = productSkus
        .where((element) => (element.skuCode ?? "").startsWith(prefixCode))
        .toList();

    if (filterSkus.isNotEmpty) {
      int quantity =
          filterSkus.fold(0, (total, record) => total + (record.quantity ?? 0));
      AttrSubItem subItem = AttrSubItem(cateCode: cateCode, name: attrName);
      subItem.quantity = quantity;
      if (filterSkus.length == 1) {
        ActivitiesRaceProductSku sku = filterSkus[0];
        if (sku.skuCode == prefixCode) {
          subItem.sku = sku;
        }
      }
      return subItem;
    }
    return null;
  }

  void reloadData() {
    for (var i = 0; i < attributes.length; i++) {
      ActivitiesRaceProductAttribute attribute = attributes[i];

      AttrCategoryItem cateItem = AttrCategoryItem(
          cateCode: attribute.code ?? 0, cateName: attribute.name ?? "");

      List<AttrSubItem> subItems = [];

      AttrSubItem? firstValidSubItem;
      for (var attrName in (attribute.values ?? [])) {
        AttrSubItem? subItem = getAttrSubItem(attrName, cateItem.cateCode);

        if (subItem != null) {
          subItems.add(subItem);

          if (firstValidSubItem == null && subItem.quantity > 0) {
            firstValidSubItem = subItem;
          }
        }
      }

      // 判断是否有默认选中项，如果没有，则默认第一个有数量的选中
      if (firstValidSubItem == null) {
        selectSubItemMap.remove(cateItem.cateCode);
      } else {
        AttrSubItem? currentSelectItem = selectSubItemMap[cateItem.cateCode];
        if (currentSelectItem == null) {
          selectSubItemMap[cateItem.cateCode] = firstValidSubItem;
        } else {
          //判断之前选择的sku是否有效
          AttrSubItem? selectItem = subItems.firstWhereOrNull((element) =>
              element.name == currentSelectItem.name && element.quantity > 0);

          List<String> prefixNames = getPrefixNames(cateItem.cateCode);
          String prefixCode = prefixNames.join('_');
          if (selectItem == null ||
              currentSelectItem.sku?.skuCode?.startsWith(prefixCode) == false) {
            selectSubItemMap[cateItem.cateCode] = firstValidSubItem;
          }
        }
      }

      cateItem.subItems = subItems;
      allCateItemMap[cateItem.cateCode] = cateItem;
    }

    allCateItems.value = allCateItemMap.values.toList();
    selectedSku.value = _getSelectedSku();
  }
}
