import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/io/user.dart';
import 'package:new_edge/model/activities_race_bag.dart';
import 'package:new_edge/model/api_response/api_edge_response_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/identity_code.dart';
import 'package:new_edge/pages/base/list_controller.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/widgets/load_state.dart';

class CompetitionBagDetailLogic extends ListController<ActivitiesRaceBag> {
  final String id;

  CompetitionBagDetailLogic(this.id) {}

  @override
  Future<ApiResponse<List<ActivitiesRaceBag>>> loadData() {
    return ActiviesApi(MyHttp.dio).getActivitiesRaceBags(groupId: id);
  }
}
