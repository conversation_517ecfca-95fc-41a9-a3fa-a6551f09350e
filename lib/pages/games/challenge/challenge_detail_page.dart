import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:new_edge/app.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/pages/common/disclaimer_dialog.dart';
import 'package:new_edge/pages/common/normal_dialog.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/challenge_detail_logic.dart';
import 'package:new_edge/pages/games/challenge/challenge_detail_sub_view.dart';
import 'package:new_edge/pages/games/challenge/challenge_target_view.dart';
import 'package:new_edge/pages/games/challenge/prize/challenge_prize_page.dart';
import 'package:new_edge/pages/games/challenge/ridebrief/challenge_ride_brief_confirm_page.dart';
import 'package:new_edge/pages/games/challenge/ridebrief/challenge_ride_brief_page.dart';
import 'package:new_edge/pages/games/challenge/signup/challenge_order_payment_page.dart';
import 'package:new_edge/pages/games/challenge/signup/challenge_registration_logic.dart';
import 'package:new_edge/pages/games/challenge/signup/challenge_sign_tip_page.dart';
import 'package:new_edge/pages/games/challenge/store/challenge_choose_store_page.dart';
import 'package:new_edge/pages/login/login_input_phone.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/navigator.dart';
import 'package:new_edge/util/pay/wechat_utils.dart';
import 'package:new_edge/util/string_format_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/NormalWidget.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/custom_slide_widget.dart';
import 'package:new_edge/widgets/date_time_widget.dart';
import 'package:new_edge/widgets/html_widget.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/series_circle_profile.dart';
import 'package:new_edge/widgets/submit_button.dart';

class ChallengeDetailPage extends StatefulWidget {
  String? id = Get.arguments['id'] as String;

  ChallengeDetailPage() {}

  @override
  _ChallengeDetailState createState() => _ChallengeDetailState();
}

class _ChallengeDetailState extends RefreshState<ChallengeDetailPage>
    with RouteAware {
  final logic = Get.put(ChallengeDetailLogic());
  final item = Get.find<ChallengeDetailLogic>().item;

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  void initState() {
    super.initState();
    _initController();
  }

  void _initController() {
    Get.put(ChallengeRegistrationLogic(widget.id ?? "", REG_SOURCE_DETAIL));
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (mounted) {
      logic.refreshData(widget.id ?? "");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset(
            DImages.formatPathPng(
              'jiantou_left_white',
            ),
            height: 17,
            width: 17,
            color: DColor.ff232323,
          ),
          onPressed: () {
            FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
            Navigator.pop(context, false);
          },
        ),
        title: Text("挑战详情",
            style: TextStyle(color: DColor.ff242424, fontSize: 18)),
        actions: [_buildShareAction()],
      ),
      body: Obx(() => _buildContent(context)),
    );
  }

  Widget _buildList(BuildContext context) {
    return buildRefreshWidget(
        enablePullUp: false,
        enablePullDown: widget.id != null,
        onRefresh: () {
          logic.refreshData(widget.id ?? "");
        },
        refreshController: refreshController,
        builder: () => logic.hasData()
            ? CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: _buildDetail(context),
                  ),
                ],
              )
            : buildLoadStateWidget(logic.loadStatus.value, () {
                refreshController.requestRefresh();
              }));
  }

  Widget _buildDetail(BuildContext context) {
    bool isJoined = item.value.userSignUpFlag ?? false;
    bool isCompleted = item.value.userIsComplete ?? false;
    return Column(
      children: [
        ChallengeDetailHeader(item.value),
        Container(
            padding: EdgeInsets.only(left: 10, right: 10, top: 4),
            child: Column(
              children: [
                _buildDetailSummary(item.value),
                _buildPrizeInfo(item.value),
                isCompleted
                    ? Column(
                        children: [
                          buildChallengeAward(item.value, isCompleted),
                          Visibility(
                              visible: isJoined, child: _buildTarget(context)),
                        ],
                      )
                    : Column(
                        children: [
                          Visibility(
                              visible: isJoined,
                              child: buildChallengeTip(item.value.endTime)),
                          Visibility(
                              visible: isJoined, child: _buildTarget(context)),
                          buildChallengeAward(item.value, isCompleted),
                        ],
                      ),
                _buildMemberInfo(),
                _buildDetailImage(),
              ],
            )),
      ],
    );
  }

  Widget _buildPrizeInfo(ChallengeDetail item) {
    String prizeImgUrl = item.luckyDrawaInfoVo?.cover ?? "";
    if (prizeImgUrl.isEmpty) {
      return Container();
    }
    return GestureDetector(
        onTap: () {
          Get.to(ChallengePrizePage(
            challenge: item,
            luckyDrawaId: item.luckyDrawaId ?? "",
            isComplete: item.userIsComplete,
          ));
        },
        child: CommonCard(
            padding: EdgeInsets.zero,
            child: SizedBox(
                height: 165,
                width: double.infinity,
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(5.0),
                    child: CachedNetworkImage(
                        imageUrl: prizeImgUrl,
                        placeholder: (context, url) => Image.asset(
                              DImages.formatPathPng('img_placeholder_bg'),
                              fit: BoxFit.fitHeight,
                            ),
                        errorWidget: (context, url, error) => Image.asset(
                              DImages.formatPathPng('img_placeholder_bg'),
                              fit: BoxFit.fitHeight,
                            ),
                        fit: BoxFit.fitHeight)))));
  }

  Widget _buildDetailSummary(ChallengeDetail item) {
    return Column(
      children: [
        SizedBox(height: 8),
        Text(
          item.title ?? "",
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        _buildChallengeInfo(item),
      ],
    );
  }

  Widget _buildChallengeInfo(ChallengeDetail item) {
    DateTime startTime = item.startTime != null
        ? DateTime.parse(item.startTime ?? '')
        : DateTime.now();
    DateTime endTime = item.endTime != null
        ? DateTime.parse(item.endTime ?? '')
        : DateTime.now();
    final difference = max(0, endTime.difference(DateTime.now()).inDays);

    return CommonCard(
        child: Row(
      children: [
        DateTimeItem(dateTime: endTime),
        SizedBox(width: 8),
        Expanded(
            child: Column(
          children: [
            Row(
              children: [
                Expanded(
                    child: Text(
                  getChallengeStatus(item),
                  style: TextStyle(fontSize: 14),
                )),
                RichText(
                  text: TextSpan(
                    children: <TextSpan>[
                      TextSpan(
                          text: '剩余 ',
                          style: TextStyle(color: Colors.black, fontSize: 14)),
                      TextSpan(
                        text: '$difference',
                        style: TextStyle(
                          color: DColor.primary,
                          fontFamily: DFamily.dinBold,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextSpan(
                          text: ' 天',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                          )),
                    ],
                  ),
                )
              ],
            ),
            SizedBox(height: 8),
            ChallengeTargetList(item.mileageTarget, item.altitudeTarget,
                item.durationTarget, item.timesTarget)
          ],
        ))
      ],
    ));
  }

  String getChallengeStatus(ChallengeDetail item) {
    int targetCount = 0;

    if (item.mileageTarget != null) targetCount++;
    if (item.altitudeTarget != null) targetCount++;
    if (item.durationTarget != null) targetCount++;
    if (item.timesTarget != null) targetCount++;

    if (targetCount >= 2) {
      return "完成综合挑战";
    } else if (item.mileageTarget != null) {
      return "完成距离挑战";
    } else if (item.altitudeTarget != null) {
      return "完成爬升挑战";
    } else if (item.durationTarget != null) {
      return "完成时长挑战";
    } else if (item.timesTarget != null) {
      return "完成次数挑战";
    } else {
      return "没有挑战目标";
    }
  }

  Widget _buildMemberInfo() {
    bool hasFinishUser = item.value.accomplishHeadPortraits?.isNotEmpty ?? false;
    bool hasProgressUser = item.value.signUpHeadPortraits?.isNotEmpty ?? false;
    if (!hasFinishUser && !hasProgressUser) {
      return Container();
    }
    return CommonCard(
        child: Column(
      children: [
        Visibility(
            visible: hasFinishUser,
            child: Container(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "已完成",
                      style: TextStyle(fontSize: 12),
                    ),
                    SizedBox(height: 5),
                    SeriesCircleProfile(
                        imageUrls: item.value.accomplishHeadPortraits ?? <String>[]),
                    SizedBox(height: 20)
                  ],
                ))),
        Visibility(
            visible: hasProgressUser,
            child: Container(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "已报名",
                      style: TextStyle(fontSize: 12),
                    ),
                    SizedBox(height: 5),
                    SeriesCircleProfile(
                        imageUrls: item.value.signUpHeadPortraits ?? <String>[]),
                  ],
                )))
      ],
    ));
  }

  Widget _buildShareAction() {
    return Account.isLogin()
        ? GestureDetector(
            onTap: () async {
              WechatUtils.shareMiniProgram(
                  context, "pages/challenge/info/detail/index?id=${widget.id}",
                  title: item.value.title ?? "",
                  description: "",
                  imageUrl: item.value.wxMiniShareImg ?? item.value.logo ?? "");
            },
            child: Padding(
                padding: EdgeInsets.only(right: 15),
                child: Image.asset(
                  "assets/images/icon_share.png",
                  height: 20,
                  width: 20,
                )))
        : Container();
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      children: [Expanded(child: _buildList(context)), _buildSubmit()],
    );
  }

  Widget _buildTargetCompleted() {
    DateTime providedEndTime = item.value.endTime != null
        ? DateTime.parse(item.value.endTime ?? '')
        : DateTime.now();
    bool isInProgress = !(item.value.userIsComplete ?? false);
    String? expiredTimeText =
        "将在${DateFormat('yyyy.MM.dd HH:mm:ss').format(providedEndTime)}自动完赛";

    return BoxShadowContent(
        child: Column(
      children: [
        Visibility(
            visible: isInProgress,
            child: Padding(
                padding: EdgeInsets.only(bottom: 10),
                child: Text(
                  expiredTimeText,
                  style: TextStyle(color: Color(0xFF6B6B6B), fontSize: 14),
                ))),
        SizedBox(
            width: double.infinity,
            child: SubmitButton(
                enable: isInProgress,
                backgroundColor: isInProgress ? DColor.primary : Colors.white,
                borderColor: isInProgress ? DColor.transparent : DColor.primary,
                textColor: isInProgress ? DColor.white : DColor.primary,
                text: isInProgress ? "立即确认完赛" : "已完成",
                onPressed: () async {
                  if (isInProgress) {
                    showNormalDialog(() async {
                      bool result = await showRideBriefConfirmBottomSheet(
                              context, item.value.registrationRecordId ?? "") ??
                          false;

                      if (result) {
                        refreshController.requestRefresh();
                      }
                    },
                        title: "温馨提示",
                        content: "您已达成所有挑战目标，是否确认完赛",
                        confirmText: "确认完赛",
                        cancelText: "再想想");
                  }
                }))
      ],
    ));
  }

  Widget _buildSubmit() {
    bool isCompleted = item.value.userIsComplete ?? false;
    if (isCompleted) {
      return _buildTargetCompleted();
    }
    if (item.value.startTime == null) {
      return Container();
    }
    DateTime raceStartTime = DateTime.parse(item.value.startTime ?? '');
    DateTime raceEndTime = DateTime.parse(item.value.endTime ?? '');
    String startTime = DateFormat('yyyy.MM.dd').format(raceStartTime);
    String endTime = DateFormat('yyyy.MM.dd').format(raceEndTime);

    DateTime registrationStartTime =
        DateTime.parse(item.value.registrationStartTime ?? '');
    DateTime registrationEndTime =
        DateTime.parse(item.value.registrationEndTime ?? '');

    bool isRegisterFinish = DateTime.now().millisecondsSinceEpoch >
        registrationEndTime.millisecondsSinceEpoch;
    bool isRegisterStart = DateTime.now().millisecondsSinceEpoch >
        registrationStartTime.millisecondsSinceEpoch;
    bool isPendingRegister = DateTime.now().millisecondsSinceEpoch <
        registrationStartTime.millisecondsSinceEpoch;
    bool isRegistering = isRegisterStart && !isRegisterFinish; //正在报名中
    bool isRaceFinish = DateTime.now().millisecondsSinceEpoch >
        raceEndTime.millisecondsSinceEpoch;
    bool isJoined = item.value.userSignUpFlag ?? false; //是否已加入
    bool allowJoin = item.value.allowJoin ?? false; //是否允许加入
    bool isShowJoin = isRegistering && !isJoined;

    String text = "";
    Color textColor = DColor.color_ff999999;
    Color borderSideColor = DColor.color_ff999999;
    if (isPendingRegister) {
      text = "即将开放";
    } else if (isRegistering) {
      if (isJoined) {
        text = "进行中";
        textColor = DColor.primary;
        borderSideColor = DColor.primary;
      } else {
        text = "加入";
        textColor = DColor.white;
        borderSideColor = DColor.transparent;
      }
    } else if (isRaceFinish) {
      text = "活动结束";
    } else if (isRegisterFinish) {
      text = "报名截止";
    }

    String priceContent = "";
    if ((item.value.price ?? 0) > 0) {
      priceContent = "(￥${(item.value.price ?? 0)})";
    }

    return Visibility(
        visible: isShowJoin,
        child: BoxShadowContent(
            child: SubmitButton(
                backgroundColor:
                    isRegistering ? DColor.primary : Color(0xFF999999),
                // enable: isRegistering,
                text: "$text $priceContent",
                onPressed: () async {
                  if (allowJoin) {
                    _doJoin(context);
                  } else {
                    NormalDialog.showNormalDialog(
                        context, "温馨提示", item.value.overRangeTips ?? "");
                  }
                })));
  }

  Future<void> _doJoin(BuildContext context) async {
    if (!Account.isLogin()) {
      pushPage(context, LoginInputPhonePage());
      return;
    }

    bool isShowSignTip = item.value.successPrizeToStoreFlag ?? false;
    if (isShowSignTip) {
      bool result =
          await showSignTipConfirmBottomSheet(context, item.value) ?? false;
      if (result) {
        _startJoin();
      }
    } else {
      _startJoin();
    }
  }

  void _startJoin() async {
    String richTextLiabilityWaiver = item.value.richTextLiabilityWaiver ?? "";
    if (richTextLiabilityWaiver.isNotEmpty &&
        !await DisclaimerDialog.showDisclaimerDialog(
            context, "免责声明", richTextLiabilityWaiver ?? "",
            disAgreeText: "放弃报名")) {
      return;
    }
    if ((item.value.price ?? 0) > 0) {
      Get.to(ChallengeOrderPaymentPage(id: widget.id ?? ""),
          transition: Transition.rightToLeft);
      return;
    }
    showLoadingDialogWithFuture(
      context,
      logic.joinChallenge(item.value.id ?? ""),
    ).then((result) async {
      if (result?.success ?? false) {
        showToast('您已成功加入！', context);
        logic.refreshData(item.value.id ?? "");
      } else {
        showToast(result?.message ?? '加入失败, 请稍后再试!', context);
      }
    });
  }

  Widget _buildDetailImage() {
    return CommonCard(
        padding: EdgeInsets.zero,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                child: Text(
                  "挑战介绍",
                  style: TextStyle(fontSize: 14),
                )),
            HtmlContent(
              context,
              item.value.richTextDescriptions ?? "",
            ),
          ],
        ));
  }

  Widget _buildTarget(BuildContext context) {
    bool userIsComplete =
        item.value.userIsComplete??false;

    String? id = item.value.registrationRecordId ?? "";
    return CommonCard(
        child: Column(
      children: [
        Visibility(
            visible: (item.value.mileageTarget ?? 0) > 0,
            child: _buildTargetItem(
                context,
                "骑行里程",
                "mileage_black",
                formatMToKm(item.value.userMileageTarget ??
                    0),
                formatMToKm(item.value.mileageTarget ?? 0),
                (item.value.mileageTarget ??
                        0) /
                    (item.value.mileageTarget ?? 0),
                "km",
                fractionDigits: 2)),
        Visibility(
            visible: (item.value.altitudeTarget ?? 0) > 0,
            child: _buildTargetItem(
                context,
                "海拔爬升",
                "altitude_black",
                (item.value.userAltitudeTarget ??
                        0)
                    .toString(),
                (item.value.altitudeTarget ?? 0).toString(),
                (item.value.altitudeTarget ??
                        0) /
                    (item.value.altitudeTarget ?? 0),
                "m")),
        Visibility(
            visible: (item.value.durationTarget ?? 0) > 0,
            child: _buildTargetItem(
                context,
                "骑行时长",
                "duration_black",
                formatRideSecondsWithUnit(item
                        .value
                        .durationTarget ??
                    0),
                formatSecToH((item.value.durationTarget ?? 0).toDouble(),
                    fractionDigits: 0),
                (item.value.userDurationTarget??
                        0) /
                    (item.value.durationTarget ?? 0),
                "h",
                fractionDigits: 1)),
        Visibility(
            visible: (item.value.timesTarget ?? 0) > 0,
            child: _buildTargetItem(
                context,
                "骑行次数",
                "times_black",
                (item.value.timesTarget ??
                        0)
                    .toString(),
                (item.value.timesTarget ?? 0).toString(),
                (item.value.userTimesTarget ??
                        0) /
                    (item.value.timesTarget ?? 0),
                "次",
                unitFamily: DFamily.pingFangSc,
                unitFontSize: 11,
                unitPaddingBottom: 2)),
        GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              Get.to(ChallengeRideBriefPage(id: id, isComplete: userIsComplete),
                  transition: Transition.rightToLeft);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text("骑行数据",
                    style: TextStyle(fontSize: 14, color: DColor.primary)),
                SizedBox(width: 8),
                Image.asset(
                  height: 14,
                  color: DColor.primary,
                  DImages.formatPathPng("jiantou_right_grey"),
                  fit: BoxFit.fitHeight,
                )
              ],
            )),
      ],
    ));
  }

  Widget _buildTargetItem(BuildContext context, String title, String assetName,
      String? current, String? target, double progress, String unit,
      {fractionDigits = 0,
      String? unitFamily,
      double? unitFontSize,
      double? unitPaddingBottom}) {
    return Column(
      children: [
        Row(
          children: [
            Image.asset(
              DImages.formatPathPng(assetName),
              height: 23,
              fit: BoxFit.cover,
            ),
            SizedBox(width: 13),
            Expanded(
                child: Text(
              title,
              style: TextStyle(fontSize: 14),
            )),
            RichText(
              text: TextSpan(
                children: <TextSpan>[
                  TextSpan(
                    text: (progress < 1.0) ? '${current ?? ""}/' : '',
                    style: TextStyle(
                      color: DColor.primary,
                      fontFamily: DFamily.dinBold,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextSpan(
                      text: '${target ?? ""}',
                      style: TextStyle(
                          fontFamily: DFamily.dinBold,
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.bold)),
                ],
              ),
            ),
            Padding(
                padding: EdgeInsets.only(bottom: unitPaddingBottom ?? 0),
                child: Text('$unit',
                    style: TextStyle(
                      color: DColor.black,
                      fontSize: unitFontSize ?? 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: unitFamily ?? DFamily.dinBold,
                    ))),
          ],
        ),
        CustomSliderWidget(value: progress * 100),
      ],
    );
  }
}
