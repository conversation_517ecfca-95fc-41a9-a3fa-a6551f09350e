import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_info.dart';
import 'package:new_edge/pages/base/page_controller.dart';
import 'package:new_edge/pages/games/challenge/challenge_list_state.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:geolocator/geolocator.dart';

class ChallengeListLogic
    extends PagingController<ChallengeInfo, ChallengeListState> {
  String? tenantId;

  @override
  ChallengeListState getState() => ChallengeListState();

  @override
  Future<ApiResponse<ApiPaging<ChallengeInfo>>?> loadData(
      int pagingIndex) {
    return ActiviesApi(MyHttp.dio).getActiviesChallengeList(
        pageNum: pagingIndex, pageSize: 30, tenantId: tenantId);
  }
}
