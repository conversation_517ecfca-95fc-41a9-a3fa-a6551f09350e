import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/util/string_format_utils.dart';

Widget ChallengeTargetList(num? mileageTarget, num? altitudeTarget,
    num? durationTarget, num? timesTarget) {
  return LayoutBuilder(builder: (context, constraints) {
    double width = constraints.maxWidth / 4;
    List<Widget> widgets = [];
    if (mileageTarget != null) {
      widgets.add(_ChallengeTargetItem(
          "mileage_none", "${formatMToKm(mileageTarget ?? 0)}", "km"));
    }
    if (altitudeTarget != null) {
      widgets
          .add(_ChallengeTargetItem("altitude_none", "${altitudeTarget}", "m"));
    }
    if (durationTarget != null) {
      widgets.add(_ChallengeTargetItem("duration_none",
          "${formatSecToH((durationTarget).toDouble(), fractionDigits:0)}", "h"));
    }
    if (timesTarget != null) {
      widgets.add(_ChallengeTargetItem("times_none", "${timesTarget}", "次",
          unitFamily: DFamily.pingFangSc,
          unitFontSize: 10,
          unitPaddingBottom: 4));
    }

    for (int i = 0; i < 4 - widgets.length; i++) {
      widgets.add(Container());
    }

    return Row(
        children:
            widgets.map((e) => SizedBox(width: width, child: e)).toList());
  });
}

Widget _ChallengeTargetItem(String assertName, String title, String unit,
    {String? unitFamily, double? unitFontSize, double? unitPaddingBottom}) {
  return Column(children: [
    Image.asset(
      DImages.formatPathPng(assertName),
      width: 22,
      height: 19,
    ),
    SizedBox(height: 5),
    Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(title,
            style: TextStyle(
              color: DColor.ff232323,
              fontSize: 15,
              fontFamily: DFamily.dinBold,
            )),
        Padding(
            padding: EdgeInsets.only(bottom: unitPaddingBottom ?? 0),
            child: Text(" $unit",
                style: TextStyle(
                  color: DColor.ff232323,
                  fontSize: unitFontSize ?? 15,
                  fontWeight: FontWeight.bold,
                  fontFamily: unitFamily ?? DFamily.dinBold,
                ))),
      ],
    )
  ]);
}
