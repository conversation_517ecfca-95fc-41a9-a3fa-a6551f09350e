import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_info.dart';
import 'package:new_edge/pages/games/challenge/challenge_target_view.dart';
import 'package:new_edge/pages/login/login_input_phone.dart';
import 'package:new_edge/routers/router.dart';
import 'package:new_edge/util/navigator.dart';

class ChallengeItem extends StatelessWidget {
  final ChallengeInfo item;
  final bool isSelected;
  final Function()? onTap;
  final Function()? onJoinClicked;

  const ChallengeItem(
      {Key? key,
      required this.item,
      this.onJoinClicked,
      this.onTap,
      this.isSelected = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    DateTime raceEndTime = DateTime.parse(item.endTime ?? '');
    bool isRaceFinish = DateTime.now().millisecondsSinceEpoch >
        raceEndTime.millisecondsSinceEpoch;

    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap ??
            () {
              Get.toNamed(RouteGet.challengeDetail, arguments: {
                'id': item.id,
              });
            },
        child: Column(
          children: [
            Container(height: 8),
            Material(
                color: DColor.white,
                shadowColor: Color(0xFF000000).withOpacity(0.16),
                elevation: 5,
                child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: isSelected ? DColor.primary : Colors.transparent,
                        width: 2.0,
                      ),
                    ),
                    padding: EdgeInsets.only(left: 24, top: 10, bottom: 10),
                    child: Column(
                      children: [
                        _buildHeaderItem(item.type == 1, isRaceFinish),
                        SizedBox(
                          height: 8,
                        ),
                        Row(
                          children: [
                            SizedBox(
                                width: 100,
                                height: 100,
                                child: ClipRRect(
                                    borderRadius: BorderRadius.circular(5.0),
                                    child: CachedNetworkImage(
                                        imageUrl: item.logo ?? "",
                                        placeholder: (context, url) =>
                                            Image.asset(
                                              DImages.formatPathPng(
                                                  'img_placeholder_bg'),
                                              fit: BoxFit.cover,
                                            ),
                                        errorWidget: (context, url, error) =>
                                            Image.asset(
                                              DImages.formatPathPng(
                                                  'img_placeholder_bg'),
                                              // Use placeholder as error widget
                                              width: double.infinity,
                                              fit: BoxFit.cover,
                                            ),
                                        fit: BoxFit.cover))),
                            SizedBox(width: 10),
                            Expanded(
                              child: Container(
                                  alignment: Alignment.topLeft,
                                  child: _buildDetailItem(item)),
                            )
                          ],
                        ),
                      ],
                    ))),
          ],
        ));
  }

  Widget _buildAddItem(BuildContext context) {
    DateTime raceStartTime = DateTime.parse(item.startTime ?? '');
    DateTime raceEndTime = DateTime.parse(item.endTime ?? '');
    String startTime = DateFormat('yyyy.MM.dd').format(raceStartTime);
    String endTime = DateFormat('yyyy.MM.dd').format(raceEndTime);

    DateTime registrationStartTime =
        DateTime.parse(item.registrationStartTime ?? '');
    DateTime registrationEndTime =
        DateTime.parse(item.registrationEndTime ?? '');

    bool isRegisterFinish = DateTime.now().millisecondsSinceEpoch >
        registrationEndTime.millisecondsSinceEpoch;
    bool isRegisterStart = DateTime.now().millisecondsSinceEpoch >
        registrationStartTime.millisecondsSinceEpoch;
    bool isPendingRegister = DateTime.now().millisecondsSinceEpoch <
        registrationStartTime.millisecondsSinceEpoch;
    bool isRegistering = isRegisterStart && !isRegisterFinish;
    bool isRaceFinish = DateTime.now().millisecondsSinceEpoch >
        raceEndTime.millisecondsSinceEpoch;
    bool isJoined = item.userSignUpFlag ?? false;
    bool isEnableJoin = isRegistering && !isJoined;

    String text = "";
    Color textColor = DColor.color_ff999999;
    Color borderSideColor = DColor.color_ff999999;
    if (isPendingRegister) {
      text = "即将开放";
      textColor = DColor.primary;
      borderSideColor = DColor.primary;
    } else if (isRegistering) {
      if (isJoined) {
        text = "正在报名";
        textColor = DColor.primary;
        borderSideColor = DColor.primary;
      } else {
        text = "加入";
        textColor = DColor.white;
        borderSideColor = DColor.transparent;
      }
    } else if (isRaceFinish) {
      text = "活动结束";
    } else if (isRegisterFinish) {
      text = "报名截止";
    }

    return MaterialButton(
      minWidth: double.infinity,
      padding: EdgeInsets.only(left: 6, right: 6, top: 9, bottom: 9),
      onPressed: isEnableJoin
          ? () {
              if (!Account.isLogin()) {
                pushPage(context, LoginInputPhonePage());
                return;
              }
              onJoinClicked?.call();
            }
          : null,
      child: Text(
        text,
        style: TextStyle(color: textColor, fontSize: 16),
      ),
      shape: RoundedRectangleBorder(
        //边框颜色
        side: BorderSide(
          color: borderSideColor,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(3.0),
      ),
      color: DColor.primary,
      disabledColor: Colors.white,
    );
  }

  Widget _buildHeaderItem(bool isSpecial, bool isFinish) {
    return Row(
      children: [
        Text(
          isSpecial ? '专属挑战' : '挑战',
          style: TextStyle(
              color: isSpecial ? DColor.primary : Color(0xFF666666),
              fontSize: 13),
        ),
        Expanded(
            child: isSpecial ? _buildSpecialHeaderContainer() : Container()),
        Padding(
            padding: EdgeInsets.only(
              right: isFinish ? 0 : 14,
            ),
            child: Image.asset(
              DImages.formatPathPng(isFinish ? 'icon_finish' : 'icon_hot'),
              height: 20,
            )),
        Visibility(
            visible: isSelected,
            child: Padding(
                padding: EdgeInsets.only(right: 15),
                child: Image.asset(
                  DImages.formatPathPng('selected'),
                  height: 15,
                  width: 15,
                  fit: BoxFit.cover,
                )))
      ],
    );
  }

  Widget _buildSpecialHeaderContainer() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildSpecialItem("icon_location", "西安"),
        SizedBox(width: 5),
        _buildSpecialItem("icon_bike", "specialized"),
        SizedBox(width: 5),
        _buildSpecialItem("icon_people", "指定人群"),
      ],
    );
  }

  Widget _buildSpecialItem(String assertName, String title) {
    return Row(
      children: [
        Image.asset(
          DImages.formatPathPng(assertName),
          height: 10,
        ),
        SizedBox(width: 5),
        Text(
          title,
          style: TextStyle(color: Color(0xFF999999), fontSize: 9),
        ),
      ],
    );
  }

  Widget _buildDetailItem(ChallengeInfo item) {
    String startTime =
        DateFormat('yyyy.MM.dd').format(DateTime.parse(item.startTime ?? ''));
    String endTime =
        DateFormat('yyyy.MM.dd').format(DateTime.parse(item.endTime ?? ''));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
            padding: EdgeInsets.only(right: 10),
            child: Text(
              item.title ?? "",
              style: TextStyle(fontSize: 14, fontFamily: DFamily.pingFangSc),
            )),
        SizedBox(height: 7),
        Text(
          '$startTime-$endTime',
          style: TextStyle(
              fontSize: 16,
              fontFamily: DFamily.dinBold,
              fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 10),
        Padding(
            padding: EdgeInsets.only(right: 10),
            child: ChallengeTargetList(item.mileageTarget, item.altitudeTarget,
                item.durationTarget, item.timesTarget))
      ],
    );
  }
}
