import 'dart:async';

import 'package:alipay_kit/alipay_kit_platform_interface.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/activities_discount_code.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/payment_order.dart';
import 'package:new_edge/pages/games/challenge/challenge_detail_logic.dart';
import 'package:new_edge/pages/games/challenge/challenge_detail_sub_view.dart';
import 'package:new_edge/pages/games/challenge/signup/challenge_payresult_page.dart';
import 'package:new_edge/pages/games/challenge/signup/challenge_registration_logic.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/pay/alipay_utils.dart';
import 'package:new_edge/util/pay/payment_method.dart';
import 'package:new_edge/util/pay/wechat_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/NormalWidget.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/date_time_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:wechat_kit/wechat_kit_platform_interface.dart';

class ChallengeOrderPaymentPage extends StatefulWidget {
  final String id;

  ChallengeOrderPaymentPage({required this.id}) {}

  @override
  _ChallengeOrderPaymentState createState() => _ChallengeOrderPaymentState();
}

class _ChallengeOrderPaymentState extends State<ChallengeOrderPaymentPage>
    with WidgetsBindingObserver {
  final logic = Get.find<ChallengeRegistrationLogic>();
  final detailLogic = Get.find<ChallengeDetailLogic>();
  final item = Get.find<ChallengeDetailLogic>().item;

  final paymentMethod = Get.find<ChallengeRegistrationLogic>().paymentMethod;
  final discountCode = Get.find<ChallengeRegistrationLogic>().discountCode;

  late final StreamSubscription<WechatResp> _wechatSubs;
  late final StreamSubscription<AlipayResp> _alipaySubs;

  void _listenPay(AlipayResp resp) {
    print("alipay response: $resp");
    // _toPayResult(resp.isSuccessful);
  }

  void _listenResp(WechatResp resp) {
    print("wechat response: $resp");
    if (resp is WechatPayResp) {
      // _toPayResult(resp.isSuccessful);
    }
  }

  @override
  void initState() {
    super.initState();
    _wechatSubs = WechatKitPlatform.instance.respStream().listen(_listenResp);
    _alipaySubs = AlipayKitPlatform.instance.payResp().listen(_listenPay);

    WidgetsBinding.instance.addObserver(this);

    _queryWhitePrice();
  }

  void _queryWhitePrice() async {
    showLoadingDialogWithFuture(
        context, logic.queryWhiteByLinkedId(widget.id ?? ""),
        barrierDismissible: false)
        .then((result) async {})
        .catchError((error) {
      if (error is DioException) {
        showToast(error.message.toString(), context);
      }
    });
  }

  @override
  void dispose() {
    _wechatSubs.cancel();
    _alipaySubs.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkPayResult();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff232323,
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                Navigator.pop(context, false);
              },
            ),
            title: Text("确认订单",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: Obx(() => Column(
              children: [Expanded(child: _buildList()), _buildSubmitContent()],
            )));
  }

  Widget _buildSubmitContent() {
    return BoxShadowContent(
      child: Container(
        width: MediaQuery.of(context).size.width,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            RichText(
              text: TextSpan(children: [
                TextSpan(
                  text: "总计: ",
                  style: TextStyle(color: Color(0xFF333333), fontSize: 16),
                ),
                TextSpan(
                    text:
                        "¥${logic.getTotalPrice(item.value).toStringAsFixed(2)}",
                    style: TextStyle(
                        color: DColor.primary,
                        fontFamily: DFamily.dinBold,
                        fontSize: 20)),
              ]),
            ),
            MaterialButton(
              padding:
                  EdgeInsets.only(top: 10, bottom: 10, left: 50, right: 50),
              onPressed: () {
                showNormalDialog(
                  () {
                    _submitOrder();
                  },
                  title: "温馨提示",
                  content: "请仔细阅读免责声明，\n 支付后无法取消及退款，\n 想清楚再支付哦~",
                  confirmText: "确认支付",
                );
              },
              child: Text(
                "立即支付",
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              shape: RoundedRectangleBorder(
                //边框颜色
                side: BorderSide(
                  color: Colors.transparent,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(5.0),
              ),
              color: DColor.primary,
              disabledColor: DColor.primary.withOpacity(0.5),
            )
          ],
        ),
      ),
    );
  }

  void _submitOrder() async {
    showLoadingDialogWithFuture(context, logic.submit(),
            barrierDismissible: false, delayTime: const Duration(seconds: 2))
        .then((result) async {
      if (result.success == true) {
        showToast('订单创建成功！', context);

        _orderPay();
      } else {
        showToast(result.message ?? '订单创建失败！', context);
      }
    }).catchError((error) {
      if (error is DioException) {
        showToast(error.message.toString(), context);
      }
    });
  }

  void _checkPayResult() async {
    if (!logic.isPaying.value) {
      return;
    }

    logic.isPaying.value = false;
    showLoadingDialogWithFuture(context, logic.queryByOrderNumber(),
            delayTime: const Duration(seconds: 2), barrierDismissible: false)
        .then((result) async {
      PaymentOrder? order = result.result;
      if (result.success == true && order?.paymentStatus == 2) {
        _toPayResult(true);
      } else {
        _toPayResult(false);
      }
    }).catchError((error) {
      if (error is DioException) {
        showToast(error.message.toString(), context);
      }
      _toPayResult(false);
    });
  }

  void _orderPay() async {
    if (logic.paymentMethod.value == PaymentMethod.Weixin) {
      if (!await WechatUtils.isInstalled()) {
        showToast("请先安装微信", context);
        return;
      }
      showLoadingDialogWithFuture(context, logic.payWechat(),
              barrierDismissible: false)
          .then((result) async {
        if (result.success == true) {
          logic.isPaying.value = true;
          await WechatUtils.pay(result.result!);
        } else {
          logic.isPaying.value = false;
          showToast(result.message ?? '订单支付失败！', context);
        }
      }).catchError((error) {
        if (error is DioException) {
          showToast(error.message.toString(), context);
        }
        logic.isPaying.value = false;
      });
    } else if (logic.paymentMethod.value == PaymentMethod.Alipay) {
      if (!await AlipayUtils.isInstalled()) {
        showToast("请先安装支付宝", context);
        return;
      }
      showLoadingDialogWithFuture(context, logic.payAli(),
              barrierDismissible: false)
          .then((result) async {
        if (result.success == true) {
          logic.isPaying.value = true;
          await AlipayUtils.pay(result.result ?? "");
        } else {
          logic.isPaying.value = false;
          showToast(result.message ?? '订单支付失败！', context);
        }
      }).catchError((error) {
        if (error is DioException) {
          showToast(error.message.toString(), context);
        }
        logic.isPaying.value = false;
      });
    }
  }

  Widget _buildList() {
    return Container(
        padding: EdgeInsets.only(left: 10, right: 10, top: 15),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
                child: Column(
              children: [
                buildChallengeTip(item.value.endTime),
                _buildGroupInfo(item.value),
              ],
            )),
            SliverToBoxAdapter(
                child: Column(
              children: [
                _buildPaymentInfo(),
              ],
            )),
          ],
        ));
  }

  Widget _buildPaymentInfo() {
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "支付方式",
          style: TextStyle(color: Color(0xFF666666), fontSize: 14),
        ),
        SizedBox(height: 20),
        _buildPaymentItem(PaymentMethod.Weixin),
        SizedBox(height: 20),
        _buildPaymentItem(PaymentMethod.Alipay)
      ],
    ));
  }

  Widget _buildPaymentItem(PaymentMethod method) {
    bool isSelected = method == paymentMethod.value;
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          logic.setPaymentMethod(method);
        },
        child: Row(children: [
          Image.asset(height: 19, DImages.formatPathPng(method.iconName)),
          SizedBox(width: 8),
          Text(method.title,
              style: TextStyle(color: Color(0xFF999999), fontSize: 13)),
          Expanded(child: Container()),
          Image.asset(
            // ignore: unrelated_type_equality_checks
            isSelected
                ? DImages.formatPathPng('selected')
                : DImages.formatPathPng('no_selected'),
            height: 15,
            width: 15,
            fit: BoxFit.cover,
          )
        ]));
  }

  Widget _buildModifyAction(VoidCallback onModify) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        onModify();
      },
      child: Padding(
          padding: EdgeInsets.all(5),
          child: Image.asset(height: 15, DImages.formatPathPng("icon_modify"))),
    );
  }

  Widget _buildEndInfo(ActivitiesChallenge group) {
    DateTime endTime = item.value.endTime != null
        ? DateTime.parse(item.value.endTime ?? '')
        : DateTime.now();

    String endTimeContent = DateFormat('MM月dd日').format(endTime);
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "结束时间",
          style: TextStyle(color: Color(0xFF666666), fontSize: 14),
        ),
        SizedBox(height: 14),
        Row(children: [
          DateTimeItem(dateTime: endTime),
          SizedBox(width: 8),
          Expanded(child: Text("需要在$endTimeContent前上传您的骑行数据 数据上传会有延迟请您不要踩点上传"))
        ])
      ],
    ));
  }

  Widget _buildGroupInfo(ChallengeDetail group) {
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildGroupItem(group),
      ],
    ));
  }

  Widget buildGroupItem(ChallengeDetail group) {
    num? price = logic.getGroupPrice(group);
    bool hasDiscount = logic.hasDiscount(group);

    return Row(
      children: [
        Expanded(child: Container(
            padding: EdgeInsets.only(top: 7, bottom: 7),
            child: Text(
              maxLines:2,
              overflow: TextOverflow.ellipsis,
              group.title ?? "",
              style: TextStyle(color: DColor.ff242424, fontSize: 14),
            ))),
        SizedBox(width: 4),
        hasDiscount
            ? _buildAlreadyDiscount()
            : _buildAddGroupDiscountInfo(group),
        SizedBox(width: 10),
        _buildDiscountPrice(
            group.price ?? 0, price, logic.getGroupPrice(group))
      ],
    );
  }

  Widget _buildAddGroupDiscountInfo(ChallengeDetail group) {
    return GestureDetector(
        onTap: () {
          showDiscountDialog(context, (value) async {
            showLoadingDialogWithFuture(
              context,
              logic.queryDiscountCode(value),
              barrierDismissible: false,
            ).then((result) async {
              ActivitiesDiscountCode? code = result.result;
              if (result.success == true && code != null) {
                bool bindResult = logic.bindDiscountCodeToGroup(code);
                if (!bindResult) {
                  showToast("优惠码无效", context);
                }
              } else {
                showToast("优惠码请求失败", context);
              }
            });
          });
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "优惠码",
              style: TextStyle(color: Color(0xFF232323), fontSize: 14),
            ),
            SizedBox(width: 5),
            Image.asset(
              DImages.formatPathPng(
                'icon_add_code',
              ),
              height: 20,
              width: 20,
            )
          ],
        ));
  }

  Widget _buildDiscountPrice(
      num origPrice, num? discountPrice, num totalPrize) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Visibility(
            visible: discountPrice != origPrice,
            child: Container(
                padding: EdgeInsets.only(top: 6),
                child: Text(
                  "¥ ${origPrice}",
                  style: TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 15,
                    fontFamily: DFamily.dinBold,
                    decoration: TextDecoration.lineThrough,
                    decorationColor: Colors.grey,
                    decorationThickness: 3.0,
                  ),
                ))),
        SizedBox(width: 8),
        Container(
            padding: EdgeInsets.only(top: 4),
            child: Text(
              "¥ ${totalPrize}",
              style: TextStyle(
                color:
                    discountPrice != null ? DColor.primary : Color(0xFF333333),
                fontSize: 19,
                fontFamily: DFamily.dinBold,
              ),
            )),
      ],
    );
  }

  Widget _buildAlreadyDiscount() {
    return Container(
        padding: EdgeInsets.only(left: 7, right: 7, top: 2, bottom: 2),
        decoration: BoxDecoration(
            border: Border.all(
              color: DColor.primary,
              width: 1.0,
            ),
            borderRadius: BorderRadius.all(Radius.circular(2))),
        child: Text(
          "已优惠",
          style: TextStyle(color: DColor.primary, fontSize: 10),
        ));
  }

  void _toPayResult(bool result) {
    Get.to(ChallengePayResultPage(id: widget.id, isSuccess: result),
        transition: Transition.rightToLeft);
  }
}

Future showDiscountDialog(
    BuildContext context, ValueChanged<String>? onSubmit) {
  return showDialog(
      context: context,
      builder: (BuildContext context) {
        return DiscountDialog(onSubmit: onSubmit);
      });
}

class DiscountDialog extends StatefulWidget {
  final ValueChanged<String>? onSubmit;

  const DiscountDialog({Key? key, this.onSubmit}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _DiscountDialogState();
}

class _DiscountDialogState extends State<DiscountDialog> {
  final _controller = TextEditingController();
  bool _isButtonEnabled = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        child: Container(
          height: 190,
          padding: EdgeInsets.all(15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Image.asset(
                      DImages.formatPathPng(
                        'icon_close_yellow',
                      ),
                      height: 15,
                      width: 15,
                    ),
                  )
                ],
              ),
              Text("优惠码",
                  style: TextStyle(
                      fontSize: 17,
                      color: DColor.ff232323,
                      fontWeight: FontWeight.bold)),
              SizedBox(height: 20),
              _buildTextField(context),
              SizedBox(height: 20),
              _buildSubmit(context)
            ],
          ),
        ));
  }

  Widget _buildSubmit(BuildContext context) {
    return MaterialButton(
      padding: EdgeInsets.only(top: 7, bottom: 7, left: 20, right: 20),
      onPressed: _isButtonEnabled
          ? () {
              widget.onSubmit?.call(_controller.text);
              Navigator.pop(context);
            }
          : null,
      child: Text(
        '确认使用',
        style: TextStyle(color: Colors.white, fontSize: 14),
      ),
      shape: RoundedRectangleBorder(
        //边框颜色
        side: BorderSide(
          color: Colors.transparent,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(3.0),
      ),
      color: DColor.primary,
      disabledColor: DColor.primary.withOpacity(0.5),
    );
  }

  Widget _buildTextField(BuildContext context) {
    return TextField(
      textAlign: TextAlign.center,
      controller: _controller,
      keyboardType: TextInputType.text,
      cursorWidth: 2,
      cursorColor: DColor.primary,
      cursorRadius: Radius.circular(1),
      minLines: 1,
      maxLines: 1,
      style: TextStyle(fontSize: 14, color: DColor.primary),
      decoration: InputDecoration(
          focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: DColor.primary, width: 0.5)),
          enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: DColor.primary, width: 0.5)),
          counterText: "",
          hintText: "请输入",
          hintStyle: TextStyle(fontSize: 14, color: DColor.primary),
          isDense: true,
          contentPadding: EdgeInsets.all(10)),
      onChanged: (text) {
        setState(() {
          // 根据输入字符串的长度更新按钮的可点击状态
          _isButtonEnabled = text.length >= 6;
        });
      },
    );
  }
}
