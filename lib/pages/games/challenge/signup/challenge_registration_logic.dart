import 'dart:collection';

import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/io/pay.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/activities_discount_code.dart';
import 'package:new_edge/model/activities_white_price.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/payment_order.dart';
import 'package:new_edge/model/wx_pay.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/pay/payment_method.dart';

const String REG_SOURCE_DETAIL = "challenge_detail";
const String REG_SOURCE_PAYMENT = "challenge_payment";
const String REG_SOURCE_JOINED_ORDER = "challenge_joined_order";

class ChallengeRegistrationLogic extends GetxController {
  final String id;
  final String source;

  final orderNumber = "".obs;
  final isPaying = false.obs;

  final Rx<ActivitiesDiscountCode?> discountCode =
      Rx<ActivitiesDiscountCode?>(null);
  final paymentMethod = PaymentMethod.Weixin.obs;

  final groupWhitePriceMap = HashMap<String,
      ActivitiesWhitePrice>(); // key:skuCode, value:discountCode;

  ChallengeRegistrationLogic(this.id, this.source) {}

  void setPaymentMethod(PaymentMethod method) {
    paymentMethod.value = method;
  }

  num getTotalPrice(ChallengeDetail item) {
    num total = getGroupPrice(item);

    return total;
  }


  bool hasDiscount(ChallengeDetail group) {
    return getGroupPrice(group) != group.price;
  }

  num getGroupPrice(ChallengeDetail item) {
    ActivitiesDiscountCode? code = discountCode.value;
    ActivitiesWhitePrice? whitePrice = getActivitiesWhitePriceByGroupId(item.id ?? "");
    num? price = item.price ?? 0;
    if (whitePrice != null) {
      price = whitePrice.price;
    } else if (code != null) {
      price = code.priceAfterDiscount;
    }
    return price ?? 0;
  }

  ActivitiesWhitePrice? getActivitiesWhitePriceByGroupId(String groupId) {
    return groupWhitePriceMap[groupId];
  }


  bool bindDiscountCodeToGroup(ActivitiesDiscountCode code) {
    if (id != code.productId) {
      return false;
    }
    discountCode.value = code;
    return true;
  }

  Future<ApiResponse<String>> preResponse() async {
    ApiResponse<String> result = ApiResponse<String>();
    result.code = 0;
    result.result = orderNumber.value;
    result.success = true;
    return result;
  }

  Future<ApiResponse<String>> submit() {
    return _joinChallenge(id).then((value) {
      if (value.success == true) {
        orderNumber.value = value.result ?? "";
      }
      return value;
    });
  }

  Future<ApiResponse<ActivitiesDiscountCode>> queryDiscountCode(
      String discountCode) {
    return ActiviesApi(MyHttp.dio)
        .queryDiscountCode(discountCode: discountCode);
  }

  Future<ApiResponse<WxPay>> payWechat() {
    return PayApi(MyHttp.dio).wxPay(
        orderNumber: orderNumber.value,
        orderType: 2,
        uid: Account.loginAccount?.uid ?? 0);
  }

  Future<ApiResponse<String>> payAli() {
    return PayApi(MyHttp.dio).aliPay(
        orderNumber: orderNumber.value,
        orderType: 2,
        uid: Account.loginAccount?.uid ?? 0);
  }

  Future<ApiResponse<PaymentOrder>> queryByOrderNumber() {
    return ActiviesApi(MyHttp.dio)
        .queryByOrderNumber(orderNumber: orderNumber.value);
  }

  Future<ApiResponse<String>> _joinChallenge(String challengeId) {
    return ActiviesApi(MyHttp.dio).joinChallenge(
        challengeId: challengeId,
        discountCode: discountCode.value?.discountCode ?? "");
  }

  Future<ApiResponse<ActivitiesWhitePrice>> queryWhiteByLinkedId(
      String groupId) {
    return ActiviesApi(MyHttp.dio)
        .queryWhiteByLinkedId(linkedId: groupId)
        .then((value) {
      if (value.success == true) {
        ActivitiesWhitePrice? price = value.result;
        if (price != null) {
          groupWhitePriceMap[groupId] = price;
        }
      }
      return value;
    });
  }


  void clearCache() {
    orderNumber.value = "";
    isPaying.value = false;
  }
}
