import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/app.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/pages/games/challenge/signup/challenge_registration_logic.dart';
import 'package:new_edge/routers/router.dart';
import 'package:new_edge/widgets/submit_button.dart';

class ChallengePayResultPage extends StatefulWidget {
  final String id;
  final bool isSuccess;

  ChallengePayResultPage({required this.id, required this.isSuccess}) {}

  @override
  _ChallengeResultState createState() => _ChallengeResultState();
}

class _ChallengeResultState extends State<ChallengePayResultPage> {
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          back();
          return true;
        },
        child: Scaffold(
            appBar: AppBar(
                centerTitle: true,
                leading: IconButton(
                  icon: Image.asset(
                    DImages.formatPathPng(
                      'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                    color: DColor.ff232323,
                  ),
                  onPressed: () {
                    back();
                  },
                ),
                title: Text("支付结果",
                    style: TextStyle(color: DColor.ff242424, fontSize: 18))),
            body: _buildPayResult(context)));
  }

  Widget _buildPayResult(BuildContext context) {
    return Container(
        width: MediaQuery.of(context).size.width,
        child: Column(children: [
          SizedBox(height: 58),
          Image.asset(
            DImages.formatPathPng(
                widget.isSuccess ? "icon_success" : "icon_failed"),
            width: 70,
            height: 70,
            fit: BoxFit.cover,
          ),
          SizedBox(height: 12),
          Text(
            widget.isSuccess ? "支付成功" : "支付失败",
            style: TextStyle(color: Color(0xFF232323), fontSize: 14),
          ),
          SizedBox(height: 30),
          SubmitButton(
              text: widget.isSuccess ? "返回挑战详情" : "返回订单页面",
              onPressed: () {
                back();
              })
        ]));
  }

  bool isRouteInStack(String routeName) {
    return globalRouterObserver.containRoute(routeName);
  }

  void back() {
    if (widget.isSuccess) {
      //判断是否存在参赛详情页面
      if (isRouteInStack(RouteGet.challengeDetail)) {
        Get.until((route) => route.settings.name == RouteGet.challengeDetail);
      } else {
        Navigator.of(context)
            .pushNamedAndRemoveUntil("/home", (route) => false);

        Get.until((route) => route.settings.name == RouteGet.challengeDetail);
      }
      //清除缓存
      _removeCache();
    } else {
      Get.back();
    }
  }

  void _removeCache() {
    Get.find<ChallengeRegistrationLogic>().clearCache();
  }
}
