import 'dart:math';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/prize_claim_form.dart';
import 'package:new_edge/model/reward_shop.dart';
import 'package:new_edge/pages/games/challenge/store/challenge_choose_store_logic.dart';
import 'package:new_edge/pages/games/challenge/store/challenge_choose_store_page.dart';
import 'package:new_edge/util/geolocator_util.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';

Future showSignTipConfirmBottomSheet(
  BuildContext context,
  ChallengeDetail challenge,
) {
  return showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (BuildContext context) {
      return Container(
        padding: EdgeInsets.only(left: 0, right: 0),
        child: Container(
          color: Colors.white,
          constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7),
          child: ChallengeSignTipPage(
            challenge: challenge,
          ),
        ),
      );
    },
  );
}

class ChallengeSignTipPage extends StatefulWidget {
  final ChallengeDetail challenge;

  ChallengeSignTipPage({required this.challenge}) {}

  @override
  _ChallengeChooseStoreState createState() => _ChallengeChooseStoreState();
}

class _ChallengeChooseStoreState extends State<ChallengeSignTipPage> {
  final logic = Get.put(ChallengeChooseStoreLogic());
  final _loadingState = FitLoadStatus.loading.obs;
  List<ShopInfo?> _nearShopList = [];

  Future<void> _refreshNearCity() async {
    Position? position = await GeolocatorUtils.requestLocationPosition(context);
    logic.position.value = position;

    try {
      _loadingState.value = FitLoadStatus.loading;
      _nearShopList = (await logic.getNearShopList())?.result ?? [];
      _loadingState.value = FitLoadStatus.loadSuccess;
    } catch (e) {
      _loadingState.value = FitLoadStatus.loadError;
    }
  }

  @override
  void initState() {
    logic.challengeId = widget.challenge.id;
    super.initState();

    _refreshNearCity();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => _buildContent(context)),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 10),
        Text(
          "特别提醒",
          style: TextStyle(
              fontSize: 16,
              color: DColor.ff242424,
              fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 10),
        _buildTip(context),
        Expanded(child: _buildNearShops(context)),
        GestureDetector(
          onTap: () {
            Get.to(ChallengeChooseStorePage(
              type: "showSignTipConfirmBottomSheet",
              challenge: widget.challenge,
            ));
          },
          child: Text(
            "查看全部门店",
            style: TextStyle(fontSize: 12, color: DColor.primary),
          ),
        ),
        SizedBox(height: 10),
        _buildSubmit()
      ],
    );
  }

  Widget _buildTip(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(children: [
        TextSpan(
          text: "本次挑战包含实物奖品 \n",
          style: TextStyle(
            fontSize: 13,
            color: DColor.ff242424,
          ),
        ),
        TextSpan(
          text: "可选择“",
          style: TextStyle(
            fontSize: 13,
            color: DColor.ff242424,
          ),
        ),
        TextSpan(
          text: "到店领取",
          style: TextStyle(
            fontSize: 15,
            color: DColor.primary,
          ),
        ),
        TextSpan(
          text: "”和“",
          style: TextStyle(
            fontSize: 13,
            color: DColor.ff242424,
          ),
        ),
        TextSpan(
          text: "邮寄",
          style: TextStyle(
            fontSize: 15,
            color: DColor.primary,
          ),
        ),
        TextSpan(
          text: "”两种领取方式 \n",
          style: TextStyle(
            fontSize: 13,
            color: DColor.ff242424,
          ),
        ),
        TextSpan(
          text: "若选择“邮寄”方式，领奖时需支付",
          style: TextStyle(
            fontSize: 13,
            color: DColor.ff242424,
          ),
        ),
        TextSpan(
          text: "${widget.challenge.deliveryFee}元",
          style: TextStyle(
            fontSize: 15,
            color: DColor.primary,
          ),
        ),
        TextSpan(
          text: "运费",
          style: TextStyle(
            fontSize: 13,
            color: DColor.ff242424,
          ),
        ),
      ]),
    );
  }

  Widget _buildNearShops(BuildContext context) {
    if (_loadingState.value == FitLoadStatus.loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_nearShopList.isEmpty) {
      return buildLoadStateWidget(_loadingState.value, _refreshNearCity);
    }
    return Column(
      children: [
        SizedBox(height: 14),
        Text(
          "距离您最近的三家门店",
          style: TextStyle(fontSize: 12, color: DColor.ff808080),
        ),
        SizedBox(height: 10),
        Expanded(
            child: ListView(
          shrinkWrap: true,
          children: List.generate(
              min(_nearShopList.length, 3),
              (index) => CommonCard(
                    child: StoreInfoView(context, _nearShopList[index]!),
                  )),
        ))
      ],
    );
  }

  Widget _buildSubmit() {
    return Visibility(
        visible: true,
        child: BoxShadowContent(
            child: SubmitButton(
                text: "确认报名",
                onPressed: () async {
                  Get.back(result: true);
                })));
  }
}
