import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart'
    as GetTransition;
import 'package:intl/intl.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/express_detail.dart';
import 'package:new_edge/model/shopping_address.dart';
import 'package:new_edge/pages/address/user_address_logic.dart';
import 'package:new_edge/pages/address/user_address_widget.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/express/express_detail_logic.dart';
import 'package:new_edge/pages/express/express_detail_page.dart';
import 'package:new_edge/pages/games/challenge/award/physical/physical_award_detail_logic.dart';
import 'package:new_edge/pages/games/challenge/challenge_detail_sub_view.dart';
import 'package:new_edge/pages/games/challenge/store/challenge_choose_store_page.dart';
import 'package:new_edge/pages/personal/qrcode/personal_qrcode_detail_page.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';

import 'physical_award_express_page.dart';

class PhysicalAwardDetailPage extends StatefulWidget {
  final ChallengeDetail challenge;

  final PrizeInfo physicalReward;

  PhysicalAwardDetailPage(
      {required this.challenge, required this.physicalReward}) {}

  @override
  _PhysicalAwardDetailState createState() => _PhysicalAwardDetailState();
}

class _PhysicalAwardDetailState extends RefreshState<PhysicalAwardDetailPage> {
  final logic = Get.put(PhysicalAwardDetailLogic());
  final item = Get.find<PhysicalAwardDetailLogic>().item;
  final expressItem = Get.find<PhysicalAwardDetailLogic>().expressItem;

  final addressLogic = Get.put(UserAddressLogic());
  final expressLogic = Get.put(ExpressDetailLogic());

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff232323,
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                Navigator.pop(context, false);
              },
            ),
            title: Text("奖励详情",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: _buildContent(context));
  }

  Widget _buildContent(BuildContext context) {
    bool isProvided = (widget.physicalReward.isProvided ?? 0) == 1;
    String? prizeClaimDeadline = widget.physicalReward.prizeClaimDeadline;
    DateTime providedEndTime = prizeClaimDeadline != null
        ? DateTime.parse(prizeClaimDeadline ?? '')
        : DateTime.now();
    bool isProvidedExpired = DateTime.now().millisecondsSinceEpoch >
        providedEndTime.millisecondsSinceEpoch;
    DateFormat dateFormat = DateFormat('yyyy.MM.dd');

    //method 1 快递，0到店
    bool hasStore = widget.physicalReward.hasStore;
    bool hasExpress = widget.physicalReward.hasExpress;

    return Column(
      children: [
        Expanded(child: _buildList(context)),
        Visibility(
            visible: !isProvided && !isProvidedExpired,
            child: BoxShadowContent(
                padding:
                    EdgeInsets.only(left: 40, right: 40, top: 10, bottom: 10),
                child: Column(
                  children: [
                    Text("请选择领奖方式",
                        style: TextStyle(fontSize: 14, color: DColor.ff232323)),
                    SizedBox(height: 5),
                    Text("请在${dateFormat.format(providedEndTime)}前确认领取方式",
                        style:
                            TextStyle(color: Color(0xFFFF0000), fontSize: 12)),
                    SizedBox(height: 5),
                    Row(
                      children: [
                        Visibility(
                          visible: hasStore,
                          child: Expanded(
                              child: Padding(
                            padding: EdgeInsets.only(left: 5, right: 5),
                            child: _buildStoreSubmitButton(),
                          )),
                        ),
                        Visibility(
                            visible: hasExpress,
                            child: Expanded(
                              child: Padding(
                                padding: EdgeInsets.only(left: 5, right: 5),
                                child: _buildExpressSubmitButton(),
                              ),
                            )),
                      ],
                    )
                  ],
                ))),
      ],
    );
  }

  Widget _buildStoreSubmitButton() {
    String submitText = "到店领取";
    return SubmitButton(
        text: submitText,
        enable: true,
        onPressed: () async {
          await Get.to(ChallengeChooseStorePage(
            challenge: widget.challenge,
            physicalReward: widget.physicalReward,
            type: "PhysicalAwardDetailPage",
          ));

          setState(() {});
        });
  }

  Widget _buildExpressSubmitButton() {
    bool hasFee = (widget.challenge.deliveryFee ?? 0) > 0;
    String submitText = hasFee ? "邮寄(自费)" : "邮寄";
    return SubmitButton(
        text: submitText,
        enable: true,
        onPressed: () async {
          if (hasFee) {
            //检查是否已付费
            await showLoadingDialogWithFuture(context,
                    logic.checkPayDeliveryStatus(widget.challenge.id ?? ""),
                    barrierDismissible: false)
                .then((result) async {
              if (result.success ?? false) {
                await Get.to(PhysicalAwardExpressPage(
                  challenge: widget.challenge,
                  physicalReward: widget.physicalReward,
                  hasPayFee: result.result ?? false,
                ));
              } else {
                await Get.to(PhysicalAwardExpressPage(
                    challenge: widget.challenge,
                    physicalReward: widget.physicalReward));
              }
            });
          } else {
            await Get.to(PhysicalAwardExpressPage(
              challenge: widget.challenge,
              physicalReward: widget.physicalReward,
            ));
          }

          setState(() {});
        });
  }

  String? getRewardDistributionRecordId() {
    return widget.physicalReward.prizeClaimFormId ?? item.value.id;
  }

  void _refresh() {
    logic.refreshData(getRewardDistributionRecordId() ?? "");
  }

  @override
  bool isAutoRefresh() {
    return widget.physicalReward.prizeGetState == null;
  }

  Widget _buildList(BuildContext context) {
    String? rewardDistributionRecordId = getRewardDistributionRecordId();

    return buildRefreshWidget(
        enablePullUp: false,
        enablePullDown: rewardDistributionRecordId != null,
        onRefresh: () {
          _refresh();
        },
        refreshController: refreshController,
        builder: () => CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Obx(() => _buildDetail(context)),
                ),
              ],
            ));
  }

  Widget _buildDetail(BuildContext context) {
    String image = widget.physicalReward.image ?? "";
    String title = widget.physicalReward.name ?? "";
    String summary = widget.physicalReward.description ?? "";
    bool isProvided = (widget.physicalReward.isProvided ?? 0) == 1;

    ShoppingAddress? shoppingAddress;
    if (isProvided) {
      shoppingAddress = logic.getShoppingAddress();
    } else {
      shoppingAddress = addressLogic.isRemoteValid()
          ? addressLogic.remoteAddress.value
          : null;
    }

    bool useStore = widget.physicalReward.type == CollectType.physicalStore ||
        widget.physicalReward.type == CollectType.physicalMailStore;
    bool useExpress = widget.physicalReward.type == CollectType.physicalMail ||
        widget.physicalReward.type == CollectType.physicalMailStore;

    return Column(
      children: [
        ChallengeDetailHeader(widget.challenge),
        Container(
            padding: EdgeInsets.only(left: 10, right: 10, top: 4),
            child: Column(
              children: [
                SizedBox(height: 8),
                Text(
                  widget.challenge.title ?? "",
                  style: TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  height: 8,
                ),
                _buildAwardInfo(image, title, summary),
                Visibility(
                    visible: isProvided && useStore,
                    child: _buildStoreInfo(context)),
                Visibility(
                    visible: isProvided && useExpress,
                    child: _buildExpressInfo(shoppingAddress)),
              ],
            )),
      ],
    );
  }

  Widget _buildExpressInfo(ShoppingAddress? address) {
    return Column(
      children: [
        _buildShippingAddress(address),
        _buildExpressDetail(),
      ],
    );
  }

  Widget _buildExpressDetail() {
    return CommonCard(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text("物流信息", style: TextStyle(fontSize: 14)),
      SizedBox(height: 10),
      item.value.mailVo?.postNum == null
          ? Center(
              child: Padding(
                  padding: EdgeInsets.only(bottom: 10),
                  child: Text("待发货...",
                      style: TextStyle(fontSize: 14, color: DColor.ff808080))))
          : Column(
              children: [
                buildExpressHeader(
                    context,
                    item.value.mailVo?.postCompany ?? "",
                    item.value.mailVo?.postNum ?? ""),
                _buildExpressRecent(),
                SizedBox(height: 10),
                MaterialButton(
                  minWidth: 70,
                  padding:
                      EdgeInsets.only(left: 8, right: 8, top: 9, bottom: 9),
                  onPressed: () {
                    Get.to(
                        ExpressDetailPage(
                            companyName: item.value.mailVo?.postCompany ?? "",
                            company: item.value.mailVo?.postCompanyCode ?? "",
                            expressId: item.value.mailVo?.postNum ?? "",
                            phone: item.value.mailVo?.mobile),
                        transition: GetTransition.Transition.rightToLeft);
                  },
                  color: DColor.primary,
                  child: Text(
                    '查看完整物流进度',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                )
              ],
            )
    ]));
  }

  Widget _buildExpressRecent() {
    if (expressItem.value.data?.isEmpty ?? false) {
      return Container();
    }
    ExpressData? item = expressItem.value.data?.first;
    if (item == null) {
      return Container();
    }
    return buildExpressItem(item);
  }

  Widget _buildAwardInfo(String image, String title, String? summary) {
    return CommonCard(
        child: Column(
      children: [
        Row(
          children: [
            Image.asset(
              DImages.formatPathPng(
                'icon_award',
              ),
              height: 20,
              width: 20,
              color: DColor.ff232323,
            ),
            SizedBox(width: 8),
            Text(
              "赢取奖励",
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        SizedBox(height: 8),
        SizedBox(
            height: 100,
            child: CachedNetworkImage(
                imageUrl: image,
                placeholder: (context, url) => Image.asset(
                      DImages.formatPathPng('img_placeholder_bg'),
                      fit: BoxFit.fitHeight,
                    ),
                errorWidget: (context, url, error) => Image.asset(
                      DImages.formatPathPng('img_placeholder_bg'),
                      fit: BoxFit.fitHeight,
                    ),
                fit: BoxFit.fitHeight)),
        SizedBox(height: 10),
        Text(title, style: TextStyle(color: DColor.ff232323, fontSize: 13)),
        SizedBox(height: 10),
        Visibility(
            visible: summary?.isNotEmpty ?? false,
            child: Padding(
                padding: EdgeInsets.only(top: 8),
                child: Text(summary ?? "",
                    textAlign: TextAlign.center,
                    style:
                        TextStyle(color: DColor.color_ff999999, fontSize: 9)))),
        SizedBox(height: 10),
        _buildAwardStatus()
      ],
    ));
  }

  Widget _buildAwardStatus() {
    bool isProvided = (widget.physicalReward.isProvided ?? 0) == 1;
    String? prizeClaimDeadline = widget.physicalReward.prizeClaimDeadline;
    DateTime pickupStartTime = widget.physicalReward.pickupStartTime != null
        ? DateTime.parse(widget.physicalReward.pickupStartTime ?? '')
        : DateTime.now();
    DateTime pickupEndTime = widget.physicalReward.pickupEndTime != null
        ? DateTime.parse(widget.physicalReward.pickupEndTime ?? '')
        : DateTime.now();
    DateTime providedEndTime = prizeClaimDeadline != null
        ? DateTime.parse(prizeClaimDeadline ?? '')
        : DateTime.now();
    bool isProvidedExpired = DateTime.now().millisecondsSinceEpoch >
        providedEndTime.millisecondsSinceEpoch;
    DateFormat dateFormat = DateFormat('yyyy.MM.dd');

    bool useStore = widget.physicalReward.type == CollectType.physicalStore ||
        widget.physicalReward.type == CollectType.physicalMailStore;
    bool useExpress = widget.physicalReward.type == CollectType.physicalMail ||
        widget.physicalReward.type == CollectType.physicalMailStore;

    //TODO
    bool distributionStatus = false; //奖品是否已发放

    if (!isProvided) {
      return MaterialButton(
        minWidth: 70,
        padding: EdgeInsets.only(left: 8, right: 8, top: 9, bottom: 9),
        onPressed: () {},
        color: DColor.ff808080,
        child: Text(
          '未领取',
          style: TextStyle(color: Colors.white, fontSize: 12),
        ),
      );
    }

    if (useStore) {
      if (distributionStatus) {
        return Column(
          children: [
            Text(
              "已领取",
              style: TextStyle(
                fontSize: 14,
                color: DColor.primary,
              ),
              textAlign: TextAlign.center,
            ),
            _buildDistributionInfo(context),
          ],
        );
      }
      return Column(
        children: [
          MaterialButton(
            minWidth: 70,
            padding: EdgeInsets.only(left: 8, right: 8, top: 9, bottom: 9),
            onPressed: () {
              if (isProvided) {
                Get.to(
                    PersonalQrcodeDetailPage(tip: "请出示给门店工作人员扫码", otherInfo: {
                  'acPhysicalReward': item.value.awardId ?? "",
                }));
              }
            },
            shape: RoundedRectangleBorder(
              //边框颜色
              side: BorderSide(
                color: DColor.primary,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(3.0),
            ),
            color: DColor.primary,
            child: Text(
              '出示领奖码',
              style: TextStyle(color: DColor.white, fontSize: 12),
            ),
          ),
          SizedBox(height: 5),
          RichText(
            textAlign: TextAlign.center, // 设置整个文本居中
            text: TextSpan(
              text:
                  "请在${dateFormat.format(pickupStartTime)}-${dateFormat.format(pickupEndTime)} 前往门店领取奖励\n",
              style: TextStyle(color: Color(0xFFFF0000), fontSize: 12),
              children: [
                TextSpan(
                  text: "具体时间请与门店联系",
                  style: TextStyle(color: Color(0xFFFF0000), fontSize: 12),
                ),
              ],
            ),
          )
        ],
      );
    }

    bool hasSend = item.value.mailVo?.postNum != null;
    if (!useStore) {
      return Column(
        children: [
          MaterialButton(
            minWidth: 70,
            padding: EdgeInsets.only(left: 8, right: 8, top: 9, bottom: 9),
            onPressed: () {},
            shape: RoundedRectangleBorder(
              //边框颜色
              side: BorderSide(
                color: DColor.primary,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(3.0),
            ),
            color: DColor.white,
            child: Text(
              hasSend ? '已发货' : '待发货',
              style: TextStyle(color: DColor.primary, fontSize: 12),
            ),
          )
        ],
      );
    } else {
      return Container();
    }
  }

  Widget _buildShippingAddress(ShoppingAddress? address) {
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("收货信息", style: TextStyle(fontSize: 14)),
        SizedBox(height: 14),
        UserAddressView(address: address),
      ],
    ));
  }

  Widget _buildStoreInfo(BuildContext context) {
    if (item.value.storeVo == null) {
      return Container();
    }
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("门店信息", style: TextStyle(fontSize: 14)),
        SizedBox(height: 14),
        StoreInfoView(context, item.value.storeVo!.shopInfo!)
      ],
    ));
  }

  /**
   * 发放信息，领取时间及发放店员信息
   */
  Widget _buildDistributionInfo(BuildContext context) {
    return Obx(() {
      final record = item.value;

      // 数据加载中时
      if (item == null) {
        return Center();
      }

      // 门店信息为空时，返回空容器
      if (record.storeVo == null) {
        return SizedBox.shrink(); // 更标准的空占位写法
      }

      DateTime distributionTime = record.storeVo?.distributionTime != null
          ? DateTime.parse(record.storeVo?.distributionTime ?? '')
          : DateTime.now();

      // 领取时间及发放工作人员
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 10,
          ),
          Container(
            height: 1,
            color: DColor.ff707070,
          ),
          SizedBox(
            height: 12,
          ),
          Text(
              "领取时间：${DateFormat('yyyy.MM.dd HH:mm:ss').format(distributionTime)}",
              style: TextStyle(fontSize: 12, color: DColor.ff232323)),
          SizedBox(height: 14),
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage:
                    NetworkImage(record.storeVo?.distributionFaceUrl ?? ''),
                onBackgroundImageError: (_, __) {
                  // 如果图片加载失败，使用默认图片
                },
                backgroundColor: Colors.grey[200],
              ),
              SizedBox(width: 10),
              // 用户信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      record.storeVo?.distributionNick ?? "用户名",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 5),
                    Text(
                      "粉丝 ${record.storeVo?.fansTotal ?? 0}",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),

              // TextButton(
              //   onPressed: () async {
              //     try {
              //       if (record.isFriend == 0 || record.isFriend == 3) {
              //         await FollowApi(MyHttp.dio)
              //             .queryFollow(record.distributionUid as int);
              //         var eventFollow =
              //             FollowCountUpdated(++Constants.followCount);
              //         BlocProvider.of<FollowBloc>(context).add(eventFollow);
              //         setState(() {
              //           if (record.isFriend == 3) {
              //             record.isFriend = 1;
              //           } else {
              //             record.isFriend = 2;
              //           }
              //         });
              //       } else {
              //         ModalPopUtils.showModalPop(context, (value) async {
              //           try {
              //             await FollowApi(MyHttp.dio)
              //                 .queryUnfollow(record.distributionUid as int);
              //             var eventFollow =
              //                 FollowCountUpdated(--Constants.followCount);
              //             BlocProvider.of<FollowBloc>(context).add(eventFollow);
              //             setState(() {
              //               if (record.isFriend == 1) {
              //                 record.isFriend = 3;
              //               } else {
              //                 record.isFriend = 0;
              //               }
              //             });
              //           } on DioException catch (e) {
              //             showToast(e.message.toString(), context: context);
              //           }
              //         }, modals: [
              //           PopModal(
              //               tag: "unfollow",
              //               content: "取消关注",
              //               color: DColor.ff242424),
              //         ]);
              //       }
              //     } on DioException catch (e) {
              //       try {
              //         showToast(e.message.toString(), context: context);
              //       } catch (e) {}
              //     }
              //   },
              //   style: ButtonStyle(
              //     textStyle: MaterialStateProperty.all(TextStyle(fontSize: 13)),
              //     minimumSize: MaterialStateProperty.all(Size(55, 25)),
              //     padding: MaterialStateProperty.all(
              //         EdgeInsets.symmetric(vertical: 3, horizontal: 8)),
              //     backgroundColor: MaterialStateProperty.resolveWith((states) {
              //       //设置按下时的背景颜色
              //       // if (states.contains(MaterialState.pressed)) {
              //       //   return Colors.green[200];
              //       // }
              //       return record.isFriend == 1 || record.isFriend == 2
              //           ? Colors.transparent
              //           : DColor.fffb691d;
              //     }),
              //     foregroundColor: MaterialStateProperty.all(
              //         record.isFriend == 1 || record.isFriend == 2
              //             ? DColor.hitText
              //             : Colors.white),
              //     side: MaterialStateProperty.all(BorderSide(
              //         color: record.isFriend == 1 || record.isFriend == 2
              //             ? DColor.hitText
              //             : DColor.fffb691d,
              //         width: 1)),
              //     //外边框装饰 会覆盖 side 配置的样式
              //     shape: MaterialStateProperty.all(StadiumBorder()),
              //   ),
              //   child: Text(record.isFriend == 2
              //       ? "已关注"
              //       : record.isFriend == 1
              //           ? "互相关注"
              //           : "关注"),
              // ),
            ],
          )
        ],
      );
    });
  }
}
