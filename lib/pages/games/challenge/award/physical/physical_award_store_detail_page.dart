import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/award/physical/physical_award_store_detail_logic.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';

import '../../../../../model/activities_challenge.dart';
import '../../../../../model/activities_challenge_assistant_distrobution_records_detail.dart';
import '../../../../../util/loading.dart';
import '../../../../../util/toast.dart';
import '../../../../../widgets/NormalWidget.dart';
import '../../../../../widgets/common_card.dart';
import '../../../../common/normal_dialog.dart';
import '../../challenge_detail_sub_view.dart';

class PhysicalAwardStoreDetailPage extends StatefulWidget {
  final String uid;
  final String acPhysicalReward;

  PhysicalAwardStoreDetailPage(
      {required this.uid, required this.acPhysicalReward}) {}

  @override
  _PhysicalAwardStoreDetailState createState() =>
      _PhysicalAwardStoreDetailState();
}

class _PhysicalAwardStoreDetailState
    extends RefreshState<PhysicalAwardStoreDetailPage> {
  final logic = Get.put(PhysicalAwardStoreDetailLogic());
  final item = Get.find<PhysicalAwardStoreDetailLogic>().item;

  @override
  void initState() {
    logic.uid = widget.uid;
    logic.acPhysicalReward = widget.acPhysicalReward;
    super.initState();
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff232323,
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                Navigator.pop(context, false);
              },
            ),
            title: Text("奖励详情",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: _buildContent(context));
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      children: [
        Expanded(child: _buildList(context)),
      ],
    );
  }

  void _refresh() {
    logic.refreshData();
  }

  Widget _buildList(BuildContext context) {
    return buildRefreshWidget(
        enablePullUp: false,
        onRefresh: () {
          _refresh();
        },
        refreshController: refreshController,
        builder: () => CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Obx(() => _buildDetail(context)),
                ),
              ],
            ));
  }

  Widget _buildDetail(BuildContext context) {
    return Column(
      children: [
        ChallengeDetailHeader(ChallengeDetail(
          id: item.value.activitiesChallenge!.id,
          title: item.value.activitiesChallenge!.title,
          titleEn: item.value.activitiesChallenge!.titleEn,
          logo: item.value.activitiesChallenge!.logo,
          headBg: item.value.activitiesChallenge!.headBg,
        )),
        Container(
          padding: EdgeInsets.only(left: 10, right: 10, top: 4),
          child: Column(
            children: [
              SizedBox(height: 8),
              Text(
                item.value.activitiesChallenge!.title ?? "",
                style: TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
              ),
              // SizedBox(height: 4),
              _buildUserInfo(context),
              _buildPermissionSection(),
              // SizedBox(height: 4),
              _buildPhysicalRewards(context), // 完赛奖励
              // SizedBox(height: 4),
              _buildLuckyDrawRewards(context), // 抽奖奖品
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Image.asset(
              DImages.formatPathPng(
                'icon_contact',
              ),
              height: 14,
              width: 14,
              color: DColor.ff232323,
            ),
            SizedBox(width: 5),
            Text("用户信息", style: TextStyle(fontSize: 14)),
          ],
        ),
        SizedBox(height: 14),
        Row(children: [
          Text("领奖人",
              style: TextStyle(fontSize: 14, color: DColor.color_ff999999)),
          SizedBox(width: 40),
          Text("${item.value.userInfo?.nick ?? ""}",
              style: TextStyle(fontSize: 14, color: DColor.color_ff333333)),
        ]),
        SizedBox(height: 10),
        Row(children: [
          Text("手机号",
              style: TextStyle(fontSize: 14, color: DColor.color_ff999999)),
          SizedBox(width: 40),
          Text(maskPhoneNumber("${item.value.userInfo?.mobile ?? ""}"),
              style: TextStyle(fontSize: 14, color: DColor.color_ff333333)),
        ])
      ],
    ));
  }

  String maskPhoneNumber(String phoneNumber) {
    if (phoneNumber.length == 11) {
      return phoneNumber.replaceRange(3, 7, "****");
    }
    return phoneNumber; // 如果号码长度不符合预期，直接返回原号码
  }

  Widget _buildPhysicalRewards(BuildContext context) {
    // 判断完赛奖励列表是否为空
    if (item.value.physicalRewardDetailVoList == null ||
        item.value.physicalRewardDetailVoList!.isEmpty) {
      return SizedBox.shrink(); // 返回一个空组件
    }

    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.asset(
                DImages.formatPathPng(
                  'icon_award',
                ),
                height: 16,
                width: 16,
                color: DColor.ff232323,
              ),
              SizedBox(width: 8),
              Text(
                "完赛奖励",
                style: TextStyle(fontSize: 14, color: DColor.ff232323),
              ),
            ],
          ),
          SizedBox(height: 14),
          ...item.value.physicalRewardDetailVoList!
              .map((reward) => _buildRewardItem(reward))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildLuckyDrawRewards(BuildContext context) {
    // 判断抽奖奖品列表是否为空
    if (item.value.luckyDrawaPhysicalRewardDetailVoList == null ||
        item.value.luckyDrawaPhysicalRewardDetailVoList!.isEmpty) {
      return SizedBox.shrink(); // 返回一个空组件
    }

    return CommonCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.asset(
                DImages.formatPathPng(
                  'icon_lc_draw',
                ),
                height: 16,
                width: 16,
                color: DColor.ff232323,
              ),
              SizedBox(width: 5),
              Text("抽奖奖品", style: TextStyle(fontSize: 14)),
            ],
          ),
          SizedBox(height: 14),
          ...item.value.luckyDrawaPhysicalRewardDetailVoList!
              .map((reward) => _buildRewardItem(reward))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildRewardItem(
      ActivitiesChallengePhysicalRewardDistributionDetailVo reward) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10), // 上下间距
      padding: EdgeInsets.all(10), // 内边距
      decoration: BoxDecoration(
        color: Colors.white,
        // borderRadius: BorderRadius.circular(8), // 圆角
        // boxShadow: [
        //   BoxShadow(
        //     color: Colors.grey.withOpacity(0.1),
        //     blurRadius: 5,
        //     spreadRadius: 2,
        //   ),
        // ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Center(
            child: reward.image != null && reward.image!.isNotEmpty
                ? Image.network(
                    reward.image!,
                    height: 100,
                    fit: BoxFit.contain,
                  )
                : SizedBox(height: 100), // 如果没有图片，保留空高度
          ),
          SizedBox(height: 5),
          Text(
            reward.name != null ? "${reward.name}×1" : "未知奖品×1",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 14),
          if (item.value.hasPermission! && reward.distributionStatus == 1)
            Text(
              "已领取",
              style: TextStyle(
                fontSize: 14,
                color: reward.distributionStatus == 1
                    ? DColor.primary
                    : DColor.ff232323,
              ),
              textAlign: TextAlign.center,
            ),
          if (!item.value.hasPermission!)
            Text(
              reward.distributionStatus == 0 ? "未领取" : "已领取",
              style: TextStyle(
                fontSize: 14,
                color: reward.distributionStatus == 1
                    ? DColor.primary
                    : DColor.ff232323,
              ),
              textAlign: TextAlign.center,
            ),
          SizedBox(height: 5),
          if (reward.distributionTime != null)
            Text(
              "领奖时间：${reward.distributionTime}",
              style: TextStyle(fontSize: 12, color: DColor.ff808080),
              textAlign: TextAlign.center,
            ),
          // 确认发放按钮
          if (reward.distributionStatus == 0) // 只有未领取时显示按钮
            if (item.value.hasPermission!)
              _buildConfirmButton(reward)
            else
              _buildUnConfirmButton(reward)
        ],
      ),
    );
  }

  Widget _buildPermissionSection() {
    if (item.value.hasPermission!) {
      return SizedBox.shrink(); // 不显示任何内容
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonCard(
          color: DColor.fff10000,
          child: Center(
            // 包裹文字的 Center 部件
            child: Text(
              "用户选择的以下门店，请提醒用户前往指定门店领奖",
              style: TextStyle(
                fontSize: 13,
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontFamily: "PF",
              ),
            ),
          ),
        ),
        // 领取门店信息
        CommonCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "领奖门店",
                style: TextStyle(
                    fontSize: 14,
                    color: DColor.ff232323,
                    fontWeight: FontWeight.w400),
              ),
              SizedBox(height: 15),
              Text(
                item.value.shopInfo!.name ?? "店铺名称", // 店铺名称
                style: TextStyle(
                    fontSize: 12,
                    color: DColor.ff232323,
                    fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 5),
              Text(
                "营业时间：${item.value.shopInfo!.businessHours ?? "营业时间"}",
                style: TextStyle(fontSize: 10, color: Colors.grey),
              ),
              SizedBox(height: 5),
              Text(
                "${item.value.shopInfo?.cityname ?? ''}${item.value.shopInfo?.areaname ?? ''}${item.value.shopInfo?.addr ?? ''}"
                    .trim(),
                style: TextStyle(fontSize: 10, color: Colors.grey),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildConfirmButton(
      ActivitiesChallengePhysicalRewardDistributionDetailVo
          distributionDetail) {
    return InkWell(
      onTap: () {
        showNormalDialog(
          () {
            _onConfirmDistribute(distributionDetail);
          },
          title: "温馨提示",
          content: "确定要继续吗？",
          confirmText: "确认",
        );
      },
      child: Container(
        height: 35,
        // 固定高度
        width: 110,
        // 可选：固定宽度，视需求调整
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: DColor.primary, // 按钮背景颜色
          borderRadius: BorderRadius.circular(5), // 圆角
        ),
        child: Text(
          "确认发放",
          style: TextStyle(fontSize: 16, color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildUnConfirmButton(
      ActivitiesChallengePhysicalRewardDistributionDetailVo
          distributionDetail) {
    return InkWell(
      onTap: () {
        // _onConfirmDistribute(distributionDetail); // 按钮点击逻辑
      },
      child: Container(
        height: 35,
        // 固定高度
        width: 110,
        // 可选：固定宽度，视需求调整
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.transparent, // 背景透明
          border: Border.all(color: DColor.primary, width: 1.5), // 橙色边框
          borderRadius: BorderRadius.circular(5), // 圆角
        ),
        child: Text(
          "未领取",
          style: TextStyle(fontSize: 16, color: DColor.primary), // 橙色文字
        ),
      ),
    );
  }

  void _onConfirmDistribute(
      ActivitiesChallengePhysicalRewardDistributionDetailVo
          distributionDetail) async {
    showLoadingDialogWithFuture(
            context,
            logic.distributeChallengeReward(
                distributionDetail.distributionRecordId!),
            barrierDismissible: false,
            delayTime: const Duration(seconds: 2))
        .then((result) async {
      if (result!.success == true) {
        showToast('发放成功！', context);
        _refresh();
      } else {
        showToast(result.message ?? '发放失败！', context);
      }
    }).catchError((error) {
      if (error is DioException) {
        showToast(error.message.toString(), context);
      }
    });
  }
}
