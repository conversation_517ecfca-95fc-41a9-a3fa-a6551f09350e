import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/activities_challenge_store_reward.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/widgets/load_state.dart';

import '../../../../../model/activities_challenge_assistant_distrobution_records_detail.dart';

class PhysicalAwardStoreDetailLogic extends GetxController {
  var uid = "";
  var acPhysicalReward = "";
  var item = ActivitiesChallengeAssistantDistrobutionRecordsDetail().obs;
  var loadStatus = FitLoadStatus.loading.obs;

  Future refreshData() async {
    ActivitiesChallengeAssistantDistrobutionRecordsDetail? responseData;

    loadStatus.value = FitLoadStatus.loading;
    try {
      responseData = (await _loadData()).result;

      if (responseData != null) {
        item.value = responseData;
      }

      /// 刷新完成
      loadStatus.value = FitLoadStatus.loadSuccess;
    } catch (error) {
      print("出错 $error");
      loadStatus.value = FitLoadStatus.loadError;
    }
  }

  Future<ApiResponse<ActivitiesChallengeAssistantDistrobutionRecordsDetail>> _loadData() {
    return ActiviesApi(MyHttp.dio)
        .queryByQrCodeStr4CurrentShop(scannedUid: uid, acPhysicalRewardId: acPhysicalReward);
  }

  Future<ApiResponse<String>?> distributeChallengeReward(String distributionRecordId) {
    return ActiviesApi(MyHttp.dio)
        .distributeChallengeReward(toUid: uid, distributionRecordId: distributionRecordId);
  }
}
