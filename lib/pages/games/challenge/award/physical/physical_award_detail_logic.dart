import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/io/user.dart';
import 'package:new_edge/io/util.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/activities_challenge_reward_distribution_records.dart';
import 'package:new_edge/model/api_response/api_edge_response_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/express_detail.dart';
import 'package:new_edge/model/prize_claim_form.dart';
import 'package:new_edge/model/reward_distribution_record.dart';
import 'package:new_edge/model/shopping_address.dart';
import 'package:new_edge/model/verify_name_result.dart';
import 'package:new_edge/pages/games/challenge/award/third/third_award_account_logic.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/widgets/load_state.dart';

class PhysicalAwardDetailLogic extends GetxController {
  var item = PrizeClaimForm().obs;
  var expressItem = ExpressDetail().obs;
  var loadStatus = FitLoadStatus.loading.obs;

  Future refreshData(String id) async {
    PrizeClaimForm? responseData;

    loadStatus.value = FitLoadStatus.loading;
    try {
      responseData = (await _loadData(id)).result;

      if (responseData != null) {
        item.value = responseData;

        //请求快递信息
        if (responseData.mailVo != null) {
          try {
            ExpressDetail responseExpress = await _loadExpressData(
                responseData.mailVo?.postCompanyCode ?? '',
                responseData.mailVo?.postNum ?? '',
                responseData.mailVo?.userMobile);
            expressItem.value = responseExpress;
          } catch (error) {
            print("出错 $error");
          }
        }
      }

      /// 刷新完成
      loadStatus.value = FitLoadStatus.loadSuccess;
    } catch (error) {
      print("出错 $error");
      loadStatus.value = FitLoadStatus.loadError;
    }
  }

  Future<ExpressDetail> _loadExpressData(
      String deliveryId, String num, String? phone) {
    return UtilApi(MyHttp.dio)
        .getTrackList(com: deliveryId, num: num, mobile: phone);
  }

  Future<ApiResponse<PrizeClaimForm>> _loadData(String id) {
    return ActiviesApi(MyHttp.dio)
        .getRewardDistributionRecords(prizeClaimFormId: id);
  }

  Future<ApiResponse<bool>> checkPayDeliveryStatus(String id) {
    return ActiviesApi(MyHttp.dio).checkPayDeliveryStatus(challengeId: id);
  }

  Future<ApiResponse<PrizeClaimForm>?>
      addPhysicalRewardAddressRecord(ChallengeDetail challenge,
          PrizeInfo physicalReward, ShoppingAddress address) {
    return ActiviesApi(MyHttp.dio)
        .addRewardDistributionRecords(
            data: RewardDistributionRecord(
                prizeClaimFormId: physicalReward.prizeClaimFormId!,
                address: address.address,
                city: address.city,
                mobile: address.mobile,
                shoppingName: address.shoppingName,
                postCode: address.postCode,
                province: address.province,
                town: address.town,
                userShoppingAddressId: address.id,
                distributionMethod: CollectType.physicalMail.value))
        .then((value) {
      if (value.success == true) {
        item.value = value.result!;
      }
      return value;
    });
  }

  Future<ApiResponse<PrizeClaimForm>?>
      addPhysicalRewardShopRecord(ChallengeDetail challenge,
          PrizeInfo physicalReward, String shopId) {
    return ActiviesApi(MyHttp.dio)
        .addRewardDistributionRecords(
            data: RewardDistributionRecord(
                prizeClaimFormId: physicalReward.prizeClaimFormId!,
                shopId: shopId,
                distributionMethod: CollectType.physicalStore.value))
        .then((value) {
      if (value.success == true) {
        item.value = value.result!;
      }
      return value;
    });
  }

  Future<ApiResponse<PrizeClaimForm>?>
      addThirdPartyRewardRecord(ActivitiesChallenge challenge,
          PrizeInfo thirdPartyReward, ThirdAccountInfo accountInfo) {
    return ActiviesApi(MyHttp.dio)
        .addRewardDistributionRecords(
            data: RewardDistributionRecord(
                prizeClaimFormId: thirdPartyReward.prizeClaimFormId!,
                userMobile: accountInfo.mobile,
                nickname: accountInfo.name,
                thirdPartyAccount: accountInfo.thirdNo))
        .then((value) {
      if (value.success == true) {
        item.value = value.result!;
      }
      return value;
    });
  }

  Future<bool> checkVerifyName() async {
    try {
      return (await _hasRealNameInfo4Mobile()).info?.hasRealNameInfo ?? false;
    } catch (error) {
      print("出错 $error");
    }
    return false;
  }

  Future<ApiEdgeResponse<VerifyNameResult>> _hasRealNameInfo4Mobile() {
    return UserApi(MyHttp.dio).hasRealNameInfo();
  }

  ShoppingAddress? getShoppingAddress() {
    if (item.value.id == null || item.value.mailVo == null) {
      return null;
    }
    return ShoppingAddress(
        address: item.value.mailVo?.address,
        city: item.value.mailVo?.city,
        mobile: item.value.mailVo?.mobile,
        areaCode: "+86",
        postCode: item.value.mailVo?.postCode,
        province: item.value.mailVo?.province,
        shoppingName: item.value.mailVo?.shoppingName,
        town: item.value.mailVo?.town);
  }
}
