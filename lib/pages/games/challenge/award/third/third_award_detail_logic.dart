import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/io/user.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/activities_challenge_reward_distribution_records.dart';
import 'package:new_edge/model/api_response/api_edge_response_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/express_detail.dart';
import 'package:new_edge/model/prize_claim_form.dart';
import 'package:new_edge/model/reward_distribution_record.dart';
import 'package:new_edge/model/verify_name_result.dart';
import 'package:new_edge/pages/games/challenge/award/third/third_award_account_logic.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/widgets/load_state.dart';

class ThirdAwardDetailLogic extends GetxController {
  var item = PrizeClaimForm().obs;
  var expressItem = ExpressDetail().obs;
  var loadStatus = FitLoadStatus.loading.obs;

  Future refreshData(String id) async {
    PrizeClaimForm? responseData;

    loadStatus.value = FitLoadStatus.loading;
    try {
      responseData = (await _loadData(id)).result;

      if (responseData != null) {
        item.value = responseData;
      }

      /// 刷新完成
      loadStatus.value = FitLoadStatus.loadSuccess;
    } catch (error) {
      print("出错 $error");
      loadStatus.value = FitLoadStatus.loadError;
    }
  }

  Future<ApiResponse<PrizeClaimForm>> _loadData(String id) {
    return ActiviesApi(MyHttp.dio)
        .getRewardDistributionRecords(prizeClaimFormId: id);
  }

  Future<ApiResponse<PrizeClaimForm>?> addThirdPartyRewardRecord(
      ChallengeDetail challenge,
      PrizeInfo thirdPartyReward,
      ThirdAccountInfo accountInfo) {
    return ActiviesApi(MyHttp.dio)
        .addRewardDistributionRecords(
            data: RewardDistributionRecord(
                prizeClaimFormId: thirdPartyReward.prizeClaimFormId!,
                phone: accountInfo.mobile,
                name: accountInfo.name,
                thirdPartyAccount: accountInfo.thirdNo))
        .then((value) {
      if (value.success == true) {
        item.value = value.result!;
      }
      return value;
    });
  }

  Future<bool> checkVerifyName() async {
    try {
      return (await _hasRealNameInfo4Mobile()).info?.hasRealNameInfo ?? false;
    } catch (error) {
      print("出错 $error");
    }
    return false;
  }

  Future<ApiEdgeResponse<VerifyNameResult>> _hasRealNameInfo4Mobile() {
    return UserApi(MyHttp.dio).hasRealNameInfo();
  }

  ThirdAccountInfo? getThirdAccountInfo() {
    if (item.value.id == null || item.value.infoVirtualVo == null) {
      return null;
    }
    return ThirdAccountInfo(
        name: item.value.infoVirtualVo?.name,
        mobile: item.value.infoVirtualVo?.phone,
        thirdNo: item.value.infoVirtualVo?.thirdPartyAccount);
  }
}
