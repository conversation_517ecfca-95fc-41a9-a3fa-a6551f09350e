import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart'
    as GetTransition;
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/award/third/third_award_account_logic.dart';
import 'package:new_edge/pages/games/challenge/award/third/third_award_account_page.dart';
import 'package:new_edge/pages/games/challenge/award/third/third_award_account_widget.dart';
import 'package:new_edge/pages/games/challenge/challenge_detail_sub_view.dart';
import 'package:new_edge/pages/verifyidcard/verifyidcard_edit_page.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/NormalWidget.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:intl/intl.dart';

import 'third_award_detail_logic.dart';

class ThirdAwardDetailPage extends StatefulWidget {
  final ChallengeDetail challenge;

  final PrizeInfo thirdPartyReward;

  ThirdAwardDetailPage(
      {required this.challenge, required this.thirdPartyReward}) {}

  @override
  _ThirdAwardDetailState createState() => _ThirdAwardDetailState();
}

class _ThirdAwardDetailState extends RefreshState<ThirdAwardDetailPage> {
  final logic = Get.put(ThirdAwardDetailLogic());

  final thirdAwardLogic = Get.put(ThirdAwardAccountLogic());

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff232323,
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                Navigator.pop(context, false);
              },
            ),
            title: Text("奖励详情",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: Obx(() => _buildContent(context)));
  }

  Widget _buildContent(BuildContext context) {
    bool isProvided = widget.thirdPartyReward.isProvided;

    String? prizeClaimDeadline = widget.thirdPartyReward.prizeClaimDeadline;
    DateTime providedEndTime = prizeClaimDeadline != null
        ? DateTime.parse(prizeClaimDeadline ?? '')
        : DateTime.now();
    DateFormat dateFormat = DateFormat('yyyy.MM.dd');
    bool isProvidedExpired = DateTime.now().millisecondsSinceEpoch >
        providedEndTime.millisecondsSinceEpoch;

    return Column(
      children: [
        Expanded(child: _buildList(context)),
        Visibility(
            visible: !isProvided && !isProvidedExpired,
            child: BoxShadowContent(
                child: Column(
              children: [
                Text("请在${dateFormat.format(providedEndTime)}前领取",
                    style: TextStyle(color: Color(0xFFFF0000), fontSize: 12)),
                SizedBox(height: 10),
                SizedBox(width: double.infinity, child: _buildSubmitButton())
              ],
            ))),
      ],
    );
  }

  Widget _buildSubmitButton() {
    bool isReady = thirdAwardLogic.thirdInfo.value.isValid();
    String submitText = "确认领取";
    return SubmitButton(
        text: submitText,
        enable: isReady,
        onPressed: () {
          showLoadingDialogWithFuture(
            context,
            logic.checkVerifyName(),
          ).then((result) async {
            if (result) {
              showNormalDialog(
                () {
                  _addAwardRecord();
                },
                title: "温馨提示",
                content: "确认领取后，无法修改领奖人信息，\n 请确认无误后再点击领取。",
                confirmText: "确认领取",
              );
            } else {
              showNormalDialog(
                () async {
                  await Get.to(VerifyIdCardEditPage(),
                      transition: GetTransition.Transition.rightToLeft);
                },
                title: "温馨提示",
                content: "请先进行实名认证后，再领奖",
                confirmText: "去认证",
              );
            }
          });
        });
  }

  void _addAwardRecord() {
    showLoadingDialogWithFuture(
            context,
            logic.addThirdPartyRewardRecord(widget.challenge,
                widget.thirdPartyReward, thirdAwardLogic.thirdInfo.value),
            barrierDismissible: false)
        .then((result) async {
      if (result?.success ?? false) {
        // Get.back();
        var item = result?.result;
        if (item != null) {
          logic.item.value = item;
        }
        showToast('用户已确认', context);
        _refresh();
      } else {
        showToast('领取失败！', context);
      }
    });
  }

  void _refresh() {
    String? rewardDistributionRecordId =
        widget.thirdPartyReward.prizeClaimFormId;
    logic.refreshData(rewardDistributionRecordId ?? "");
  }

  @override
  bool isAutoRefresh() {
    return widget.thirdPartyReward.prizeClaimFormId != null;
  }

  Widget _buildList(BuildContext context) {
    return Obx(() => buildRefreshWidget(
        enablePullUp: false,
        enablePullDown: true,
        onRefresh: () {
          _refresh();
        },
        refreshController: refreshController,
        builder: () => CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: _buildDetail(context),
                ),
              ],
            )));
  }

  Widget _buildDetail(BuildContext context) {
    bool isProvided = widget.thirdPartyReward.isProvided;

    ThirdAccountInfo? thirdAccountInfo;
    if (isProvided) {
      thirdAccountInfo = logic.getThirdAccountInfo();
    } else {
      thirdAccountInfo = thirdAwardLogic.thirdInfo.value;
    }

    return Column(
      children: [
        ChallengeDetailHeader(widget.challenge),
        Container(
            padding: EdgeInsets.only(left: 10, right: 10, top: 4),
            child: Column(
              children: [
                SizedBox(height: 8),
                Text(
                  widget.challenge.title ?? "",
                  style: TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
                ),
                SizedBox(
                  height: 8,
                ),
                _buildAwardInfo(),
                Column(children: [
                  _buildThirdPartyInfo(thirdAccountInfo),
                ])
              ],
            )),
      ],
    );
  }

  Widget _buildAwardInfo() {
    bool isProvided = (widget.thirdPartyReward.isProvided ?? 0) == 1;
    String image = widget.thirdPartyReward.image ?? "";
    String title = widget.thirdPartyReward.name ?? "";
    String summary = widget.thirdPartyReward.description ?? "";

    String? prizeClaimDeadline = widget.thirdPartyReward.prizeClaimDeadline;
    DateTime providedEndTime = prizeClaimDeadline != null
        ? DateTime.parse(prizeClaimDeadline ?? '')
        : DateTime.now();
    bool isProvidedExpired = DateTime.now().millisecondsSinceEpoch >
        providedEndTime.millisecondsSinceEpoch;

    return CommonCard(
        child: Column(
      children: [
        Row(
          children: [
            Image.asset(
              DImages.formatPathPng(
                'icon_award',
              ),
              height: 20,
              width: 20,
              color: DColor.ff232323,
            ),
            SizedBox(width: 8),
            Text(
              "赢取奖励",
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        SizedBox(height: 8),
        SizedBox(
            height: 100,
            child: CachedNetworkImage(
                imageUrl: image,
                placeholder: (context, url) => Image.asset(
                      DImages.formatPathPng('img_placeholder_bg'),
                      fit: BoxFit.fitHeight,
                    ),
                errorWidget: (context, url, error) => Image.asset(
                      DImages.formatPathPng('img_placeholder_bg'),
                      fit: BoxFit.fitHeight,
                    ),
                fit: BoxFit.fitHeight)),
        SizedBox(height: 10),
        Text(title, style: TextStyle(color: DColor.ff232323, fontSize: 13)),
        SizedBox(height: 10),
        Visibility(
            visible: summary.isNotEmpty,
            child: Padding(
                padding: EdgeInsets.only(top: 8),
                child: Text(summary,
                    textAlign: TextAlign.center,
                    style:
                        TextStyle(color: DColor.color_ff999999, fontSize: 9)))),
        SizedBox(height: 10),
        MaterialButton(
          minWidth: 70,
          padding: EdgeInsets.only(left: 8, right: 8, top: 9, bottom: 9),
          onPressed: () {},
          shape: RoundedRectangleBorder(
            //边框颜色
            side: BorderSide(
              color: isProvided ? DColor.primary : Colors.transparent,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(3.0),
          ),
          color: isProvided ? Colors.white : DColor.ff808080,
          child: Text(
            isProvided ? '已领取' : '未领取',
            style: TextStyle(
                color: isProvided ? DColor.primary : Colors.white,
                fontSize: 12),
          ),
        ),
      ],
    ));
  }

  Widget _buildThirdPartyInfo(ThirdAccountInfo? accountInfo) {
    bool isProvided = widget.thirdPartyReward.isProvided;
    bool isShowModify = (accountInfo?.isValid() ?? false) && !isProvided;
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(child: Text("领奖人信息", style: TextStyle(fontSize: 14))),
            GestureDetector(
              onTap: () async {
                await Get.to(
                    ThirdAwardAccountPage(
                      thirdPartyName: widget.thirdPartyReward.thirdPartyName,
                    ),
                    transition: GetTransition.Transition.rightToLeft);
              },
              child: Visibility(
                  visible: isShowModify,
                  child: Row(children: [
                    Image.asset(
                        height: 15, DImages.formatPathPng("icon_modify")),
                    SizedBox(width: 5),
                    Text('修改',
                        style:
                            TextStyle(color: Color(0xFF999999), fontSize: 13)),
                  ])),
            )
          ],
        ),
        SizedBox(height: 14),
        ThirdAwardInfoView(
            info: accountInfo,
            onChooseThirdNo: () async {
              await Get.to(
                  ThirdAwardAccountPage(
                      thirdPartyName: widget.thirdPartyReward.thirdPartyName),
                  transition: GetTransition.Transition.rightToLeft);
            })
      ],
    ));
  }
}
