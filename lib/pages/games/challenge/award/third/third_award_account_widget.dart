import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/pages/games/challenge/award/third/third_award_account_logic.dart';

class ThirdAwardInfoView extends StatelessWidget {
  final ThirdAccountInfo? info;
  final Function? onChooseThirdNo;

  ThirdAwardInfoView({this.info, this.onChooseThirdNo}) {}

  @override
  Widget build(BuildContext context) {
    return _buildAddressInfo();
  }

  Widget _buildAddressInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        (info?.isValid() ?? false)
            ? Column(children: [
                Row(
                  children: [
                    _buildExpressItem("领奖人", "请输入姓名", info?.name ?? ""),
                    SizedBox(width: 40),
                    _buildExpressItem("联系方式", "请输入手机号", info?.mobile ?? ""),
                  ],
                ),
                SizedBox(height: 10),
                _buildExpressItem("领奖账号", "-", info?.thirdNo ?? ""),
              ])
            : _buildAddButton(() async {
                onChooseThirdNo?.call();
              }),
      ],
    );
  }

  Widget _buildAddButton(VoidCallback onConfirm) {
    return Center(
        child: MaterialButton(
            minWidth: 140,
            padding: EdgeInsets.only(top: 9, bottom: 9),
            onPressed: () {
              onConfirm();
            },
            child: Text(
              '添加领取号码',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
            shape: RoundedRectangleBorder(
              //边框颜色
              side: BorderSide(
                color: Colors.transparent,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(3.0),
            ),
            color: DColor.primary,
            disabledColor: DColor.primary.withOpacity(0.5)));
  }

  Widget _buildExpressItem(String name, String hint, String value) {
    return Row(children: [
      Container(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(name, style: TextStyle(color: Color(0xFF999999), fontSize: 14)),
      ])),
      SizedBox(width: 10),
      Text(value.isNotEmpty ? value : hint,
          style: TextStyle(
              fontSize: 14,
              color: value.isNotEmpty ? DColor.ff242424 : DColor.hitText))
    ]);
  }
}
