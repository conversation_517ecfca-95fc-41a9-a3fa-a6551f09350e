import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ChallengeDetailLogic extends GetxController {
  var item = ChallengeDetail().obs;
  var loadStatus = FitLoadStatus.loading.obs;

  bool hasData() {
    return item.value.id != null;
  }

  void refreshData(String id) async {
    ChallengeDetail? responseData;

    loadStatus.value = FitLoadStatus.loading;
    try {
      responseData = (await _loadData(id)).result;

      if (responseData != null) {
        item.value = responseData;
      }

      /// 刷新完成
      loadStatus.value = FitLoadStatus.loadSuccess;
    } catch (error) {
      print("出错 $error");
      loadStatus.value = FitLoadStatus.loadError;
    }
  }

  Future<ApiResponse<ChallengeDetail>> _loadData(String id) {
    return ActiviesApi(MyHttp.dio).getActivitiesChallenge(id: id);
  }

  Future<ApiResponse<String>?> joinChallenge(String challengeId) {
    return ActiviesApi(MyHttp.dio).joinChallenge(challengeId: challengeId);
  }

  bool hasPhysicalReward() {
    return item.value.hasPhysicalReward;
  }

  bool hasVirtualReward() {
    return item.value.hasVirtualReward;
  }
}
