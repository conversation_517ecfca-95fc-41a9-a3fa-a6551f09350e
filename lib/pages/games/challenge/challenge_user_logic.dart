import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/activities_user_challenge.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/join_challenge.dart';
import 'package:new_edge/pages/base/page_controller.dart';
import 'package:new_edge/pages/games/challenge/challenge_user_state.dart';
import 'package:new_edge/request/MyHttp.dart';

class ChallengeUserLogic extends PagingController<JoinChallenge, ChallengeUserState> {
  @override
  ChallengeUserState getState() => ChallengeUserState();

  @override
  Future<ApiResponse<ApiPaging<JoinChallenge>>?> loadData(int pagingIndex) {
    return ActiviesApi(MyHttp.dio).getActivitiesUserChallengeList(pageNum: pagingIndex, pageSize: 100);
  }

}
