import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../config/color.dart';
import '../../../../config/image_path.dart';
import '../../../../model/activities_challenge_store_reward_statistic_detail.dart';
import '../../../../util/toast.dart';

class ItemStoreAwardStatisticDetailView extends StatelessWidget {
  final ActivitiesChallengeStoreRewardStatisticDetail item;
  final bool isSelected;
  final Function()? onTap;
  final Function()? onJoinClicked;

  const ItemStoreAwardStatisticDetailView({
    Key? key,
    required this.item,
    this.onJoinClicked,
    this.onTap,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 判断是否发放
    bool isDistributed = item.distributionStatus == 1;

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: onTap ??
          () {
            // showToast("点击了用户 ${item.nick}", context);
          },
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                padding: EdgeInsets.all(10),
                margin: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(width: 10),
                        // 用户头像
                        CircleAvatar(
                          radius: 20,
                          backgroundImage: NetworkImage(item.faceUrl ?? ''),
                          onBackgroundImageError: (_, __) {
                            // 如果图片加载失败，使用默认图片
                          },
                          backgroundColor: Colors.grey[200],
                        ),
                        SizedBox(width: 10),
                        // 用户信息
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item.nick ?? "用户名",
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: 5),
                              Text(
                                _obfuscateMobile(item.mobile ?? ""),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // 时间和状态
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            // Text(
                            //   item.distributionTime ?? "未发放",
                            //   style: TextStyle(
                            //     fontSize: 12,
                            //     color: isDistributed ? Colors.green : Colors.red,
                            //   ),
                            // ),
                            SizedBox(height: 5),
                            GestureDetector(
                              onTap: () {
                                String? tel = item.mobile;
                                if (tel != null) {
                                  launchUrl(Uri.parse("tel:$tel"));
                                } else {
                                  showToast("暂无电话", context);
                                }
                              },
                              child: Image.asset(
                                DImages.formatPathPng('icon_phone_red'),
                                height: 16,
                                fit: BoxFit.fitHeight,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    if (isDistributed) ...[
                      SizedBox(height: 10),
                      Padding(
                        padding: EdgeInsets.fromLTRB(55, 0, 50, 0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              children: [
                                Text(
                                  "发奖人",
                                  style: TextStyle(
                                      fontSize: 12, color: DColor.ff242424),
                                ),
                                Text(
                                  "${item.distributionFullName}",
                                  style: TextStyle(
                                      fontSize: 12, color: DColor.ff808080),
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                Text(
                                  "时间",
                                  style: TextStyle(
                                      fontSize: 12, color: DColor.ff242424),
                                ),
                                Text(
                                  "${item.distributionTime}",
                                  style: TextStyle(
                                      fontSize: 12, color: DColor.ff808080),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (isDistributed)
                Positioned(
                  top: 0,
                  bottom: 0, // 同时设置 top 和 bottom
                  right: 0,  // 保持在右侧
                  child: Image.asset(
                    DImages.formatPathPng('img_distributed'),
                    width: 120,
                    height: 120,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  String _obfuscateMobile(String mobile) {
    if (mobile.length == 11) {
      // 将中间4位替换为****
      return mobile.replaceRange(3, 7, "****");
    }
    return mobile; // 如果号码格式不对，直接返回原号码
  }
}
