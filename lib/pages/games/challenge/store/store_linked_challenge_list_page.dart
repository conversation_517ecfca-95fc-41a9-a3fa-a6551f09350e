import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/app.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/store/challenge_item_view.dart';
import 'package:new_edge/pages/games/challenge/store/store_linked_challenge_list_logic.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';

import '../../../../config/color.dart';
import '../../../../config/image_path.dart';

class StoreLinkedChallengeListPage extends StatefulWidget {
  @override
  _StoreLinkedChallengeListState createState() =>
      _StoreLinkedChallengeListState();
}

class _StoreLinkedChallengeListState
    extends RefreshState<StoreLinkedChallengeListPage> with RouteAware {
  final listLogic = Get.put(StoreLinkedChallengeListLogic());
  final listState = Get.find<StoreLinkedChallengeListLogic>().pagingState;

  @override
  Rx<FitLoadStatus> getLoadState() {
    return listLogic.loadStatus;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (mounted) {
      // userLogic.refreshData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          centerTitle: true,
          leading: IconButton(
            icon: Image.asset(
              DImages.formatPathPng(
                'jiantou_left_white',
              ),
              height: 17,
              width: 17,
              color: DColor.ff232323,
            ),
            onPressed: () {
              FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
              Navigator.pop(context, false);
            },
          ),
          title: Text("挑战列表",
              style: TextStyle(color: DColor.ff242424, fontSize: 18))),
      body: Obx(() => buildRefreshWidget(
          builder: () => listState.value.hasData()
              ? _buildList(context)
              : buildLoadStateWidgetFullEmpty(listLogic.loadStatus.value, () {
                  refreshController.requestRefresh();
                }, "bg_challenge_empty"),
          onRefresh: () {
            listLogic.refreshData();
          },
          onLoad: listLogic.loadMoreData,
          enablePullUp: listState.value.hasMore,
          refreshController: refreshController)),
    );
  }

  Widget _buildList(BuildContext context) {
    return CustomScrollView(
      slivers: [
        // SliverToBoxAdapter(
        //   child: Column(
        //     children: [
        //
        //     ],
        //   ),
        // ),
        SliverList(
          delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
            ChallengeDetail item = listState.value.data[index];
            return ChallengeItemB(
              item: item,
            );
          }, childCount: listState.value.getCount()),
        ),
      ],
    );
  }
}
