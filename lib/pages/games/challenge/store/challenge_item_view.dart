import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart'
as GetTransition;
import 'package:intl/intl.dart' show DateFormat;
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/pages/games/challenge/challenge_target_view.dart';
import 'package:new_edge/pages/games/challenge/store/store_award_statistic_page.dart';

class ChallengeItemB extends StatelessWidget {
  final ChallengeDetail item;
  final bool isSelected;
  final Function()? onTap;
  final Function()? onJoinClicked;

  const ChallengeItemB(
      {Key? key,
      required this.item,
      this.onJoinClicked,
      this.onTap,
      this.isSelected = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    DateTime raceEndTime = DateTime.parse(item.endTime ?? '');
    bool isRaceFinish = DateTime.now().millisecondsSinceEpoch >
        raceEndTime.millisecondsSinceEpoch;

    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap ??
            () {
              // Get.toNamed(RouteGet.challengeDetail, arguments: {
              //   'id': item.id,
              // });
              // showToast("msg", context);

              // Get.toNamed('/storeAwardStatisticPage', arguments: {
              //   'id': item.id, // 如果需要传递参数，例如活动ID
              //   'name': item.name, // 也可以传递其他参数
              // });
              Get.to(
                  StoreAwardStatisticPage(
                      activitiesChallenge: item),
                  transition: GetTransition.Transition.rightToLeft);
            },
        child: Column(
          children: [
            Container(height: 8),
            Material(
                color: DColor.white,
                shadowColor: Color(0xFF000000).withOpacity(0.16),
                elevation: 5,
                child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(
                        color: isSelected ? DColor.primary : Colors.transparent,
                        width: 2.0,
                      ),
                    ),
                    padding: EdgeInsets.only(left: 24, top: 10, bottom: 10),
                    child: Column(
                      children: [
                        _buildHeaderItem(item.type == 1, isRaceFinish),
                        SizedBox(
                          height: 8,
                        ),
                        Row(
                          children: [
                            SizedBox(
                                width: 100,
                                height: 100,
                                child: ClipRRect(
                                    borderRadius: BorderRadius.circular(5.0),
                                    child: CachedNetworkImage(
                                        imageUrl: item.logo ?? "",
                                        placeholder: (context, url) =>
                                            Image.asset(
                                              DImages.formatPathPng(
                                                  'img_placeholder_bg'),
                                              fit: BoxFit.cover,
                                            ),
                                        errorWidget: (context, url, error) =>
                                            Image.asset(
                                              DImages.formatPathPng(
                                                  'img_placeholder_bg'),
                                              // Use placeholder as error widget
                                              width: double.infinity,
                                              fit: BoxFit.cover,
                                            ),
                                        fit: BoxFit.cover))),
                            SizedBox(width: 10),
                            Expanded(
                              child: Container(
                                  alignment: Alignment.topLeft,
                                  child: _buildDetailItem(item)),
                            )
                          ],
                        ),
                      ],
                    ))),
          ],
        ));
  }

  Widget _buildHeaderItem(bool isSpecial, bool isFinish) {
    return Row(
      children: [
        Text(
          isSpecial ? '专属挑战' : '挑战',
          style: TextStyle(
              color: isSpecial ? DColor.primary : Color(0xFF666666),
              fontSize: 13),
        ),
        Expanded(
            child: isSpecial ? _buildSpecialHeaderContainer() : Container()),
        Padding(
            padding: EdgeInsets.only(
              right: isFinish ? 0 : 14,
            ),
            child: Image.asset(
              DImages.formatPathPng(isFinish ? 'icon_finish' : 'icon_hot'),
              height: 20,
            )),
        Visibility(
            visible: isSelected,
            child: Padding(
                padding: EdgeInsets.only(right: 15),
                child: Image.asset(
                  DImages.formatPathPng('selected'),
                  height: 15,
                  width: 15,
                  fit: BoxFit.cover,
                )))
      ],
    );
  }

  Widget _buildSpecialHeaderContainer() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildSpecialItem("icon_location", "西安"),
        SizedBox(width: 5),
        _buildSpecialItem("icon_bike", "specialized"),
        SizedBox(width: 5),
        _buildSpecialItem("icon_people", "指定人群"),
      ],
    );
  }

  Widget _buildSpecialItem(String assertName, String title) {
    return Row(
      children: [
        Image.asset(
          DImages.formatPathPng(assertName),
          height: 10,
        ),
        SizedBox(width: 5),
        Text(
          title,
          style: TextStyle(color: Color(0xFF999999), fontSize: 9),
        ),
      ],
    );
  }

  Widget _buildDetailItem(ChallengeDetail item) {
    String startTime =
        DateFormat('yyyy.MM.dd').format(DateTime.parse(item.startTime ?? ''));
    String endTime =
        DateFormat('yyyy.MM.dd').format(DateTime.parse(item.endTime ?? ''));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
            padding: EdgeInsets.only(right: 10),
            child: Text(
              item.title ?? "",
              style: TextStyle(fontSize: 14, fontFamily: DFamily.pingFangSc),
            )),
        SizedBox(height: 7),
        Text(
          '$startTime-$endTime',
          style: TextStyle(
              fontSize: 16,
              fontFamily: DFamily.dinBold,
              fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 10),
        Padding(
            padding: EdgeInsets.only(right: 10),
            child: ChallengeTargetList(item.mileageTarget, item.altitudeTarget,
                item.durationTarget, item.timesTarget))
      ],
    );
  }
}
