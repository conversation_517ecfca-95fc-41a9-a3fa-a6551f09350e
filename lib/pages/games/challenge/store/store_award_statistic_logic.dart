import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/pages/games/challenge/store/store_award_statistic_state.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:geolocator/geolocator.dart';

import '../../../../model/activities_challenge_store_reward_statistic.dart';
import '../../../base/list_controller.dart';

class StoreAwardStatisticLogic
    extends ListController<ActivitiesChallengeStoreRewardStatistic> {

  final Rx<List<ActivitiesChallengeStoreRewardStatistic>?> statistics =
  Rx<List<ActivitiesChallengeStoreRewardStatistic>?>(null);

  late ChallengeDetail activitiesChallenge;

  @override
  StoreAwardStatisticState getState() => StoreAwardStatisticState();

  @override
  Future<ApiResponse<List<ActivitiesChallengeStoreRewardStatistic>>> loadData() {
    // return ActiviesApi(MyHttp.dio)
    //     .queryStoreRewardStatistic(
    //     challengeId: "1849807221731393537");

    return ActiviesApi(MyHttp.dio)
        .queryStoreRewardStatistic(challengeId: activitiesChallenge.id)
        .then((value) {
      if (value.success == true) {
        statistics.value = value.result;
      }
      return value;
    });
  }
}
