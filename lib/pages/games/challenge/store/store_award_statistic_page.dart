import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart'
    as GetTransition;
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/prize_claim_form.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/store/store_award_statistic_detail_page.dart';
import 'package:new_edge/pages/games/challenge/store/store_award_statistic_logic.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';

import '../../../../model/activities_challenge_store_reward_statistic.dart';
import '../../../../util/login_utils.dart';
import '../../../../util/qrcode_utils.dart';
import '../../../qrscan/live_decode.dart';
import '../challenge_detail_sub_view.dart';

class StoreAwardStatisticPage extends StatefulWidget {
  // final String groupId;
  // final String source;

  final ChallengeDetail activitiesChallenge;

  StoreAwardStatisticPage({required this.activitiesChallenge}) {}

  @override
  _StoreAwardStatisticPageState createState() =>
      _StoreAwardStatisticPageState();
}

class _StoreAwardStatisticPageState
    extends RefreshState<StoreAwardStatisticPage> {
  final StoreAwardStatisticLogic logic = Get.put(StoreAwardStatisticLogic());
  final list = Get.find<StoreAwardStatisticLogic>().list;

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  void initState() {
    logic.activitiesChallenge = widget.activitiesChallenge;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return WillPopScope(
        onWillPop: () async {
          // if (logic.hasSelectedAvgSpeed() == false) {
          //   showToast("请先选择均速！", context);
          //   return false;
          // }
          return true;
        },
        child: Scaffold(
          backgroundColor: DColor.fff7f7f7,
          appBar: AppBar(
              centerTitle: true,
              leading: IconButton(
                icon: Image.asset(
                  DImages.formatPathPng(
                    'jiantou_left_white',
                  ),
                  height: 17,
                  width: 17,
                  color: DColor.ff232323,
                ),
                onPressed: () {
                  FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                  Navigator.pop(context);
                },
              ),
              title: Text("奖励详情",
                  style: TextStyle(color: DColor.ff242424, fontSize: 18))),
          body: Obx(() {
            return Column(
              children: [
                Expanded(child: _buildRefreshList()),
                BoxShadowContent(
                  child: SubmitButton(
                    enable: true,
                    text: "扫码发放奖励",
                    onPressed: () {
                      LoginUtils.setILogin(() async {
                        String result = await Get.to(LiveDecodePage()) ?? "";
                        QrcodeUtils.openQrCode(context, result);
                      }, context);
                    },
                  ),
                ),
              ],
            );
          }),
        ));
  }

  Widget _buildRefreshList() {
    return buildRefreshWidget(
      builder: () => Column(
        children: [
          ChallengeDetailHeader(widget.activitiesChallenge),
          _buildDetailSummary(widget.activitiesChallenge),
          // 显示列表
          Expanded(
            child: list.isNotEmpty
                ? _buildList()
                : buildLoadStateWidget(logic.loadStatus.value, () {
                    refreshController.requestRefresh();
                  }),
          ),
        ],
      ),
      onRefresh: () {
        logic.refreshData();
      },
      enablePullUp: false,
      refreshController: refreshController,
    );
  }

  Widget _buildList() {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10, top: 0),
      // color: DColor.color_4c343131,
      child: CustomScrollView(
        slivers: [
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) {
                ActivitiesChallengeStoreRewardStatistic
                    challengeStoreRewardStatistic =
                    logic.statistics.value![index];
                return GestureDetector(
                  onTap: () {
                    // 点击逻辑
                  },
                  child: buildItem(challengeStoreRewardStatistic),
                );
              },
              childCount:
                  logic.statistics.value!.length, // 确保 childCount 不超出实际列表长度
            ),
          ),
        ],
      ),
    );
  }

  Widget buildItem(ActivitiesChallengeStoreRewardStatistic item) {
    return CommonCard(
      color: DColor.white,
      child: Column(
        children: [
          _buildStoreInfo(item),
          if (item.physicalRewardStatisticVoList != null &&
              item.physicalRewardStatisticVoList!.isNotEmpty)
            _buildRewardSection("完赛奖励", "icon_gift", item.shopInfo!,
                item.physicalRewardStatisticVoList!),
          Container(height: 15,),
          if (item.luckyDrawaPhysicalRewardStatisticVoList != null &&
              item.luckyDrawaPhysicalRewardStatisticVoList!.isNotEmpty)
            _buildRewardSection("抽奖奖品", "icon_lc_draw", item.shopInfo!,
                item.luckyDrawaPhysicalRewardStatisticVoList!),
        ],
      ),
    );
  }

  Widget _buildStoreInfo(ActivitiesChallengeStoreRewardStatistic item) {
    return Container(
      // color: DColor.color_4c343131,
      child: Padding(
        padding: EdgeInsets.fromLTRB(0, 10, 0, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Image.asset(
                  DImages.formatPathPng("icon_contact"),
                  width: 16,
                ),
                Container(
                  width: 2,
                ),
                Text(
                  "门店信息",
                  style: TextStyle(
                      fontSize: 14,
                      color: DColor.ff232323,
                      fontWeight: FontWeight.w500),
                ),
              ],
            ),
            SizedBox(height: 7),
            Text(
              item.shopInfo!.name!,
              style: TextStyle(
                  fontSize: 12,
                  color: DColor.ff232323,
                  fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Text(
              "营业时间：10:00-19:00",
              style: TextStyle(fontSize: 10, color: DColor.color_ff999999),
            ),
            SizedBox(height: 4),
            Text(
              item.shopInfo!.address!,
              style: TextStyle(fontSize: 10, color: DColor.color_ff999999),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRewardSection(String title, String icon, ShopInfo shopInfo,
      List<PhysicalRewardStatisticVoList> rewards) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Image.asset(
              DImages.formatPathPng(icon),
              width: 16,
            ),
            Container(
              width: 2,
            ),
            Text(
              title,
              style: TextStyle(
                  fontSize: 14,
                  color: DColor.ff232323,
                  fontWeight: FontWeight.w500),
            ),
          ],
        ),
        SizedBox(height: 8),
        ...rewards.map((reward) => _buildRewardItem(shopInfo, reward)).toList(),
      ],
    );
  }

  Widget _buildDetailSummary(ChallengeDetail item) {
    return Column(
      children: [
        SizedBox(height: 8),
        Text(
          item.title ?? "",
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        // _buildChallengeInfo(item),
      ],
    );
  }

  Widget _buildRewardItem(
      ShopInfo shopInfo, PhysicalRewardStatisticVoList reward) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: EdgeInsets.all(0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 确保整个 Row 垂直方向居中
          children: [
            SizedBox(
              width: 62,
              height: 62,
              child: Image.network(
                reward.image!, // 替换为奖品图片的 URL
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 5),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    reward.name!,
                    style: TextStyle(
                        fontSize: 12,
                        color: DColor.ff232323,
                        fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 5),
                  Text(
                    reward.description?.isNotEmpty == true // 判断是否有值
                        ? reward.description!
                        : '', // 没有值时显示空串
                    style: TextStyle(fontSize: 8, color: DColor.color_ff999999),
                  ),
                  SizedBox(height: 5),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        " ${reward.distributedCount?.toString()}/${reward.totalCount?.toString()}",
                        style: TextStyle(fontSize: 10, color: DColor.fffb691d),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(width: 21),
            GestureDetector(
              onTap: () {
                // 点击事件逻辑
                print("用户列表被点击");
                // 你可以在这里导航到用户列表页面或触发其他逻辑
                Get.to(
                    StoreAwardStatisticDetailPage(
                        challengeId: widget.activitiesChallenge.id!,
                        shopId: shopInfo.id!,
                        physicalRewardStatisticVo: reward),
                    transition: GetTransition.Transition.rightToLeft);
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center, // 确保 Row 内部内容居中
                children: [
                  Text(
                    "用户列表",
                    style: TextStyle(fontSize: 10, color: DColor.fffb691d),
                  ),
                  SizedBox(width: 4), // 添加间距
                  Image.asset(
                    DImages.formatPathPng('icon_arrow_right'),
                    width: 6, // 设置图片宽度
                    height: 10, // 设置图片高度
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
