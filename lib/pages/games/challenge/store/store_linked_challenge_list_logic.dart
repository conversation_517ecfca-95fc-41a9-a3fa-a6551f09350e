import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/pages/base/page_controller.dart';
import 'package:new_edge/pages/games/challenge/challenge_list_state.dart';
import 'package:new_edge/pages/games/challenge/store/store_linked_challenge_list_state.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:geolocator/geolocator.dart';

class StoreLinkedChallengeListLogic
    extends PagingController<ChallengeDetail, StoreLinkedChallengeListState> {

  var position = Rx<Position?>(null);

  @override
  StoreLinkedChallengeListState getState() => StoreLinkedChallengeListState();

  @override
  Future<ApiResponse<ApiPaging<ChallengeDetail>>?> loadData(
      int pagingIndex) {
    return ActiviesApi(MyHttp.dio)
        .queryPageLinkedChallengeList(
        pageNum: pagingIndex);
  }
}
