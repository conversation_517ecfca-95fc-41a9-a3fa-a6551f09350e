import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/reward_city.dart';
import 'package:new_edge/pages/base/list_controller.dart';
import 'package:new_edge/request/MyHttp.dart';

import '../../../../model/prize_claim_form.dart';

class ChallengeChooseStoreLogic extends ListController<ShopInfo> {
  String? challengeId;
  var cityName = Rx<String?>(null);
  var position = Rx<Position?>(null);
  var selectedShop = Rx<ShopInfo?>(null);
  var searchKey = "".obs;

  var rewardCities = <RewardCity>[].obs;

  void onInit() {
    super.onInit();
    // 重置 cityName 确保每次进入页面都重新选择城市
    cityName.value = null;
  }

  Future<void> search(String key) async {
    searchKey.value = key;
    if (key.isNotEmpty) {
      refreshData();
    }
  }

  void updateCityName(String name) {
    cityName.value = name;
  }

  bool isSelectedShop(String id) {
    return selectedShop.value?.id == id;
  }

  void setSelectedShop(ShopInfo? shop) {
    selectedShop.value = shop;
  }

  @override
  Future<ApiResponse<List<ShopInfo>>?> loadData() {
    return ActiviesApi(MyHttp.dio).getShopListByCity(
        challengeId: challengeId ?? "",
        cityName: cityName.value,
        latitude: position.value?.latitude.toString(),
        longitude: position.value?.longitude.toString());
  }

  Future<ApiResponse<List<ShopInfo>>?> getNearShopList() {
    return ActiviesApi(MyHttp.dio).getShopListByCity(
        challengeId: challengeId ?? "",
        cityName: cityName.value,
        latitude: position.value?.latitude.toString(),
        longitude: position.value?.longitude.toString());
  }

  Future<ApiResponse<List<RewardCity>>?> getCityList() {
    return ActiviesApi(MyHttp.dio).getCityList(
        challengeId: challengeId ?? "",
        latitude: position.value?.latitude.toString(),
        longitude: position.value?.longitude.toString());
  }
}
