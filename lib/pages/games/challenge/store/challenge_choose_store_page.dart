import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/prize_claim_form.dart';
import 'package:new_edge/model/reward_shop.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/award/physical/physical_award_detail_logic.dart';
import 'package:new_edge/picker/constants/extensions.dart';
import 'package:new_edge/routers/router.dart';
import 'package:new_edge/util/geolocator_util.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/location_utils.dart';
import 'package:new_edge/util/modal.dart';
import 'package:new_edge/util/string_format_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:url_launcher/url_launcher.dart';

import 'challenge_choose_store_logic.dart';

class ChallengeChooseStorePage extends StatefulWidget {
  final ChallengeDetail challenge;

  final PrizeInfo? physicalReward;
  final String? type;

  ChallengeChooseStorePage(
      {required this.challenge, this.physicalReward, this.type}) {}

  @override
  _ChallengeChooseStoreState createState() => _ChallengeChooseStoreState();
}

class _ChallengeChooseStoreState
    extends RefreshState<ChallengeChooseStorePage> {
  final awardLogic = Get.findOrPut(() => PhysicalAwardDetailLogic());

  final logic = Get.put(ChallengeChooseStoreLogic());
  final list = Get.find<ChallengeChooseStoreLogic>().list;
  final cityName = Get.find<ChallengeChooseStoreLogic>().cityName;

  // final _search_controller = TextEditingController();

  Future<void> _doLocation(BuildContext context) async {
    print("执行 _doLocation 方法"); // 添加日志输出
    Position? position = await GeolocatorUtils.requestLocationPosition(context);
    logic.position.value = position;
    logic.refreshData();
    print("执行 _doLocation 方法结束"); // 添加日志输出
  }

  @override
  void initState() {
    logic.challengeId = widget.challenge.id;
    super.initState();

    Future.delayed(Duration(milliseconds: 50), () {
      print("准备开始执行 _doLocation 方法"); // 添加日志输出
      _doLocation(context);
      refreshController.requestRefresh();
    });
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  bool isAutoRefresh() {
    return false;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        leading: IconButton(
          icon: Image.asset(
            DImages.formatPathPng(
              'jiantou_left_white',
            ),
            height: 17,
            width: 17,
            color: DColor.ff232323,
          ),
          onPressed: () {
            FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
            Navigator.pop(context, false);
          },
        ),
        title: Text(widget.physicalReward != null ? "选择门店" : "查看门店",
            style: TextStyle(color: DColor.ff242424, fontSize: 18)),
        actions: [Obx(() => _buildLocationContent())],
      ),
      body: Obx(() => _buildContent(context)),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      children: [
        // Padding(
        //     padding: EdgeInsets.only(left: 10, right: 10),
        //     child: _buildSearchWidget()),
        Expanded(
            child: Padding(
                padding:
                    EdgeInsets.only(top: 10, bottom: 10, left: 15, right: 15),
                child: _buildList(context))),
        _buildSubmit()
      ],
    );
  }

  // Widget _buildSearchWidget() {
  //   return Container(
  //       padding: EdgeInsets.all(8),
  //       child: Container(
  //         height: 40,
  //         decoration: BoxDecoration(
  //           color: DColor.fff2f2f2,
  //           borderRadius: BorderRadius.all(Radius.circular(5)),
  //         ),
  //         child: DecoratedBox(
  //             decoration: BoxDecoration(
  //               borderRadius: BorderRadius.circular(5.0),
  //               color: DColor.fff2f2f2,
  //             ),
  //             child: Padding(
  //               padding: const EdgeInsets.only(left: 8, right: 8),
  //               child: TextField(
  //                 textInputAction: TextInputAction.search,
  //                 autofocus: true,
  //                 controller: _search_controller,
  //                 style: TextStyle(fontSize: 16, color: DColor.ff242424),
  //                 cursorColor: DColor.fffb691d,
  //                 onSubmitted: (value) {
  //                   if (value.isEmpty) {
  //                     showToast("请输入搜索内容", context);
  //                     return;
  //                   }
  //                   showLoadingDialogWithFuture(
  //                     context,
  //                     logic.search(value),
  //                   );
  //                 },
  //                 decoration: InputDecoration(
  //                   focusedBorder: OutlineInputBorder(
  //                     borderRadius: BorderRadius.circular(4),
  //                     borderSide: BorderSide(color: DColor.fffbfbfb, width: 0),
  //                   ),
  //                   enabledBorder: OutlineInputBorder(
  //                     borderRadius: BorderRadius.circular(4),
  //                     borderSide:
  //                         BorderSide(color: DColor.fffbfbfb, width: 0.5),
  //                   ),
  //                   hintText: '搜索',
  //                   hintStyle: TextStyle(fontSize: 15, color: DColor.ff242424),
  //                   isDense: true,
  //                   contentPadding: const EdgeInsets.symmetric(
  //                       horizontal: 15, vertical: 11),
  //                   // 调整内容的内边距
  //                   prefixIcon: Padding(
  //                     padding: const EdgeInsets.only(left: 5, right: 10),
  //                     // 调整prefixIcon的内边距
  //                     child: Image.asset(
  //                       DImages.formatPathPng('search'),
  //                       width: 20,
  //                       height: 20,
  //                     ),
  //                   ),
  //                   prefixIconConstraints:
  //                       BoxConstraints(maxWidth: 35, maxHeight: 20),
  //                   // 调整prefixIcon的最大宽度和高度
  //                   suffixIcon: GestureDetector(
  //                     onTap: () {
  //                       _search_controller.clear();
  //                     },
  //                     child: Image.asset(
  //                       DImages.formatPathPng('guanbi_grey'),
  //                       width: 15,
  //                       height: 15,
  //                     ),
  //                   ),
  //                   suffixIconConstraints:
  //                       BoxConstraints(maxWidth: 30, maxHeight: 15),
  //                 ),
  //               ),
  //             )),
  //       ));
  // }

  Widget _buildList(BuildContext context) {
    return buildRefreshWidget(
        enablePullUp: false,
        enablePullDown: true,
        onRefresh: () {
          logic.refreshData();
        },
        refreshController: refreshController,
        builder: () => list.isNotEmpty
            ? CustomScrollView(
                slivers: [
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                      ShopInfo shop = list[index];
                      bool isSelected = logic.isSelectedShop(shop.id ?? "");
                      return GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            logic.setSelectedShop(shop);
                          },
                          child: CommonCard(
                              sideColor: isSelected
                                  ? DColor.primary
                                  : Colors.transparent,
                              child: StoreInfoView(context, shop)));
                    }, childCount: list.length),
                  )
                ],
              )
            : buildLoadStateWidget(logic.loadStatus.value, () {
                refreshController.requestRefresh();
              }));
  }

  Widget _buildLocationContent() {
    if (list.isEmpty) {
      return Container();
    }
    String? firstCity = list.first.cityname;
    String city = cityName.value ?? firstCity ?? "";

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _selectCity();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
        // 左右10，上下5
        child: Row(children: [
          Image.asset(
            DImages.formatPathPng('icon_location'),
            height: 12,
            fit: BoxFit.fitHeight,
          ),
          SizedBox(width: 3),
          Text("${city}", style: TextStyle(color: DColor.black, fontSize: 12)),
          SizedBox(width: 15),
        ]),
      ),
    );
  }

  void _selectCity() async {
    Position? position = await GeolocatorUtils.requestLocationPosition(context);
    logic.position.value = position;

    if (logic.rewardCities.isEmpty) {
      await showLoadingDialogWithFuture(
        context,
        logic.getCityList(),
      ).then((result) async {
        if (result?.success == true) {
          logic.rewardCities.value = result?.result ?? [];
        }
      });
    }

    if (logic.rewardCities.isEmpty) {
      return;
    }

    var cityNames = logic.rewardCities.map((o) => o.cityName ?? "").toList();
    showCommonSelectModal<String>(context,
        initialValue: logic.cityName.value ?? "",
        onDataChanged: (dynamic value) {
      if (value != cityName.value) {
        logic.updateCityName(value);
        logic.setSelectedShop(null);
        refreshController.requestRefresh();
      }
    }, items: cityNames, values: cityNames);
  }

  Widget _buildSubmit() {
    return Visibility(
        visible: list.isNotEmpty && widget.physicalReward != null,
        child: BoxShadowContent(
            child: SubmitButton(
                text: "确认领奖",
                enable: logic.selectedShop.value != null,
                onPressed: () async {
                  _addAwardRecord();
                })));
  }

  void _addAwardRecord() {
    showLoadingDialogWithFuture(
            context,
            awardLogic.addPhysicalRewardShopRecord(widget.challenge,
                widget.physicalReward!, logic.selectedShop.value?.id ?? ""),
            barrierDismissible: false)
        .then((result) async {
      if (result?.success ?? false) {
        showToast('用户已确认', context);
        switch (widget.type) {
          case "ChallengePrizePage":
            // 成功领取后返回上一个页面，并附带结果
            Get.back(result: true);
            break;
          case "PhysicalAwardDetailPage":
            Get.until(
                (route) => route.settings.name == RouteGet.challengeDetail);
            break;
          case "showSignTipConfirmBottomSheet":
            Get.until(
                (route) => route.settings.name == RouteGet.challengeDetail);
            break;
        }
      } else {
        showToast('领取失败！', context);
      }
    });
  }
}

Widget StoreInfoView(BuildContext context, ShopInfo shop) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(children: [
        Expanded(
            child: Text(shop.name ?? "",
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold))),
        Visibility(
            visible: shop.districtStartPoint != null,
            child: Text("${formatMToKm(shop.districtStartPoint ?? 0)}KM",
                style: TextStyle(fontSize: 10))),
      ]),
      SizedBox(height: 15),
      Row(children: [
        Expanded(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Visibility(
              visible: shop.businessOpenTime != null,
              child: Text(
                  "营业时间：${shop.businessOpenTime?.formattedTime ?? ""} - ${shop.businessCloseTime?.formattedTime ?? ""}",
                  style: TextStyle(fontSize: 10))),
          Text("${shop.address ?? "暂无地址"}", style: TextStyle(fontSize: 10)),
        ])),
        SizedBox(
          width: 0.5,
          height: 30,
          child: DecoratedBox(
            decoration: BoxDecoration(color: Color(0xFFEAEAEA)),
          ),
        ),
        SizedBox(width: 15),
        Row(children: [
          GestureDetector(
              onTap: () {
                if (shop.latLon != null) {
                  var latLon = shop.latLon?.split(",");
                  if (latLon != null && latLon.length == 2) {
                    showToast("暂无地图信息", context);
                    return;
                  }
                  LocationUtils.showSelectMap(context, double.parse(latLon![1]),
                      double.parse(latLon[0]));
                } else {
                  showToast("暂无地图信息", context);
                }
              },
              child: Image.asset(
                DImages.formatPathPng('icon_location_red'),
                height: 16,
                fit: BoxFit.fitHeight,
              )),
          SizedBox(width: 15),
          GestureDetector(
              onTap: () {
                String? tel = shop.telephone?.toString();
                if (tel != null) {
                  launchUrl(Uri.parse("tel:${tel}"));
                } else {
                  showToast("暂无电话", context);
                }
              },
              child: Image.asset(
                DImages.formatPathPng('icon_phone_red'),
                height: 16,
                fit: BoxFit.fitHeight,
              ))
        ])
      ]),
    ],
  );
}
