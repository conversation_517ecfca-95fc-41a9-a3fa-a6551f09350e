import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/activities_user_challenge.dart';
import 'package:new_edge/model/join_challenge.dart';
import 'package:new_edge/routers/router.dart';
import 'package:new_edge/widgets/common_card.dart';

const double _defaultHeight = 244.0;

class ChallengeUserPage extends StatelessWidget {
  final List<JoinChallenge> items;
  final double height;
  final Function(int index) onItemClicked; // Callback for item click

  const ChallengeUserPage(
      {Key? key,
      required this.items,
      required this.onItemClicked,
      this.height = _defaultHeight})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      padding: EdgeInsets.zero,
      child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: items.length,
          itemBuilder: (BuildContext context, int index) {
            JoinChallenge item = items[index];
            return ChallengeUserItem(item);
          }),
    );
  }
}

Widget ChallengeUserItem(JoinChallenge item) {
  return GestureDetector(
      onTap: () {
        Get.toNamed(RouteGet.challengeDetail, arguments: {
          'id': item.challengeId,
        });
      },
      child: SizedBox(
          width: 170,
          height: 244,
          child: CommonCard(
              margin:EdgeInsets.only(left: 5,right: 5,top: 5,bottom: 10),
              shadowColor: const Color(0xFF000000).withOpacity(0.6),
              padding: EdgeInsets.zero,
              child: Column(
                children: [
                  Expanded(child: _buildItem(item)),
                  _buildAwardButton(item.challengeId ?? "", item.state ?? "")
                ],
              ))));
}

Widget _buildItem(JoinChallenge item) {
  DateTime startTime = item.startTime != null
      ? DateTime.parse(item.startTime ?? '')
      : DateTime.now();
  DateTime endTime = item.endTime != null
      ? DateTime.parse(item.endTime ?? '')
      : DateTime.now();

  String startTimeText =
      DateFormat('yyyy.MM.dd').format(startTime);
  String endTimeText =
      DateFormat('yyyy.MM.dd').format(endTime);

  return Container(
      padding: EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(2.0),
                  child: SizedBox(
                      height: 50,
                      width: 50,
                      child: CachedNetworkImage(
                          imageUrl: item.logo ?? "",
                          placeholder: (context, url) => Image.asset(
                                DImages.formatPathPng('img_placeholder_bg'),
                                fit: BoxFit.cover,
                              ),
                          errorWidget: (context, url, error) => Image.asset(
                                DImages.formatPathPng('img_placeholder_bg'),
                                fit: BoxFit.cover,
                              ),
                          fit: BoxFit.cover))),
              // _buildHistoryAwardItem(item.activitiesChallenge?.reward)
              _buildEndTime(item)
            ],
          ),
          SizedBox(height: 6),
          Container(
            height: 40,
            child: Text(
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              item.title ?? "",
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(height: 6),
          _buildHistoryMarkItem(
            item.mileageProgress,
            item.mileageTarget,
            item.altitudeProgress,
            item.altitudeTarget,
            item.durationProgress,
            item.durationTarget,
            item.timesProgress,
            item.timesTarget,
          ),
          SizedBox(height: 10),
          Container(
            child: Text(
              "完成综合骑行挑战",
              style: TextStyle(fontSize: 12),
            ),
          ),
          SizedBox(height: 6),
          Container(
            child: Text(
              "$startTimeText-$endTimeText",
              style: TextStyle(fontSize: 12, fontFamily: DFamily.dinBold),
            ),
          ),
        ],
      ));
}

Widget _buildHistoryMarkItem(
    num? mileageProgress,
    num? mileageTarget,
    num? altitudeProgress,
    num? altitudeTarget,
    num? durationProgress,
    num? durationTarget,
    num? timesProgress,
    num? timesTarget) {
  return Row(
    children: [
      _buildHistoryMarkSubItem(
          "mileage", mileageProgress ?? 0, mileageTarget ?? 0),
      _buildHistoryMarkSubItem(
          "altitude", altitudeProgress ?? 0, altitudeTarget ?? 0),
      _buildHistoryMarkSubItem(
          "duration", durationProgress ?? 0, durationTarget ?? 0),
      _buildHistoryMarkSubItem("times", timesProgress ?? 0, timesTarget ?? 0),
    ],
  );
}

Widget _buildHistoryMarkSubItem(
  String assetName,
  num progress,
  num target,
) {
  bool hasItem = target > 0;
  String status = "none";
  if (progress >= target) {
    status = "full";
  }
  // else if (progress > 0) {
  //   status = "half";
  // }

  return Visibility(
      visible: hasItem,
      child: Row(
        children: [
          Image.asset(
            DImages.formatPathPng("${assetName}_${status}"),
            width: 21,
            height: 19,
          ),
          SizedBox(width: 10),
        ],
      ));
}

Widget _buildEndTime(JoinChallenge item) {
  DateTime endTime = item.endTime != null
      ? DateTime.parse(item.endTime ?? '')
      : DateTime.now();
  final difference = max(0, endTime.difference(DateTime.now()).inDays);

  return RichText(
    text: TextSpan(
      children: <TextSpan>[
        const TextSpan(
            text: '剩余 ', style: TextStyle(color: Colors.black, fontSize: 10)),
        TextSpan(
          text: '$difference',
          style: TextStyle(
            color: DColor.primary,
            fontFamily: DFamily.dinBold,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        TextSpan(
            text: ' 天',
            style: TextStyle(
              color: Colors.black,
              fontSize: 10,
            )),
      ],
    ),
  );
}

Widget _buildHistoryAwardItem(List<Reward>? rewards) {
  bool hasVirtualReward =
      rewards?.any((element) => element.virtualReward != null) ?? false;
  bool hasPhysicalReward =
      rewards?.any((element) => element.physicalReward != null) ?? false;
  return Column(
    children: [
      Visibility(
          visible: hasVirtualReward,
          child: MaterialButton(
            padding: EdgeInsets.only(left: 6, right: 6, top: 4, bottom: 4),
            onPressed: null,
            child: Text(
              '商城积分',
              style: TextStyle(color: DColor.primary, fontSize: 12),
            ),
            shape: RoundedRectangleBorder(
              //边框颜色
              side: BorderSide(
                color: DColor.primary,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(3.0),
            ),
          )),
      SizedBox(height: 5),
      Visibility(
          visible: hasPhysicalReward,
          child: MaterialButton(
            padding: EdgeInsets.only(left: 6, right: 6, top: 4, bottom: 4),
            onPressed: null,
            child: Text(
              '数字奖杯',
              style: TextStyle(color: DColor.primary, fontSize: 12),
            ),
            shape: RoundedRectangleBorder(
              //边框颜色
              side: BorderSide(
                color: DColor.primary,
                width: 1,
              ),
              borderRadius: BorderRadius.circular(3.0),
            ),
          )),
    ],
  );
}

Widget _buildAwardButton(String challengeId, String status) {
  String text = "";
  Color textColor = DColor.white;
  Color buttonColor = DColor.primary;

  if (status == "inProgress") {
    text = "进行中";
  } else if (status == "completed") {
    text = "去领奖";
  } else if (status == "finish") {
    text = "已领奖";
  } else if (status == "exit") {
    text = "已退出";
    buttonColor = DColor.primary.withOpacity(0.5);
  } else if (status == "fail") {
    text = "未完成";
    buttonColor = DColor.primary.withOpacity(0.5);
  } else if (status == "over") {
    text = "已结束";
    buttonColor = DColor.primary.withOpacity(0.5);
    textColor = DColor.white;
  }

  return Container(
    height: 32,
    width: double.infinity,
    color: buttonColor,
    child: Center(child: Text(
      text,
      style: TextStyle(color: textColor, fontSize: 13),
    )),
  );
}
