import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/pages/games/challenge/award/physical/physical_award_detail_page.dart';
import 'package:new_edge/pages/games/challenge/award/third/third_award_detail_page.dart';
import 'package:new_edge/routers/router.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:intl/intl.dart' show DateFormat;

Widget ChallengeDetailHeader(ChallengeDetail item) {
  return Container(
      height: 220,
      child: Stack(
        children: [
          CachedNetworkImage(
              imageUrl: item.headBg ?? "",
              width: double.infinity,
              height: 160,
              placeholder: (context, url) => Image.asset(
                    DImages.formatPathPng('img_placeholder_bg'),
                    width: double.infinity,
                    height: 160,
                    fit: BoxFit.cover,
                  ),
              errorWidget: (context, url, error) => Image.asset(
                    DImages.formatPathPng('img_placeholder_bg'),
                    // Use placeholder as error widget
                    width: double.infinity,
                    fit: BoxFit.cover,
                  ),
              fit: BoxFit.cover),
          Positioned(
              top: 120,
              left: 0,
              right: 0,
              child: Column(children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: CachedNetworkImage(
                      imageUrl: item.logo ?? "",
                      width: 100,
                      height: 100,
                      placeholder: (context, url) => Image.asset(
                            DImages.formatPathPng('img_placeholder_bg'),
                            width: 100,
                            height: 100,
                            fit: BoxFit.fitHeight,
                          ),
                      errorWidget: (context, url, error) => Image.asset(
                            DImages.formatPathPng('img_placeholder_bg'),
                            // Use placeholder as error widget
                            width: 100,
                            height: 100,
                            fit: BoxFit.fitHeight,
                          ),
                      fit: BoxFit.fitHeight),
                )
              ]))
        ],
      ));
}

Widget buildChallengeAward(ChallengeDetail item, bool isTargetCompleted) {
  bool userIsComplete = item.userIsComplete ?? false;
  List<Widget> rewardItems = [];

  //实物奖品
  item.physicalRewards.forEach((element) {
    bool isProvided = element.isProvided;
    DateTime providedEndTime = element.prizeClaimDeadline != null
        ? DateTime.parse(element.prizeClaimDeadline ?? '')
        : DateTime.now();
    DateTime pickupStartTime = element.pickupStartTime != null
        ? DateTime.parse(element.pickupStartTime ?? '')
        : DateTime.now();
    DateTime pickupEndTime = element.pickupEndTime != null
        ? DateTime.parse(element.pickupEndTime ?? '')
        : DateTime.now();
    bool isProvidedExpired = (DateTime.now().millisecondsSinceEpoch >
                providedEndTime.millisecondsSinceEpoch &&
            !isProvided) ||
        (DateTime.now().millisecondsSinceEpoch >
                pickupEndTime.millisecondsSinceEpoch &&
            !isProvided); //没有领取，并且已经超时
    String providedEndTimeText = isProvidedExpired
        ? "超时未领取"
        : "请在${DateFormat('yyyy.MM.dd HH:mm:ss').format(providedEndTime)}前领奖";

    String tip = providedEndTimeText;
    if (!isTargetCompleted) {
      tip = "完成挑战后领取";
    } else if (element.prizeGetMethod == PrizeGetMethod.mail) {
      //仅快递
      tip = (element.prizeGetState == PrizeGetState.got ? "已发货" : "待发货");
    } else if (element.prizeGetMethod == PrizeGetMethod.store) {
      //仅到店
      if (element.prizeGetState == PrizeGetState.toGet) {
        if (!isProvidedExpired) {
          tip =
              "请在${DateFormat('yyyy.MM.dd').format(pickupStartTime)}-${DateFormat('yyyy.MM.dd').format(pickupEndTime)}前往门店领奖";
        } else {
          tip = "超时未领取";
        }
      } else {
        tip = "已领取";
      }
    }
    Color tipColor = isTargetCompleted && !isProvidedExpired
        ? DColor.primary
        : DColor.color_ff999999;

    rewardItems.add(Row(children: [
      Expanded(
          child: _buildChallengeAwardItem(
              element.image ?? "", element.name ?? "", element.description, tip,
              tipColor: tipColor)),
      SizedBox(width: 4),
      Visibility(
          visible: !isProvidedExpired,
          child: _buildAwardButton(
              isTargetCompleted, isProvided, userIsComplete, () {
            Get.to(
                PhysicalAwardDetailPage(
                  challenge: item,
                  physicalReward: element,
                ),
                transition: Transition.rightToLeft);
          }))
    ]));
  });

  //第三方奖品
  item.thirdPartyRewards.forEach((element) {
    bool isProvided = element.isProvided;
    DateTime providedEndTime = element.prizeClaimDeadline != null
        ? DateTime.parse(element.prizeClaimDeadline ?? '')
        : DateTime.now();
    bool isProvidedExpired = DateTime.now().millisecondsSinceEpoch >
        providedEndTime.millisecondsSinceEpoch;
    String providedEndTimeText = isProvidedExpired
        ? "超时未领取"
        : "请在${DateFormat('yyyy.MM.dd HH:mm:ss').format(providedEndTime)}前领奖";

    String tip = isTargetCompleted
        ? (isProvided ? "已领奖" : providedEndTimeText)
        : "完成挑战后领取";
    Color tipColor = isTargetCompleted && !isProvidedExpired
        ? DColor.primary
        : DColor.color_ff999999;

    rewardItems.add(Row(children: [
      Expanded(
          child: _buildChallengeAwardItem(
              element.image ?? "", element.name ?? "", element.description, tip,
              tipColor: tipColor)),
      SizedBox(width: 4),
      Visibility(
          visible: !isProvidedExpired,
          child: _buildAwardButton(
              isTargetCompleted, element.isProvided, userIsComplete, () {
            Get.to(
                ThirdAwardDetailPage(
                  challenge: item,
                  thirdPartyReward: element,
                ),
                transition: Transition.rightToLeft);
          }))
    ]));
  });
  //虚拟奖品
  bool hasVirtualReward = item.hasVirtualReward;
  if (hasVirtualReward) {
    bool isProvided = item.hasVirtualRewardProvided;

    String tip = isTargetCompleted ? (isProvided ? "已发放" : "未发放") : "完成挑战后领取";
    Color tipColor = isTargetCompleted ? DColor.primary : DColor.color_ff999999;

    rewardItems.add(Row(children: [
      Expanded(
          child: _buildChallengeAwardItem(item.medalInfoNew?.thumb ?? "",
              item.medalInfoNew?.shortTitle ?? "", item.medalInfoNew?.description, tip,
              tipColor: tipColor)),
      SizedBox(width: 4),
      Visibility(
          visible:isProvided,
          child: _buildAwardButton(
              isTargetCompleted, isProvided, userIsComplete, () {
            Get.toNamed(RouteGet.trophyDetail, arguments: {
              'trophyRecordId': item.medalInfoNew?.trophyRecordId ?? "",
            });
          }))
    ]));
  }

  if (rewardItems.isEmpty) {
    return Container();
  }
  return CommonCard(
      child: Column(
    children: [
      Row(
        children: [
          Image.asset(
            DImages.formatPathPng(
              'icon_award',
            ),
            height: 20,
            width: 20,
            color: DColor.ff232323,
          ),
          SizedBox(width: 8),
          Text(
            "赢取奖励",
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
      SizedBox(height: 8),
      Visibility(
          visible: rewardItems.isNotEmpty,
          child: Column(children: rewardItems)),
    ],
  ));
}

Widget _buildChallengeAwardItem(
    String image, String title, String? summary, String? tip,
    {Color tipColor = DColor.primary}) {
  return Padding(
      padding: EdgeInsets.only(top: 5, bottom: 5),
      child: Row(
        children: [
          SizedBox(
              width: 62,
              height: 62,
              child: CachedNetworkImage(
                  imageUrl: image ?? "",
                  placeholder: (context, url) => Image.asset(
                        DImages.formatPathPng('img_placeholder_bg'),
                        fit: BoxFit.fitHeight,
                      ),
                  errorWidget: (context, url, error) => Image.asset(
                        DImages.formatPathPng('img_placeholder_bg'),
                        fit: BoxFit.fitHeight,
                      ),
                  fit: BoxFit.fitHeight)),
          SizedBox(width: 13),
          Expanded(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                Text(title,
                    style: TextStyle(color: DColor.ff232323, fontSize: 13)),
                Visibility(
                    visible: summary?.isNotEmpty ?? false,
                    child: Padding(
                        padding: EdgeInsets.only(top: 8),
                        child: Text(summary ?? "",
                            style: TextStyle(
                                color: DColor.color_ff999999, fontSize: 9)))),
                Visibility(
                    visible: tip?.isNotEmpty ?? false,
                    child: Padding(
                        padding: EdgeInsets.only(top: 8),
                        child: Text(tip ?? "",
                            style: TextStyle(color: tipColor, fontSize: 9))))
              ]))
        ],
      ));
}

Widget _buildAwardButton(bool isCompleted, bool isProvided, bool userIsComplete,
    VoidCallback? onPressed) {
  return Visibility(
      visible: isCompleted && userIsComplete,
      child: isProvided
          ? GestureDetector(
              onTap: onPressed,
              child: Text(
                "详情 >",
                style: TextStyle(color: DColor.primary, fontSize: 12),
              ),
            )
          : MaterialButton(
              minWidth: 70,
              padding: EdgeInsets.only(left: 8, right: 8, top: 9, bottom: 9),
              onPressed: onPressed,
              shape: RoundedRectangleBorder(
                //边框颜色
                side: BorderSide(
                  color: isProvided ? DColor.primary : Colors.transparent,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(3.0),
              ),
              color: isProvided ? Colors.white : DColor.primary,
              child: Text(
                '去领奖',
                style: TextStyle(
                    color: isProvided ? DColor.primary : Colors.white,
                    fontSize: 12),
              ),
            ));
}

Widget buildChallengeTip(String? endTime) {
  DateTime raceEndTime = DateTime.parse(endTime ?? "");
  Duration duration = Duration(hours: 4);

  // 使用 subtract 方法将 DateTime 对象减去指定的时间段
  DateTime result = raceEndTime.subtract(duration);

  String endTimeTip = DateFormat('yyyy.MM.dd HH:mm:ss').format(result);
  return CommonCard(
      sideColor: DColor.primary,
      child: Column(
        children: [
          Text("温馨提示", style: TextStyle(fontSize: 14, color: DColor.primary)),
          SizedBox(height: 5),
          Text(
              textAlign: TextAlign.center,
              "由于码表数据同步可能延迟，建议您在【$endTimeTip】前完成骑行数据同步，避免影响活动奖励领取",
              style: TextStyle(fontSize: 12, color: DColor.primary)),
        ],
      ));
}
