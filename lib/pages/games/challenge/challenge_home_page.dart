import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/app.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_info.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/challenge_banner_logic.dart';
import 'package:new_edge/pages/games/challenge/challenge_item_view.dart';
import 'package:new_edge/pages/games/challenge/challenge_list_logic.dart';
import 'package:new_edge/pages/games/challenge/challenge_user_logic.dart';
import 'package:new_edge/pages/games/challenge/challenge_user_page.dart';
import 'package:new_edge/routers/router.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/slide_widget.dart';

class ChallengeHomePage extends StatefulWidget {
  @override
  _ChallengeHomeState createState() => _ChallengeHomeState();
}

class _ChallengeHomeState extends RefreshState<ChallengeHomePage>
    with RouteAware {
  final listLogic = Get.put(ChallengeListLogic());
  final listState = Get.find<ChallengeListLogic>().pagingState;

  final bannerLogic = Get.put(ChallengeBannerLogic());
  final bannerState = Get.find<ChallengeBannerLogic>().pagingState;

  final userLogic = Get.put(ChallengeUserLogic());
  final userState = Get.find<ChallengeUserLogic>().pagingState;

  @override
  Rx<FitLoadStatus> getLoadState() {
    return listLogic.loadStatus;
  }
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    if (mounted) {
      userLogic.refreshData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => buildRefreshWidget(
        builder: () => bannerState.value.hasData() ||
                userState.value.hasData() ||
                listState.value.hasData()
            ? _buildList(context)
            : buildLoadStateWidgetFullEmpty(listLogic.loadStatus.value, () {
                refreshController.requestRefresh();
              }, "bg_challenge_empty"),
        onRefresh: () {
          listLogic.refreshData();
          bannerLogic.refreshData();
          userLogic.refreshData();
        },
        onLoad: listLogic.loadMoreData,
        enablePullUp: listState.value.hasMore,
        refreshController: refreshController));
  }

  Widget _buildList(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Column(
            children: [
              Visibility(
                visible: bannerState.value.hasData(),
                child: Container(
                  padding: EdgeInsets.only(top: 10, bottom: 5),
                  child: SlideImageWidget(
                      context, bannerState.value.bannerlist(), (index) {
                    Get.toNamed(RouteGet.challengeDetail, arguments: {
                      'id': bannerState.value.getItem(index).activityId,
                    });
                  }, height: 150),
                ),
              ),
              Visibility(
                visible: userState.value.hasData(),
                child: Container(
                  padding: EdgeInsets.only(left: 10, right: 0),
                  child: ChallengeUserPage(
                      items: userState.value.data,
                      onItemClicked: (int index) {
                        Get.toNamed(RouteGet.challengeDetail, arguments: {
                          'id': userState.value.getItem(index).id,
                        });
                      }),
                ),
              ),
            ],
          ),
        ),
        SliverList(
          delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
                ChallengeInfo item = listState.value.data[index];
            return ChallengeItem(
              item: item,
              onJoinClicked: () {
                Get.toNamed(RouteGet.challengeDetail, arguments: {
                  'id': item.id,
                });
                // showLoadingDialogWithFuture(
                //   context,
                //   listLogic.joinChallenge(listState.value.data[index].id ?? ""),
                // ).then((result) async {
                //   if (result?.success ?? false) {
                //     showToast('您已成功加入！', context);
                //     listLogic.refreshData();
                //     userLogic.refreshData();
                //   } else {
                //     showToast(result?.message ?? '加入失败, 请稍后再试!', context);
                //   }
                // });
              },
            );
          }, childCount: listState.value.getCount()),
        ),
      ],
    );
  }
}
