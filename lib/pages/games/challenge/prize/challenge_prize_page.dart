import 'package:cached_network_image/cached_network_image.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:new_edge/bloc/follow_bloc.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/io/follow.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/activities_challenge_reward_distribution_records.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/challenge_lucky_drawa.dart';
import 'package:new_edge/model/prize_claim_form.dart';
import 'package:new_edge/model/shopping_address.dart';
import 'package:new_edge/pages/address/user_address_widget.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/express/express_detail_page.dart';
import 'package:new_edge/pages/games/challenge/award/third/third_award_detail_page.dart';
import 'package:new_edge/pages/games/challenge/prize/challenge_prize_logic.dart';
import 'package:new_edge/pages/games/challenge/prize/physical_award_express_page.dart';
import 'package:new_edge/pages/games/challenge/store/challenge_choose_store_page.dart';
import 'package:new_edge/pages/personal/qrcode/personal_qrcode_detail_page.dart';
import 'package:new_edge/picker/constants/constants.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/routers/router.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/modal_pop_utils.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/html_widget.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/slide_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:oktoast/oktoast.dart';

class ChallengePrizePage extends StatefulWidget {
  final ChallengeDetail? challenge;
  final String luckyDrawaId;
  final bool? isComplete;

  ChallengePrizePage(
      {required this.challenge, required this.luckyDrawaId, this.isComplete}) {}

  @override
  _ChallengePrizeState createState() => _ChallengePrizeState();
}

class _ChallengePrizeState extends RefreshState<ChallengePrizePage> {
  late ChallengePrizeLogic logic;
  late Rx<ChallengeLuckyDrawa?> item;
  int _autoplayDelay = 2000;

  @override
  void initState() {
    logic = Get.put(
        ChallengePrizeLogic(widget.challenge?.id ?? "", widget.luckyDrawaId));
    item = Get.find<ChallengePrizeLogic>().data;
    super.initState();
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff232323,
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                Navigator.pop(context, false);
              },
            ),
            title: Text("幸运好礼",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: Obx(() => _buildRefreshList(context)));
  }

  Widget _buildRefreshList(BuildContext context) {
    return buildRefreshWidget(
        enablePullUp: false,
        onRefresh: () {
          logic.refreshData();
        },
        refreshController: refreshController,
        builder: () => (item.value?.id ?? '').isNotEmpty
            ? _buildList(context)
            : buildLoadStateWidget(logic.loadStatus.value, () {
                refreshController.requestRefresh();
              }));
  }

  Widget _buildList(BuildContext context) {
    return CustomScrollView(
      slivers: [
        SliverPadding(
            padding: EdgeInsets.only(left: 10, right: 10, top: 10),
            sliver: SliverToBoxAdapter(
                child: Column(
              children: [
                _buildHeaderInfo(context),
                _buildChoseDistributionMethod(context),
                _buildStoreInfo(context),
                _buildAddressInfo(context),
                _buildTipInfo(context)
              ],
            ))),
      ],
    );
  }

  Widget _buildHeaderInfo(BuildContext context) {
    var lotteryCount = item.value?.numberOfLotteryDraws ?? 0;
    if (lotteryCount == 0) {
      return SizedBox(height: 200, child: Text("很遗憾，没有抽中奖品"));
    }
    if (item.value?.gainPrizeList?.isNotEmpty ?? false) {
      return _buildAwardInfo(context);
    }
    return _buildPrizeInfo(context);
  }

  Widget _buildPrizeInfo(BuildContext context) {
    return CommonCard(
        padding: EdgeInsets.only(top: 15, left: 10, right: 10),
        child: Container(
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                _buildPrizeBanner(),
                SizedBox(
                  height: 8,
                ),
                _buildPrizeButton(context),
                SizedBox(
                  height: 15,
                ),
              ],
            )));
  }

  Widget _buildPrizeBanner() {
    List<String> prizeInfoList =
        item.value?.prizeList.map((e) => e.image ?? '').toList() ?? [];

    return SlideImageWidget(context, prizeInfoList, (index) {},
        height: 300,
        autoplayDelay: _autoplayDelay,
        showPagination: false,
        placeholder: null);
  }

  Widget _buildPrizeButton(BuildContext context) {
    bool isComplete = widget.isComplete ?? false;
    bool hasPrize = false;
    String text = "完赛后开启抽奖";
    bool enable = false;
    if (isComplete && !hasPrize && (widget.luckyDrawaId.isNotEmpty)) {
      text = "立即抽奖";
      enable = true;
    }

    DateTime startTime = DateTime.parse(item.value!.startTime ?? '');
    DateTime endTime = DateTime.parse(item.value!.endTime ?? '');
    if (DateTime.now().millisecondsSinceEpoch >
        endTime.millisecondsSinceEpoch) {
      text = "抽奖活动已结束";
      enable = false;
    }

    if (DateTime.now().millisecondsSinceEpoch <
        startTime.millisecondsSinceEpoch) {
      text = "抽奖活动即将开始";
      enable = false;
    }

    return SizedBox(
        width: double.infinity,
        child: SubmitButton(
            enable: enable,
            text: text,
            onPressed: () async {
              setState(() {
                _autoplayDelay = 100;
              });
              showLoadingDialogWithFuture(context, logic.submitPrizeClaim(),
                      delayTime: Duration(seconds: 3))
                  .then((result) async {
                setState(() {
                  _autoplayDelay = 2000;
                });
                if (result.success ?? false) {
                  showToast("抽奖成功!");
                  setState(() {});
                } else {
                  showToast(result.message ?? '抽奖失败, 请稍后再试!');
                }
              });
            }));
  }

  Widget _buildAwardInfo(BuildContext context) {
    return CommonCard(
        padding: EdgeInsets.only(top: 15, left: 10, right: 10),
        child: Container(
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                _buildAwardImage(),
                SizedBox(
                  height: 8,
                ),
                _buildSponsorText(context),
                SizedBox(
                  height: 8,
                ),
                _buildAwardButton(context),
                SizedBox(
                  height: 15,
                ),
              ],
            )));
  }

  Widget _buildAwardImage() {
    String? image = item.value?.gainPrizeList?.first.image ?? '';
    return SizedBox(
        height: 200,
        child: CachedNetworkImage(
            imageUrl: image,
            placeholder: (context, url) => Image.asset(
                  DImages.formatPathPng('img_placeholder_bg'),
                  fit: BoxFit.fitWidth,
                ),
            errorWidget: (context, url, error) => Image.asset(
                  DImages.formatPathPng('img_placeholder_bg'),
                  fit: BoxFit.fitWidth,
                ),
            fit: BoxFit.fitWidth));
  }

  /**
   * 到店领取的状态按钮显示（未领取、出示领奖码）
   */
  Widget _buildAwardButton(BuildContext context) {
    PrizeInfo? prizeInfo = item.value?.gainPrizeList?.first;
    if (prizeInfo == null) {
      return Container();
    }
    String? prizeClaimDeadline = prizeInfo.prizeClaimDeadline;
    DateTime providedEndTime = prizeClaimDeadline != null
        ? DateTime.parse(prizeClaimDeadline ?? '')
        : DateTime.now();

    bool isProvidedExpired = DateTime.now().millisecondsSinceEpoch >
        providedEndTime.millisecondsSinceEpoch;
    String providedEndTimeText = isProvidedExpired
        ? "超时未领取"
        : "请在${DateFormat('yyyy.MM.dd HH:mm:ss').format(providedEndTime)}前领奖";

    bool hasPrized = prizeInfo.isProvided;
    String text = "未领取";
    if (prizeInfo.type == CollectType.third) {
      text = "填写领奖信息";
    }

    if (hasPrized) {
      text = "查看详情";
    }

    DateTime pickupStartTime = prizeInfo.pickupStartTime != null
        ? DateTime.parse(prizeInfo.pickupStartTime ?? '')
        : DateTime.now();
    DateTime pickupEndTime = prizeInfo.pickupEndTime != null
        ? DateTime.parse(prizeInfo.pickupEndTime ?? '')
        : DateTime.now();

    DateFormat dateFormat = DateFormat('yyyy.MM.dd');

//  提前定义布尔值变量，用于简化判断逻辑
    final bool showTimeoutButton = !isProvidedExpired && !hasPrized;
    final bool showThirdPartyRewardButton = prizeInfo.type == CollectType.third;

    final bool hasPhysicalReward = prizeInfo.isPhysicalReward;
    final PrizeGetMethod? prizeGetMethod = prizeInfo.prizeGetMethod;
    final PrizeGetState? prizeGetState = prizeInfo.prizeGetState;
    final bool hasRewardRecord = hasPhysicalReward && prizeGetMethod == null;
    final bool isInStorePickup =
        hasPhysicalReward && prizeGetMethod == PrizeGetMethod.store;
    final bool isExpressPickup =
        hasPhysicalReward && prizeGetMethod == PrizeGetMethod.mail;
    final bool isRewardReceived =
        hasPhysicalReward && prizeGetState == PrizeGetState.got;
    final bool isRewardPending =
        hasPhysicalReward && prizeGetState == PrizeGetState.registered;

// 定义显示不同按钮的布尔值
    final bool showInStoreReceivedButton = isInStorePickup && isRewardReceived;
    final bool showInStorePickupCodeButton = isInStorePickup && isRewardPending;
    final bool showPendingSelectionButton =
        hasPhysicalReward && !hasRewardRecord;
    final bool showExpressPickupButton = isExpressPickup;

    return Column(
      children: [
        // 超时未领取按钮
        if (showTimeoutButton)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8),
            child: Text(
              providedEndTimeText,
              style: TextStyle(
                color: DColor.color_ff999999,
                fontSize: 14,
              ),
            ),
          ),

        // 第三方奖品的按钮
        if (showThirdPartyRewardButton)
          SizedBox(
            width: double.infinity,
            child: SubmitButton(
              enable: true,
              text: text,
              onPressed: () {
                Get.to(
                  ThirdAwardDetailPage(
                    challenge: widget.challenge ?? ChallengeDetail(),
                    thirdPartyReward: prizeInfo,
                  ),
                );
              },
            ),
          ),

        // 到店领取 - 已领取按钮
        if (showInStoreReceivedButton)
          Column(
            children: [
              Text(
                "已领取",
                style: TextStyle(
                  fontSize: 14,
                  color: DColor.primary,
                ),
                textAlign: TextAlign.center,
              ),
              _buildDistributionInfo(context),
            ],
          ),

        // 到店领取 - 出示领奖码
        if (showInStorePickupCodeButton)
          Column(
            children: [
              MaterialButton(
                minWidth: 70,
                padding: EdgeInsets.symmetric(vertical: 9, horizontal: 8),
                onPressed: () {
                  Get.to(PersonalQrcodeDetailPage(
                    tip: "请出示给门店工作人员扫码",
                    otherInfo: {
                      'acPhysicalReward': prizeInfo.prizeClaimFormId ?? "",
                    },
                  ));
                },
                shape: RoundedRectangleBorder(
                  side: BorderSide(color: DColor.primary, width: 1),
                  borderRadius: BorderRadius.circular(3.0),
                ),
                color: DColor.primary,
                child: Text(
                  '出示领取码',
                  style: TextStyle(
                    color: DColor.white,
                    fontSize: 12,
                  ),
                ),
              ),
              SizedBox(height: 5),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  text:
                      "请在${dateFormat.format(pickupStartTime)}-${dateFormat.format(pickupEndTime)} 前往门店领取奖励\n",
                  style: TextStyle(color: Color(0xFFFF0000), fontSize: 12),
                  children: [
                    TextSpan(
                      text: "具体时间请与门店联系",
                      style: TextStyle(color: Color(0xFFFF0000), fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),

        // 待选择领取方式的未领取按钮
        if (showPendingSelectionButton)
          SizedBox(
            width: 100,
            child: SubmitButton(
              enable: true,
              text: text,
              backgroundColor: DColor.ff808080,
              onPressed: () {},
            ),
          ),

        // 快递领取 - 待发放和查看物流
        if (showExpressPickupButton)
          _buildExpressPickupButton(isRewardReceived),
      ],
    );
  }

  Widget _buildTipInfo(BuildContext context) {
    if (item.value?.rtDescription == null) {
      return Container();
    }
    return CommonCard(
      child: HtmlContent(
        context,
        "${item.value?.rtDescription ?? ""}",
      ),
    );
  }

  Widget _buildSponsorText(BuildContext context) {
    PrizeInfo? prizeInfo = item.value?.gainPrizeList?.first;
    if (prizeInfo == null) {
      return Container();
    }
    String? sponsor = prizeInfo.sponsor ?? "";

    String? name = prizeInfo.name ?? "";

    return Column(children: [
      Visibility(
          visible: sponsor.isNotEmpty,
          child: Padding(
              padding: EdgeInsets.only(top: 8, bottom: 8),
              child: Text("恭喜您获得由" + sponsor + "提供的",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: DColor.ff242424,
                    fontSize: 17,
                    fontFamily: "PF",
                  )))),
      Visibility(
          visible: name.isNotEmpty,
          child: Padding(
              padding: EdgeInsets.only(top: 8, bottom: 0),
              child: Text(name + "*1",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: DColor.ff242424,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    fontFamily: "PF",
                  )))),
    ]);
  }

  Widget _buildChoseDistributionMethod(BuildContext context) {
    PrizeInfo? prizeInfo = item.value?.gainPrizeList?.first;
    if (prizeInfo == null) {
      return Container();
    }
    String? prizeClaimDeadline = prizeInfo.prizeClaimDeadline ?? "";
    DateTime providedEndTime = (prizeClaimDeadline.trim().isNotEmpty)
        ? DateTime.parse(prizeClaimDeadline)
        : DateTime.now();

    PrizeGetMethod? prizeGetMethod = prizeInfo.prizeGetMethod;
    if (prizeGetMethod != null) {
      return Container();
    }
    bool hasFee = (widget.challenge?.deliveryFee ?? 0) > 0;
    String deliveryFeeSubmitText = hasFee ? "邮寄(自费)" : "邮寄";

    return CommonCard(
        padding: EdgeInsets.only(top: 15, left: 10, right: 10),
        child: Container(
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                Text("请选择领奖方式",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: DColor.ff232323,
                      fontSize: 14,
                      fontFamily: "PF",
                    )),
                SizedBox(
                  height: 5,
                ),
                Text(
                    "请在${DateFormat('yyyy.MM.dd').format(providedEndTime)}前确认领奖方式\n若不选择，则视为自动放弃",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 14,
                      fontFamily: "PF",
                    )),
                SizedBox(
                  height: 5,
                ),
                Row(
                  children: [
                    // 左边按钮，占三分之一宽度
                    Visibility(
                        visible: prizeInfo.hasExpress,
                        child: Expanded(
                          flex: 2, // 1/3
                          child: SubmitButton(
                            minWidth: 70,
                            borderColor: DColor.primary,
                            backgroundColor: DColor.white,
                            textColor: DColor.primary,
                            text: deliveryFeeSubmitText,
                            onPressed: () async {
                              if (hasFee) {
                                // 检查是否已付费
                                await showLoadingDialogWithFuture(
                                        context,
                                        logic.checkPayDeliveryStatus(
                                            widget.challenge!.id ?? ""),
                                        barrierDismissible: false)
                                    .then((result) async {
                                  if (result.success ?? false) {
                                    final returnValue = await Get.to(
                                      PhysicalAwardExpressPageLuckDraw(
                                        challenge: widget.challenge!,
                                        physicalReward: prizeInfo,
                                        hasPayFee: result.result ?? false,
                                      ),
                                    );
                                    if (returnValue == true) {
                                      // 刷新页面数据
                                      logic.refreshData();
                                    }
                                  } else {
                                    final returnValue = await Get.to(
                                      PhysicalAwardExpressPageLuckDraw(
                                        challenge: widget.challenge!,
                                        physicalReward: prizeInfo,
                                      ),
                                    );
                                    if (returnValue == true) {
                                      // 刷新页面数据
                                      logic.refreshData();
                                    }
                                  }
                                });
                              } else {
                                final returnValue = await Get.to(
                                  PhysicalAwardExpressPageLuckDraw(
                                    challenge: widget.challenge!,
                                    physicalReward: prizeInfo,
                                  ),
                                );
                                if (returnValue == true) {
                                  // 刷新页面数据
                                  logic.refreshData();
                                }
                              }
                            },
                          ),
                        )),
                    SizedBox(width: 10),
                    // 右边按钮，占二分之一宽度
                    Visibility(
                        visible: prizeInfo.hasStore,
                        child: Expanded(
                          flex: 4, // 2/3
                          child: SubmitButton(
                              enable: true,
                              text: "到店领取",
                              onPressed: () async {
                                final returnValue = await Get.to(
                                    ChallengeChooseStorePage(
                                        type: "ChallengePrizePage",
                                        challenge: widget.challenge!,
                                        physicalReward: prizeInfo));
                                if (returnValue == true) {
                                  // 刷新页面数据
                                  logic.refreshData();
                                }
                                setState(() {});
                              }),
                        )),
                  ],
                ),
                SizedBox(
                  height: 10,
                ),
              ],
            )));
  }

  Widget _buildStoreInfo(BuildContext context) {
    return Obx(() {
      final record = logic.rewardDistributionRecord.value;

      // 数据加载中时
      if (record == null) {
        return Center();
      }

      // 门店信息为空时，返回空容器
      var shopInfo = record.storeVo?.shopInfo;
      if (shopInfo == null) {
        return SizedBox.shrink(); // 更标准的空占位写法
      }

      // 渲染门店信息
      return CommonCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("门店信息",
                style: TextStyle(
                  fontSize: 14,
                  color: DColor.ff232323,
                )),
            SizedBox(height: 14),
            StoreInfoView(context, shopInfo)
            // 注意：修正了 record.shopInfo 的取值
          ],
        ),
      );
    });
  }

  Widget _buildAddressInfo(BuildContext context) {
    return Obx(() {
      final record = logic.rewardDistributionRecord.value;

      // 数据加载中时
      if (record == null) {
        return Center();
      }

      // 选择的领取方式为到店是不显示地址
      var mailVo = record.mailVo;
      if (mailVo == null) {
        return SizedBox.shrink(); // 更标准的空占位写法
      }

      ShoppingAddress address = ShoppingAddress(
          address: mailVo.address,
          city: mailVo.city,
          mobile: mailVo.mobile,
          areaCode: "+86",
          postCode: mailVo.postCode,
          province: mailVo.province,
          shoppingName: mailVo.shoppingName,
          town: mailVo.town);

      // 渲染门店信息
      return CommonCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("收货信息",
                style: TextStyle(
                  fontSize: 14,
                  color: DColor.ff232323,
                )),
            SizedBox(height: 14),
            UserAddressView(address: address),
          ],
        ),
      );
    });
  }

  /**
   * 发放信息，领取时间及发放店员信息
   */
  Widget _buildDistributionInfo(BuildContext context) {
    return Obx(() {
      final record = logic.rewardDistributionRecord.value;

      // 数据加载中时
      if (record == null) {
        return Center();
      }

      // 门店信息为空时，返回空容器
      var storeInfo = record.storeVo;
      if (storeInfo == null) {
        return SizedBox.shrink(); // 更标准的空占位写法
      }

      DateTime distributionTime = storeInfo.distributionTime != null
          ? DateTime.parse(storeInfo.distributionTime ?? '')
          : DateTime.now();

      // 领取时间及发放工作人员
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 10,
          ),
          Container(
            height: 1,
            color: DColor.ff707070,
          ),
          SizedBox(
            height: 12,
          ),
          Text(
              "领取时间：${DateFormat('yyyy.MM.dd HH:mm:ss').format(distributionTime)}",
              style: TextStyle(fontSize: 12, color: DColor.ff232323)),
          SizedBox(height: 14),
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage:
                    NetworkImage(storeInfo.distributionFaceUrl ?? ''),
                onBackgroundImageError: (_, __) {
                  // 如果图片加载失败，使用默认图片
                },
                backgroundColor: Colors.grey[200],
              ),
              SizedBox(width: 10),
              // 用户信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      storeInfo.distributionNick ?? "用户名",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 5),
                    Text(
                      "粉丝 " + storeInfo.fansTotal.toString(),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),

              // TextButton(
              //   onPressed: () async {
              //     try {
              //       if (record.isFriend == 0 || record.isFriend == 3) {
              //         await FollowApi(MyHttp.dio)
              //             .queryFollow(record.distributionUid as int);
              //         var eventFollow =
              //             FollowCountUpdated(++Constants.followCount);
              //         BlocProvider.of<FollowBloc>(context).add(eventFollow);
              //         setState(() {
              //           if (record.isFriend == 3) {
              //             record.isFriend = 1;
              //           } else {
              //             record.isFriend = 2;
              //           }
              //         });
              //       } else {
              //         ModalPopUtils.showModalPop(context, (value) async {
              //           try {
              //             await FollowApi(MyHttp.dio)
              //                 .queryUnfollow(record.distributionUid as int);
              //             var eventFollow =
              //                 FollowCountUpdated(--Constants.followCount);
              //             BlocProvider.of<FollowBloc>(context).add(eventFollow);
              //             setState(() {
              //               if (record.isFriend == 1) {
              //                 record.isFriend = 3;
              //               } else {
              //                 record.isFriend = 0;
              //               }
              //             });
              //           } on DioException catch (e) {
              //             showToast(e.message.toString(), context: context);
              //           }
              //         }, modals: [
              //           PopModal(
              //               tag: "unfollow",
              //               content: "取消关注",
              //               color: DColor.ff242424),
              //         ]);
              //       }
              //     } on DioException catch (e) {
              //       try {
              //         showToast(e.message.toString(), context: context);
              //       } catch (e) {}
              //     }
              //   },
              //   style: ButtonStyle(
              //     textStyle: MaterialStateProperty.all(TextStyle(fontSize: 13)),
              //     minimumSize: MaterialStateProperty.all(Size(55, 25)),
              //     padding: MaterialStateProperty.all(
              //         EdgeInsets.symmetric(vertical: 3, horizontal: 8)),
              //     backgroundColor: MaterialStateProperty.resolveWith((states) {
              //       //设置按下时的背景颜色
              //       // if (states.contains(MaterialState.pressed)) {
              //       //   return Colors.green[200];
              //       // }
              //       return record.isFriend == 1 || record.isFriend == 2
              //           ? Colors.transparent
              //           : DColor.fffb691d;
              //     }),
              //     foregroundColor: MaterialStateProperty.all(
              //         record.isFriend == 1 || record.isFriend == 2
              //             ? DColor.hitText
              //             : Colors.white),
              //     side: MaterialStateProperty.all(BorderSide(
              //         color: record.isFriend == 1 || record.isFriend == 2
              //             ? DColor.hitText
              //             : DColor.fffb691d,
              //         width: 1)),
              //     //外边框装饰 会覆盖 side 配置的样式
              //     shape: MaterialStateProperty.all(StadiumBorder()),
              //   ),
              //   child: Text(record.isFriend == 2
              //       ? "已关注"
              //       : record.isFriend == 1
              //           ? "互相关注"
              //           : "关注"),
              // ),
            ],
          )
        ],
      );
    });
  }

  // 函数实现
  Widget _buildExpressPickupButton(bool isRewardReceived) {
    return Obx(() {
      final record = logic.rewardDistributionRecord.value;

      return Column(
        children: [
          MaterialButton(
            minWidth: 70,
            padding: EdgeInsets.symmetric(vertical: 9, horizontal: 8),
            onPressed: () {
              if (isRewardReceived) {
                _navigateToExpressDetails(record);
              }
            },
            shape: RoundedRectangleBorder(
              side: BorderSide(color: DColor.primary, width: 1),
              borderRadius: BorderRadius.circular(3.0),
            ),
            color: isRewardReceived ? DColor.primary : DColor.white,
            child: Text(
              isRewardReceived ? "查看物流信息" : "待发货",
              style: TextStyle(
                color: isRewardReceived ? DColor.white : DColor.primary,
                fontSize: 16,
              ),
            ),
          ),
          SizedBox(height: 5),
        ],
      );
    });
  }

// 导航逻辑封装
  void _navigateToExpressDetails(PrizeClaimForm? record) {
    var expressInfo = record?.mailVo;
    if (expressInfo == null) {
      return;
    }
    Get.to(
      ExpressDetailPage(
        companyName: expressInfo.postCompany ?? "",
        company: expressInfo.postCompanyCode ?? "",
        expressId: expressInfo.postNum ?? "",
        phone: expressInfo.mobile,
      ),
    );
  }
}
