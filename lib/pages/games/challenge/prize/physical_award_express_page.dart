import 'dart:async';

import 'package:alipay_kit/alipay_kit_platform_interface.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart'
    as GetTransition;
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/payment_order.dart';
import 'package:new_edge/model/shopping_address.dart';
import 'package:new_edge/pages/address/user_address_logic.dart';
import 'package:new_edge/pages/address/user_address_page.dart';
import 'package:new_edge/pages/address/user_address_widget.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/express/express_detail_logic.dart';
import 'package:new_edge/pages/games/challenge/award/physical/physical_award_express_payresult_page.dart';
import 'package:new_edge/pages/games/challenge/prize/challenge_prize_logic.dart';
import 'package:new_edge/pages/verifyidcard/verifyidcard_edit_page.dart';
import 'package:new_edge/picker/constants/extensions.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/pay/alipay_utils.dart';
import 'package:new_edge/util/pay/payment_method.dart';
import 'package:new_edge/util/pay/wechat_utils.dart';
import 'package:new_edge/util/toast.dart';
import 'package:new_edge/widgets/NormalWidget.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:wechat_kit/wechat_kit_platform_interface.dart';

import 'physical_award_express_logic.dart';

class PhysicalAwardExpressPageLuckDraw extends StatefulWidget {
  final ChallengeDetail challenge;

  final PrizeInfo physicalReward;
  final bool hasPayFee;

  PhysicalAwardExpressPageLuckDraw(
      {required this.challenge,
      required this.physicalReward,
      this.hasPayFee = false}) {}

  @override
  _PhysicalAwardExpressState createState() => _PhysicalAwardExpressState();
}

class _PhysicalAwardExpressState
    extends RefreshState<PhysicalAwardExpressPageLuckDraw>
    with WidgetsBindingObserver {
  final expressFeeLogic = Get.put(PhysicalAwardExpressLogicLuckDraw());

  final logic = Get.find<ChallengePrizeLogic>();
  final item = Get.find<ChallengePrizeLogic>().item;

  final expressItem = Get.find<ChallengePrizeLogic>().expressItem;

  final addressLogic = Get.findOrPut(() => UserAddressLogic());
  final expressLogic = Get.findOrPut(() => ExpressDetailLogic());

  late final StreamSubscription<WechatResp> _wechatSubs;
  late final StreamSubscription<AlipayResp> _alipaySubs;

  void _listenPay(AlipayResp resp) {
    print("alipay response: $resp");
    // _toPayResult(resp.isSuccessful);
  }

  void _listenResp(WechatResp resp) {
    print("wechat response: $resp");
    if (resp is WechatPayResp) {
      // _toPayResult(resp.isSuccessful);
    }
  }

  @override
  void initState() {
    super.initState();
    _wechatSubs = WechatKitPlatform.instance.respStream().listen(_listenResp);
    _alipaySubs = AlipayKitPlatform.instance.payResp().listen(_listenPay);

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    _wechatSubs.cancel();
    _alipaySubs.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkPayResult();
    }
  }

  void _submitOrder() async {
    showLoadingDialogWithFuture(
            context, expressFeeLogic.submit(widget.challenge.id ?? ""),
            barrierDismissible: false, delayTime: const Duration(seconds: 2))
        .then((result) async {
      if (result.success == true) {
        showToast('订单创建成功！', context);

        _orderPay();
      } else {
        showToast(result.message ?? '订单创建失败！', context);
      }
    }).catchError((error) {
      if (error is DioException) {
        showToast(error.message.toString(), context);
      }
    });
  }

  void _checkPayResult() async {
    if (!expressFeeLogic.isPaying.value) {
      return;
    }

    expressFeeLogic.isPaying.value = false;
    showLoadingDialogWithFuture(context, expressFeeLogic.queryByOrderNumber(),
            delayTime: const Duration(seconds: 2), barrierDismissible: false)
        .then((result) async {
      PaymentOrder? order = result.result;
      if (result.success == true && order?.paymentStatus == 2) {
        _toPayResult(true);
      } else {
        _toPayResult(false);
      }
    }).catchError((error) {
      if (error is DioException) {
        showToast(error.message.toString(), context);
      }
      _toPayResult(false);
    });
  }

  void _toPayResult(bool result) {
    if (result) {
      _addAwardRecord();
    } else {
      Get.to(
          PhysicalAwardExpressPayResultPage(
              id: widget.challenge.id ?? "", isSuccess: result),
          transition: Transition.rightToLeft);
    }
  }

  void _orderPay() async {
    if (expressFeeLogic.paymentMethod.value == PaymentMethod.Weixin) {
      if (!await WechatUtils.isInstalled()) {
        showToast("请先安装微信", context);
        return;
      }
      showLoadingDialogWithFuture(context, expressFeeLogic.payWechat(),
              barrierDismissible: false)
          .then((result) async {
        if (result.success == true) {
          expressFeeLogic.isPaying.value = true;
          await WechatUtils.pay(result.result!);
        } else {
          expressFeeLogic.isPaying.value = false;
          showToast(result.message ?? '订单支付失败！', context);
        }
      }).catchError((error) {
        if (error is DioException) {
          showToast(error.message.toString(), context);
        }
        expressFeeLogic.isPaying.value = false;
      });
    } else if (expressFeeLogic.paymentMethod.value == PaymentMethod.Alipay) {
      if (!await AlipayUtils.isInstalled()) {
        showToast("请先安装支付宝", context);
        return;
      }
      showLoadingDialogWithFuture(context, expressFeeLogic.payAli(),
              barrierDismissible: false)
          .then((result) async {
        if (result.success == true) {
          expressFeeLogic.isPaying.value = true;
          await AlipayUtils.pay(result.result ?? "");
        } else {
          expressFeeLogic.isPaying.value = false;
          showToast(result.message ?? '订单支付失败！', context);
        }
      }).catchError((error) {
        if (error is DioException) {
          showToast(error.message.toString(), context);
        }
        expressFeeLogic.isPaying.value = false;
      });
    }
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return logic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff232323,
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                Navigator.pop(context, false);
              },
            ),
            title: Text("确认收货信息",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: Obx(() => _buildContent(context)));
  }

  Widget _buildContent(BuildContext context) {
    bool isProvided = (widget.physicalReward.isProvided ?? 0) == 1;

    return Column(
      children: [
        Expanded(child: _buildDetail(context)),
        Visibility(
            visible: !isProvided,
            child: BoxShadowContent(child: _buildExpressSubmitButton())),
      ],
    );
  }

  Widget _buildExpressSubmitButton() {
    bool hasFee = (widget.challenge.deliveryFee ?? 0) > 0 && !widget.hasPayFee;
    String submitText = hasFee ? "确认支付并领奖" : "确认领取";
    return SubmitButton(
        text: submitText,
        enable: true,
        onPressed: () {
          showLoadingDialogWithFuture(
            context,
            logic.checkVerifyName(),
          ).then((result) async {
            if (result) {
              showNormalDialog(
                () {
                  if (hasFee) {
                    _submitOrder();
                  } else {
                    _addAwardRecord();
                  }
                },
                title: "温馨提示",
                content: "确认领取后，无法修改收货信息，\n 请确认无误后再点击领取。",
                confirmText: "确认领取",
              );
            } else {
              showNormalDialog(
                () async {
                  await Get.to(VerifyIdCardEditPage(),
                      transition: GetTransition.Transition.rightToLeft);
                },
                title: "温馨提示",
                content: "请先进行实名认证后，再领奖",
                confirmText: "去认证",
              );
            }
          });
        });
  }

  void _addAwardRecord() async {
    await showLoadingDialogWithFuture(
            context,
            logic.addPhysicalRewardAddressRecord(widget.challenge,
                widget.physicalReward, addressLogic.remoteAddress.value),
            barrierDismissible: false)
        .then((result) async {
      if (result?.success ?? false) {
        // 成功领取后返回上一个页面，并附带结果
        Get.back(result: true);
      } else {
        showToast('领取失败！', context);
      }
    });
  }

  @override
  bool isAutoRefresh() {
    return widget.physicalReward.prizeClaimFormId != null;
  }

  Widget _buildDetail(BuildContext context) {
    bool isProvided = widget.physicalReward.isProvided;

    ShoppingAddress? shoppingAddress;
    if (isProvided) {
      shoppingAddress = logic.getShoppingAddress();
    } else {
      shoppingAddress = addressLogic.isRemoteValid()
          ? addressLogic.remoteAddress.value
          : null;
    }
    bool hasFee = (widget.challenge.deliveryFee ?? 0) > 0 && !widget.hasPayFee;

    return Column(
      children: [
        Container(
            padding: EdgeInsets.only(left: 10, right: 10, top: 4),
            child: Column(
              children: [
                _buildShippingAddress(shoppingAddress),
                Visibility(visible: hasFee, child: _buildExpressFee()),
                Visibility(visible: hasFee, child: _buildPaymentInfo())
              ],
            )),
      ],
    );
  }

  Widget _buildShippingAddress(ShoppingAddress? address) {
    bool isProvided = (widget.physicalReward.isProvided ?? 0) == 1;
    bool isShowModify = (address?.isValid() ?? false) && !isProvided;
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(child: Text("收货信息", style: TextStyle(fontSize: 14))),
            GestureDetector(
              onTap: () async {
                await Get.to(UserAddressPage(),
                    transition: GetTransition.Transition.rightToLeft);
                setState(() {});
              },
              child: Visibility(
                  visible: isShowModify,
                  child: Row(children: [
                    Image.asset(
                        height: 15, DImages.formatPathPng("icon_modify")),
                    SizedBox(width: 5),
                    Text('修改',
                        style:
                            TextStyle(color: Color(0xFF999999), fontSize: 13)),
                  ])),
            )
          ],
        ),
        SizedBox(height: 14),
        UserAddressView(address: address),
      ],
    ));
  }

  Widget _buildExpressFee() {
    return CommonCard(
        child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
            child: Text(
          "邮费",
          style: TextStyle(color: DColor.ff232323, fontSize: 14),
        )),
        Text(
          "¥${widget.challenge.deliveryFee ?? 0}",
          style: TextStyle(
              color: DColor.primary, fontFamily: DFamily.dinBold, fontSize: 19),
        )
      ],
    ));
  }

  Widget _buildPaymentInfo() {
    return CommonCard(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "支付方式",
          style: TextStyle(color: Color(0xFF666666), fontSize: 14),
        ),
        SizedBox(height: 20),
        _buildPaymentItem(PaymentMethod.Weixin),
        SizedBox(height: 20),
        _buildPaymentItem(PaymentMethod.Alipay)
      ],
    ));
  }

  Widget _buildPaymentItem(PaymentMethod method) {
    bool isSelected = method == expressFeeLogic.paymentMethod.value;
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          expressFeeLogic.setPaymentMethod(method);
        },
        child: Row(children: [
          Image.asset(height: 19, DImages.formatPathPng(method.iconName)),
          SizedBox(width: 8),
          Text(method.title,
              style: TextStyle(color: Color(0xFF999999), fontSize: 13)),
          Expanded(child: Container()),
          Image.asset(
            // ignore: unrelated_type_equality_checks
            isSelected
                ? DImages.formatPathPng('selected')
                : DImages.formatPathPng('no_selected'),
            height: 15,
            width: 15,
            fit: BoxFit.cover,
          )
        ]));
  }
}
