import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/io/pay.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/model/activities_discount_code.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/payment_order.dart';
import 'package:new_edge/model/wx_pay.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/pay/payment_method.dart';

class PhysicalAwardExpressLogicLuckDraw extends GetxController {

  final orderNumber = "".obs;
  final isPaying = false.obs;

  final Rx<ActivitiesDiscountCode?> discountCode =
      Rx<ActivitiesDiscountCode?>(null);
  final paymentMethod = PaymentMethod.Weixin.obs;

  void setPaymentMethod(PaymentMethod method) {
    paymentMethod.value = method;
  }

  Future<ApiResponse<String>> preResponse() async {
    ApiResponse<String> result = ApiResponse<String>();
    result.code = 0;
    result.result = orderNumber.value;
    result.success = true;
    return result;
  }

  Future<ApiResponse<String>> submit(String challengeId) {
    return _payDeliveryFee(challengeId).then((value) {
      if (value.success == true) {
        orderNumber.value = value.result ?? "";
      }
      return value;
    });
  }

  Future<ApiResponse<WxPay>> payWechat() {
    return PayApi(MyHttp.dio).wxPay(
        orderNumber: orderNumber.value,
        orderType: 7,
        uid: Account.loginAccount?.uid ?? 0);
  }

  Future<ApiResponse<String>> payAli() {
    return PayApi(MyHttp.dio).aliPay(
        orderNumber: orderNumber.value,
        orderType: 7,
        uid: Account.loginAccount?.uid ?? 0);
  }

  Future<ApiResponse<PaymentOrder>> queryByOrderNumber() {
    return ActiviesApi(MyHttp.dio)
        .queryByOrderNumber(orderNumber: orderNumber.value);
  }

  Future<ApiResponse<String>> _payDeliveryFee(String challengeId) {
    return ActiviesApi(MyHttp.dio).payDeliveryFee(
        challengeId: challengeId);
  }

  void clearCache() {
    orderNumber.value = "";
    isPaying.value = false;
  }
}
