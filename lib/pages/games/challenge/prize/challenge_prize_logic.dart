import 'package:get/get.dart';
import 'package:new_edge/io/activies.dart';
import 'package:new_edge/io/user.dart';
import 'package:new_edge/io/util.dart';
import 'package:new_edge/model/api_response/api_edge_response_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/challenge_lucky_drawa.dart';
import 'package:new_edge/model/express_detail.dart';
import 'package:new_edge/model/prize_claim_form.dart';
import 'package:new_edge/model/reward_distribution_record.dart';
import 'package:new_edge/model/shopping_address.dart';
import 'package:new_edge/model/verify_name_result.dart';
import 'package:new_edge/pages/base/simple_controller.dart';
import 'package:new_edge/request/MyHttp.dart';

class ChallengePrizeLogic extends SimpleController<
    ApiResponse<ChallengeLuckyDrawa>, ChallengeLuckyDrawa> {
  final String challengeId;
  final String luckyDrawaId;
  final rewardDistributionRecord = Rx<PrizeClaimForm?>(null);
  var item = PrizeClaimForm().obs;
  var expressItem = ExpressDetail().obs;

  ChallengePrizeLogic(this.challengeId, this.luckyDrawaId) {}

  @override
  ChallengeLuckyDrawa? getValue(ApiResponse<ChallengeLuckyDrawa>? m) {
    return m?.result;
  }

  @override
  Future<ApiResponse<ChallengeLuckyDrawa>> loadData() {
    return ActiviesApi(MyHttp.dio)
        .queryDrawaDetailByChallengeId(challengeId: challengeId)
        .then((response) {
      // 更新数据
      data.value = response.result;

      // 输出指定字段的值
      final prizeClaimFormId =
          response.result?.prizeList.firstOrNull?.prizeClaimFormId;

      print("prizeClaimFormId: $prizeClaimFormId");
      _loadDistributionRecord(prizeClaimFormId!);
      return response;
    });
  }

  Future<ApiResponse<PrizeInfo>> submitPrizeClaim() {
    return ActiviesApi(MyHttp.dio)
        .clickToDraw(luckyDrawaId: luckyDrawaId)
        .then((value) {
      if (value.success == true) {
        if (data.value?.prizeList != null) {
          data.value?.prizeList.add(value.result!);
        }
      }
      return value;
    });
  }

  Future<ApiResponse<bool>> checkPayDeliveryStatus(String id) {
    return ActiviesApi(MyHttp.dio).checkPayDeliveryStatus(challengeId: id);
  }

  Future<ApiResponse<PrizeClaimForm>> _loadDistributionRecord(
      String prizeClaimFormId) {
    return ActiviesApi(MyHttp.dio)
        .getRewardDistributionRecords(prizeClaimFormId: prizeClaimFormId)
        .then((response) async {
      // 将获取到的数据更新到响应式变量
      rewardDistributionRecord.value = response.result;
      var result = response.result;
      if (result != null) {
        item.value = result;

        //请求快递信息
        if (result.mailVo?.postCode != null) {
          try {
            ExpressDetail responseExpress = await _loadExpressData(
                result.mailVo!.postCompanyCode!,
                result.mailVo!.postNum!,
                result.mailVo!.mobile);
            expressItem.value = responseExpress;
          } catch (error) {
            print("出错 $error");
          }
        }
      }

      return response;
    });
  }

  Future<bool> checkVerifyName() async {
    try {
      return (await _hasRealNameInfo4Mobile()).info?.hasRealNameInfo ?? false;
    } catch (error) {
      print("出错 $error");
    }
    return false;
  }

  Future<ApiEdgeResponse<VerifyNameResult>> _hasRealNameInfo4Mobile() {
    return UserApi(MyHttp.dio).hasRealNameInfo();
  }

  Future<ExpressDetail> _loadExpressData(
      String deliveryId, String num, String? phone) {
    return UtilApi(MyHttp.dio)
        .getTrackList(com: deliveryId, num: num, mobile: phone);
  }

  Future<ApiResponse<PrizeClaimForm>?> addPhysicalRewardAddressRecord(
      ChallengeDetail challenge,
      PrizeInfo physicalReward,
      ShoppingAddress address) {
    return ActiviesApi(MyHttp.dio)
        .addRewardDistributionRecords(
            data: RewardDistributionRecord(
                prizeClaimFormId: physicalReward.prizeClaimFormId!,
                address: address.address,
                city: address.city,
                mobile: address.mobile,
                shoppingName: address.shoppingName,
                postCode: address.postCode,
                province: address.province,
                town: address.town,
                distributionMethod: 1))
        .then((value) {
      if (value.success == true) {
        item.value = value.result!;
      }
      return value;
    });
  }

  ShoppingAddress? getShoppingAddress() {
    if (item.value.id == null || item.value.mailVo == null) {
      return null;
    }
    return ShoppingAddress(
        address: item.value.mailVo?.address,
        city: item.value.mailVo?.city,
        mobile: item.value.mailVo?.mobile,
        areaCode: "+86",
        postCode: item.value.mailVo?.postCode,
        province: item.value.mailVo?.province,
        shoppingName: item.value.mailVo?.shoppingName,
        town: item.value.mailVo?.town);
  }
}
