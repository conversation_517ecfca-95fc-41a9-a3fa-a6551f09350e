import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/challenge_info.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/challenge_item_view.dart';
import 'package:new_edge/pages/games/challenge/ridebrief/challenge_choose_confirm_logic.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:oktoast/oktoast.dart';

Future showChallengeChooseConfirmBottomSheet(
    BuildContext context,
    int postRideId,
    ) {
  return showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (BuildContext context) {
      return Container(
        padding: EdgeInsets.only(left: 0, right: 0),
        child: Container(
          color: Colors.white,
          constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7),
          child: ChallengeChooseConfirmPage(
            id: postRideId,
          ),
        ),
      );
    },
  );
}

class ChallengeChooseConfirmPage extends StatefulWidget {
  final int id;

  ChallengeChooseConfirmPage({required this.id}) {}

  @override
  _ChallengeChooseConfirmState createState() => _ChallengeChooseConfirmState();
}

class _ChallengeChooseConfirmState
    extends RefreshState<ChallengeChooseConfirmPage> {
  late ChallengeChooseConfirmLogic listLogic;

  late RxList<ChallengeInfo> listState;

  String? challengeId;

  @override
  void initState() {
    listLogic = Get.put(ChallengeChooseConfirmLogic(id: widget.id));
    listState = listLogic.list;
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<ChallengeChooseConfirmLogic>();
    super.dispose();
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return listLogic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => buildRefreshWidget(
        builder: () => listState.isNotEmpty
            ? Column(children: [Expanded(child: _buildList()), _buildSubmit()])
            : _buildEmpty(),
        onRefresh: () {
          listLogic.refreshData();
        },
        enablePullUp: false,
        refreshController: refreshController));
  }

  Widget _buildSubmit() {
    return Padding(
      padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
      child: Row(children: [
        Expanded(
          flex: 1,
          child: SubmitButton(
              backgroundColor: Colors.white,
              borderColor: DColor.primary,
              textColor: DColor.primary,
              text: "再想想",
              onPressed: () async {
                Navigator.pop(context, false);
              }),
        ),
        SizedBox(width: 10),
        Expanded(
          flex: 2,
          child: SubmitButton(
              enable: challengeId != null,
              text: "确认绑定",
              onPressed: () async {
                showLoadingDialogWithFuture(
                    context, listLogic.changeChallenges(challengeId ?? ""))
                    .then((result) async {
                  if (result.success ?? false) {
                    Navigator.pop(context, true);
                  }
                  showToast(result.message ?? "");
                }).catchError((error) {});
              }),
        ),
      ]),
    );
  }

  Widget _buildEmpty() {
    return Column(children: [
      Visibility(
          visible: listLogic.loadStatus.value == FitLoadStatus.loadSuccess,
          child: Padding(
              padding: EdgeInsets.only(top: 10, bottom: 10),
              child: Center(
                  child: Text("没有可更换的挑战",
                      style:
                      TextStyle(color: Color(0xFF232323), fontSize: 17))))),
      buildLoadStateWidgetFullEmpty(listLogic.loadStatus.value, () {
        refreshController.requestRefresh();
      }, "bg_challenge_empty")
    ]);
  }

  Widget _buildList() {
    return Container(
        padding: EdgeInsets.only(left: 10, right: 10, top: 15),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Padding(
                  padding: EdgeInsets.only(bottom: 10),
                  child: Center(
                      child: Text("请选择要绑定的挑战",
                          style: TextStyle(
                              color: Color(0xFF232323), fontSize: 17)))),
            ),
            SliverList(
              delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
                ChallengeInfo item = listState[index];
                return ChallengeItem(
                  item: item,
                  onTap: () {
                    setState(() {
                      challengeId = item.id;
                    });
                  },
                  isSelected: challengeId == item.id,
                );
              }, childCount: listState.length),
            ),
          ],
        ));
  }
}
