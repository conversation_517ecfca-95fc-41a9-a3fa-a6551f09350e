import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/family.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/ride_brief.dart';
import 'package:new_edge/pages/common/refresh_page.dart';
import 'package:new_edge/pages/games/challenge/ridebrief/challenge_choose_confirm_page.dart';
import 'package:new_edge/pages/games/challenge/ridebrief/challenge_ride_brief_logic.dart';
import 'package:new_edge/pages/ride/ride_detail.dart';
import 'package:new_edge/util/loading.dart';
import 'package:new_edge/util/string_format_utils.dart';
import 'package:new_edge/widgets/common_card.dart';
import 'package:new_edge/widgets/load_state.dart';
import 'package:new_edge/widgets/refresh_widget.dart';
import 'package:new_edge/widgets/submit_button.dart';

class ChallengeRideBriefPage extends StatefulWidget {
  final String id;
  final bool isComplete;

  ChallengeRideBriefPage({required this.id, this.isComplete = false}) {}

  @override
  _ChallengeRideBriefState createState() => _ChallengeRideBriefState();
}

class _ChallengeRideBriefState extends RefreshState<ChallengeRideBriefPage> {
  bool isSelectedMode = false;
  int? postRideId;
  late ChallengeRideBriefLogic listLogic;

  late RxList<RideBrief> listState;

  @override
  void initState() {
    listLogic = Get.put(ChallengeRideBriefLogic(id: widget.id));
    listState = listLogic.list;
    super.initState();
  }

  @override
  Rx<FitLoadStatus> getLoadState() {
    return listLogic.loadStatus;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
            centerTitle: true,
            leading: IconButton(
              icon: Image.asset(
                DImages.formatPathPng(
                  'jiantou_left_white',
                ),
                height: 17,
                width: 17,
                color: DColor.ff232323,
              ),
              onPressed: () {
                FocusScope.of(context).requestFocus(FocusNode()); //失去焦点
                Navigator.pop(context, false);
              },
            ),
            title: Text("骑行数据·已记入",
                style: TextStyle(color: DColor.ff242424, fontSize: 18))),
        body: Obx(() => buildRefreshWidget(
            builder: () => listState.isNotEmpty
                ? Column(
                    children: [Expanded(child: _buildList()), _buildSubmit()])
                : buildLoadStateWidget(listLogic.loadStatus.value, () {
                    refreshController.requestRefresh();
                  }, emptyTip: "暂时没有骑行数据"),
            onRefresh: () {
              listLogic.refreshData();
            },
            enablePullUp: false,
            refreshController: refreshController)));
  }

  Widget _buildSubmit() {
    if (widget.isComplete) {
      return Container();
    }
    if (!isSelectedMode) {
      return BoxShadowContent(
          child: SubmitButton(
              text: "选定骑行记录绑定至其他挑战",
              onPressed: () async {
                setState(() {
                  if (postRideId == null && listState.isNotEmpty) {
                    postRideId = listState.first.postRideId;
                  }
                  isSelectedMode = true;
                });
              }));
    }

    return BoxShadowContent(
      child: Row(children: [
        Expanded(
          flex: 1,
          child: SubmitButton(
              backgroundColor: Colors.white,
              borderColor: DColor.primary,
              textColor: DColor.primary,
              text: "再想想",
              onPressed: () async {
                setState(() {
                  isSelectedMode = false;
                });
              }),
        ),
        SizedBox(width: 10),
        Expanded(
          flex: 2,
          child: SubmitButton(
              enable: postRideId != null,
              text: "绑定至其他挑战",
              onPressed: () async {
                bool result = await showChallengeChooseConfirmBottomSheet(context, postRideId ?? 0)??false;
                if (result) {
                  refreshController.requestRefresh();
                }
              }),
        ),
      ]),
    );
  }

  Widget _buildList() {
    return Container(
        padding: EdgeInsets.only(left: 10, right: 10, top: 15),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: Center(
                  child: RichText(
                text: TextSpan(
                  children: <TextSpan>[
                    TextSpan(
                      text: '本项挑战共记入',
                      style: TextStyle(
                        color: DColor.ff808080,
                        fontSize: 14,
                      ),
                    ),
                    TextSpan(
                      text: ' ${listState.length} ',
                      style: TextStyle(
                        color: DColor.ff808080,
                        fontSize: 14,
                      ),
                    ),
                    TextSpan(
                        text: '条骑行数据',
                        style: TextStyle(color: DColor.ff808080, fontSize: 14)),
                  ],
                ),
              )),
            ),
            SliverList(
              delegate:
                  SliverChildBuilderDelegate((BuildContext context, int index) {
                RideBrief item = listState[index];
                return GestureDetector(
                    onTap: () {
                      if (isSelectedMode) {
                        setState(() {
                          postRideId = item.postRideId;
                        });
                      } else {
                        Get.to(RideDetailPage(postRideId: item.postRideId),
                            transition: Transition.rightToLeft);
                      }
                    },
                    child: buildRideBriefItem(item,
                        isSelected:
                            isSelectedMode && postRideId == item.postRideId));
              }, childCount: listState.length),
            ),
          ],
        ));
  }
}

Widget buildRideBriefItem(RideBrief item, {bool isSelected = false}) {
  return CommonCard(
      sideColor: isSelected ? DColor.primary : DColor.transparent,
      padding: EdgeInsets.only(left: 5, top: 5, bottom: 5, right: 15),
      child: Row(children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(5),
          child: Container(
            height: 85,
            width: 125,
            child: (item.coverMapImg ?? "").length > 0 && item.type == 0
                ? CachedNetworkImage(imageUrl: item.coverMapImg!)
                : Image.asset(
                    item.type == null || item.type == 0
                        ? "assets/images/img_placeholder_bg.png"
                        : "assets/images/img_indoor_placeholder.png",
                    fit: BoxFit.cover,
                  ),
          ),
        ),
        SizedBox(
          width: 10,
        ),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Expanded(
                    child: Text(
                  formatDateForBriefTime(item.startTime ?? 0),
                  style: TextStyle(
                      fontSize: 14,
                      color: DColor.ff242424,
                      fontWeight: FontWeight.normal),
                )),
                Visibility(
                    visible: isSelected,
                    child: Image.asset(
                      DImages.formatPathPng('selected'),
                      height: 15,
                      width: 15,
                      fit: BoxFit.cover,
                    ))
              ]),
              SizedBox(
                height: 30,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildData("", formatMToKm(item.meter) ?? "", "km"),
                  _buildData("", item.climb?.toString() ?? "0", "m"),
                  _buildData("", formatSecToMin(item.second) ?? "", "min"),
                ],
              ),
            ],
          ),
        )
      ]));
}

Widget _buildData(String title, String value, String unit) {
  return Text.rich(
    TextSpan(
      children: [
        TextSpan(
          text: title,
          style: TextStyle(fontSize: 12, color: DColor.ff808080),
        ),
        TextSpan(
          text: " $value",
          style: TextStyle(
              fontSize: 20,
              color: DColor.ff242424,
              fontFamily: "DIN",
              fontWeight: FontWeight.normal),
        ),
        TextSpan(
          text: " $unit",
          style: TextStyle(
              fontSize: 14,
              color: DColor.ff242424,
              fontFamily: "DIN",
              fontWeight: FontWeight.normal),
        ),
      ],
    ),
  );
}
