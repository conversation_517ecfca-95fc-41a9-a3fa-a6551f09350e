import 'package:new_edge/io/activies.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/challenge_info.dart';
import 'package:new_edge/pages/base/list_controller.dart';
import 'package:new_edge/request/MyHttp.dart';

class ChallengeChooseConfirmLogic extends ListController<ChallengeInfo> {
  final int id;

  ChallengeChooseConfirmLogic({required this.id}) {}

  @override
  Future<ApiResponse<List<ChallengeInfo>>> loadData() {
    return ActiviesApi(MyHttp.dio).queryViableChallenges(postRideId: id);
  }

  Future<ApiResponse<String>> changeChallenges(String newChallengeId) {
    return ActiviesApi(MyHttp.dio)
        .changeChallenges(newChallengeId: newChallengeId, postRideId: id);
  }
}
