import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/config/image_path.dart';
import 'package:new_edge/model/app_update.dart';
import 'package:new_edge/widgets/submit_button.dart';
import 'package:ota_update/ota_update.dart';
import 'package:url_launcher/url_launcher.dart';

class AppUpgradeDialog extends StatefulWidget {
  final AppUpdate appUpdate;

  AppUpgradeDialog({required this.appUpdate}) {}

  @override
  _AppUpgradeDialogState createState() => _AppUpgradeDialogState();
}

class _AppUpgradeDialogState extends State<AppUpgradeDialog> {
  OtaEvent? _currentEvent = null;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        padding: EdgeInsets.fromLTRB(10, 32, 10, 20),
        child: SingleChildScrollView(
          child: _buildContent(),
        ),
      ),
    );
  }

  Widget _buildContent() {
    bool isShowDownload = _currentEvent?.status == OtaStatus.DOWNLOADING;
    return Column(
      children: [
        _buildUpdateInfo(),
        SizedBox(height: 55),
        Visibility(visible: !isShowDownload, child: _buildActionButton()),
        Visibility(visible: isShowDownload, child: _buildDownloading())
      ],
    );
  }

  Widget _buildUpdateInfo() {
    return Container(
        width: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(DImages.formatPathPng("bg_update")), // 使用本地资源图片
            fit: BoxFit.cover, // 调整图片以覆盖整个容器
          ),
        ),
        child: Column(
          children: [
            Text(
              "发现新版本",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            Text(
              "${widget.appUpdate.versionName}",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 30),
            Text(
              "${widget.appUpdate.updateContent}",
              style: TextStyle(fontSize: 14),
            ),
          ],
        ));
  }

  Widget _buildActionButton() {
    bool isUpdate = _currentEvent?.status == OtaStatus.INSTALLING;

    return Column(
      children: [
        SizedBox(
            width: 300,
            child: SubmitButton(
                text: "立即更新",
                onPressed: () {
                  if (defaultTargetPlatform == TargetPlatform.android) {
                    _downloadApp();
                  } else if (defaultTargetPlatform == TargetPlatform.iOS) {
                    launchUrl(Uri.parse(widget.appUpdate.iosUrl ??
                        'https://itunes.apple.com/app/id1526283421?action=write-review'));
                    Navigator.pop(context);
                  }
                })),
        SizedBox(height: 16),
        GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Text(
              "跳过",
              style: TextStyle(fontSize: 13, color: DColor.ff808080),
            )),
      ],
    );
  }

  Widget _buildDownloading() {
    double progress = 0;
    if (_currentEvent != null) {
      if (_currentEvent?.status == OtaStatus.DOWNLOADING) {
        try {
          progress = double.parse(_currentEvent?.value ?? "") / 100;
        } catch (e) {
          print("无法将字符串转换为数字: $e");
        }
      }
    }
    return Column(
      children: [
        Center(
            child: ClipRRect(
                borderRadius: BorderRadius.circular(8.0), // 设置圆角半径
                child: LinearProgressIndicator(
                  value: progress,
                  // 设置进度值
                  backgroundColor: Colors.grey[300],
                  // 设置背景颜色
                  valueColor: AlwaysStoppedAnimation<Color>(DColor.primary),
                  // 设置进度颜色
                  minHeight: 8.0, // 设置最小高度
                ))),
        SizedBox(height: 16),
        Text(
          "下载中…",
          style: TextStyle(fontSize: 13, color: DColor.ff808080),
        ),
      ],
    );
  }

  void _downloadApp() {
    try {
      //LINK CONTAINS APK OF FLUTTER HELLO WORLD FROM FLUTTER SDK EXAMPLES
      OtaUpdate()
          .execute(
        widget.appUpdate.apkDownloadUrl ?? "",
        // OPTIONAL
        destinationFilename: 'edge.apk',
      )
          .listen(
        (OtaEvent event) {
          setState(() => _currentEvent = event);
        },
      );
    } catch (e) {
      print('Failed to make OTA update. Details: $e');
    }
  }
}
