import 'dart:collection';

import 'package:flutter/services.dart';
import 'package:new_edge/model/pay_info.dart';

class FlutterMixPay {
  static var _channel = const MethodChannel('flutter_mixpay');
  static Future pay(String? payType,PayInfo payInfo) async {
    var map = HashMap();
    //wechat
    map['partnerid'] = payInfo.partnerId; //商户号
    map['prepayid'] = payInfo.prepayId; //支付交易会话id
    map['noncestr'] = payInfo.nonceStr; //随机字符串
    map['timeStamp'] = payInfo.timeStamp; //发送时间戳
    map['sign'] = payInfo.sign; //签名串
    map['appId'] = payInfo.appid; //appid
    map['payType'] =payType;//ALIPAY_TYPE   WECHATPAY_TYPE
    // //alipay
    map['payData']=payInfo.orderString;
    return await _channel?.invokeMethod("pay", map);
  }
}

