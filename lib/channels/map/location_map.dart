import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

import '../channels_config.dart';
import '../common_channel.dart';

class LocationMap extends StatefulWidget {
  LocationMap({Key? key}) : super(key: key);

  @override
  _LocationMapState createState() => _LocationMapState();
}

class _LocationMapState extends State<LocationMap> {
  @override
  void initState() {
  loadGPSPermissions();
  // PermissionHandler().requestPermissions([PermissionGroup.locationAlways]);
    super.initState();
  }

  Future<void> loadGPSPermissions()async {
    await CommonChannel.loadGPSPermissions();
  }

  @override
  Widget build(BuildContext context) {
    if (defaultTargetPlatform == TargetPlatform.android) {
      return AndroidView(
          viewType: "com.edge.cycling/LocationMap",
          creationParams: <String, dynamic>{
            ChannelConfigs.SelectMapTypeKey: ChannelConfigs.mapTypeKey
          },
          onPlatformViewCreated: _onPlatformViewCreated,
          creationParamsCodec: const StandardMessageCodec());
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      return UiKitView(
          viewType: "com.edge.cycling/LocationMap",
          creationParams: <String, dynamic>{
            ChannelConfigs.SelectMapTypeKey: ChannelConfigs.mapTypeKey
          },
          onPlatformViewCreated: _onPlatformViewCreated,
          creationParamsCodec: const StandardMessageCodec());
    } else {
      return Text("Not supported");
    }
  }

  Future<void> _onPlatformViewCreated(int id) async {}
}
