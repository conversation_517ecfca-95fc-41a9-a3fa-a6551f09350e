

import 'dart:io';

final WEEKDAY_TEXT_MAP = const {
  '1': '一',
  '2': '二',
  '3': '三',
  '4': '四',
  '5': '五',
  '6': '六',
  '7': '日'
};

final String PrivacyAgreementAndroidUrl = "https://www.edgecycling.cn/PrivacyPolicyAndroid.html";
final String PrivacyAgreementIOSUrl = "https://www.edgecycling.cn/PrivacyPolicyiOS.html";
final String UserAgreementAndroidUrl = "https://www.edgecycling.cn/UserAgreementAndroid.html";
final String UserAgreementIOSUrl = "https://www.edgecycling.cn/UserAgreementiOS.html";
final String VerifyAgreementAndroidUrl = "https://www.edgecycling.cn/RealNameAuthenticationAndroid.html";
final String VerifyAgreementIOSUrl = "https://www.edgecycling.cn/RealNameAuthenticationiOS.html";

//******************设置教程*******************//
final String oppo_url = "https://joyrun-web-cdn.thejoyrun.com/riding/oppo/oppo.html";
final String vivo_url = "https://joyrun-web-cdn.thejoyrun.com/riding/vivo/vivo.html";
final String xiaomi_url = "https://joyrun-web-cdn.thejoyrun.com/riding/%E5%B0%8F%E7%B1%B3/%E5%B0%8F%E7%B1%B3.html";
final String huawei_url = "https://joyrun-web-cdn.thejoyrun.com/riding/%E5%8D%8E%E4%B8%BA/%E5%8D%8E%E4%B8%BA.html";
final String other_url = "https://joyrun-web-cdn.thejoyrun.com/riding/%E5%85%B6%E4%BB%96/%E5%85%B6%E4%BB%96.html";

//高德mapkey
final GAODE_KEY = "450190d9153bd4c9ffc1c7a93dea82b5";
// final MAPBOX_KEY = "pk.eyJ1IjoiamlhbmxlZXBiIiwiYSI6ImNrcHhnMnZzMzFreDgydnM0ODdxeHM4N2MifQ.uNUdOVgwYlxFi8yUictnpg";
//final MAPBOX_KEY = "**********************************************************************************************";
final MAPBOX_KEY = "pk.eyJ1IjoiZWRnZWN5Y2xpbmciLCJhIjoiY2w3c2c5eGlwMG96azN0bWtsNjhnZmU5MiJ9.YZrY_THs6uc6JaL7Jq20PA";

final SHOWN_RIDE_CHART_SACLE_TIP = "SHOWN_RIDE_CHART_SACLE_TIP";

final int ALIPAY = 0; //支付宝
final int WECHATPAY = 1; //微信
final int PAY_SUCCESS = 0; //支付成功
final int PAY_ERROR = -1; //支付失败
final int PAY_CANCEL = -2; //支付关闭
final String ALIPAY_TYPE = "ALIPAY"; //支付宝
final String WECHATPAY_TYPE = "WEIXIN"; //微信


String getPrivacyAgreementUrl() {
  if (Platform.isAndroid) {
    return PrivacyAgreementAndroidUrl;
  } else if (Platform.isIOS) {
    return PrivacyAgreementIOSUrl;
  }
  return PrivacyAgreementAndroidUrl;
}

String getUserAgreementUrl() {
  if (Platform.isAndroid) {
    return UserAgreementAndroidUrl;
  } else if (Platform.isIOS) {
    return UserAgreementIOSUrl;
  }
  return UserAgreementAndroidUrl;
}

String getVerifyAgreementUrl() {
  if (Platform.isAndroid) {
    return VerifyAgreementAndroidUrl;
  } else if (Platform.isIOS) {
    return VerifyAgreementIOSUrl;
  }
  return VerifyAgreementAndroidUrl;
}


