import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

Future<Uint8List?> getShotcutImageData(GlobalKey repaintKey) async {
  var renderObject = repaintKey.currentContext?.findRenderObject();
  if(renderObject == null) {
    return null;
  }
  RenderRepaintBoundary boundary = renderObject as RenderRepaintBoundary;
  var image = await boundary.toImage(pixelRatio: 3.0);
  ByteData? byteData = await image.toByteData(format: ImageByteFormat.png);
  Uint8List? pngBytes = byteData?.buffer.asUint8List();
  return pngBytes;
}
