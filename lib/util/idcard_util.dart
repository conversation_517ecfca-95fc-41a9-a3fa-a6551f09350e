
bool isValidIdNumber(String idNumber) {
  // 检查长度
  if (idNumber.length != 18) {
    return false;
  }

  // 检查出生日期
  try {
    DateTime.parse(idNumber.substring(6, 14));
  } catch (e) {
    return false;
  }

  // 检查性别
  if (!isDigit(idNumber[16])) {
    return false;
  }

  // 检查校验码
  if (!isValidChecksum(idNumber)) {
    return false;
  }

  return true;
}

bool isValidChecksum(String idNumber) {
  List<int> weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  List<String> parityBits = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

  int checksum = 0;
  for (int i = 0; i < 17; i++) {
    checksum += weights[i] * int.parse(idNumber[i]);
  }

  return parityBits[checksum % 11] == idNumber[17];
}

bool isDigit(String str) {
  RegExp regExp = RegExp(r'^[0-9]+$');
  return regExp.hasMatch(str);
}

bool containsChinese(String text) {
  // 使用正则表达式判断文本中是否包含中文字符
  RegExp chineseRegExp = RegExp(r'[\u4e00-\u9fa5]');
  return chineseRegExp.hasMatch(text);
}
