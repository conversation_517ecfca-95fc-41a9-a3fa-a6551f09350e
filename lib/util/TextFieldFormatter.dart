import 'package:flutter/services.dart';

import 'dart:math';

import 'package:new_edge/util/phone.dart';

abstract class NumberInputFormatter extends TextInputFormatter {
  TextEditingValue? _lastNewValue;

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue
      ) {
    if (newValue.text == _lastNewValue?.text) {
      return newValue;
    }

    _lastNewValue = newValue;

    newValue = _formatValue(oldValue, newValue);

    /// current selection
    int selectionIndex = newValue.selection.end;

    /// format original string, this step would add some separator
    /// characters to original string
    final newText = _formatPattern(newValue.text)!;

    int insertCount = 0;
    int inputCount = 0;
    for (int i = 0; i < newText.length && inputCount < selectionIndex; i++) {
      final character = newText[i];
      if (_isUserInput(character)) {
        inputCount++;
      } else {
        insertCount++;
      }
    }

    selectionIndex += insertCount;
    selectionIndex = min(selectionIndex, newText.length);

    if (selectionIndex - 1 >= 0 &&
        selectionIndex - 1 < newText.length &&
        !_isUserInput(newText[selectionIndex - 1])) {
      selectionIndex--;
    }
    return newValue.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: selectionIndex),
        composing: TextRange.empty);
  }

  bool _isUserInput(String s);

  /// format user input with pattern formatter
  String? _formatPattern(String digits);

  /// validate user input
  TextEditingValue _formatValue(
      TextEditingValue oldValue, TextEditingValue newValue);

}

class PhoneFormatter extends NumberInputFormatter {
  static final RegExp _digitOnlyRegex = RegExp(r'\d+');
  // static final WhitelistingTextInputFormatter _digitOnlyFormatter = WhitelistingTextInputFormatter(_digitOnlyRegex);

  final String separator;
  final String? areaCode;

  PhoneFormatter({this.separator = ' ', this.areaCode});

  @override
  String? _formatPattern(String digits) {
    return formatPhone(
      digits: digits,
      areaCode: this.areaCode,
    );
  }

  @override
  TextEditingValue _formatValue(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    return oldValue;// _digitOnlyFormatter.formatEditUpdate(oldValue, newValue);
  }

  @override
  bool _isUserInput(String s) {
    return _digitOnlyRegex.firstMatch(s) != null;
  }

  // String breakWord(String word) {
  //   if (word == null || word.isEmpty) {
  //     return word;
  //   }
  //   String breakWord = ' ';
  //   word.runes.forEach((element) {
  //     breakWord += String.fromCharCode(element);
  //     breakWord += '\u200B';
  //   });
  //   return breakWord;
  // }
}