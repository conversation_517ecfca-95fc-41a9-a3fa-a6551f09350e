import 'dart:convert';
// import 'package:apple_sign_in/apple_sign_in.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/share/share_manager.dart';
import 'package:new_edge/util/string_format_utils.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
// import 'package:sharesdk_plugin/sharesdk_plugin.dart';

part 'open_account_utils.g.dart';

class OpenAccountUtils {
  // static final ShareSDKPlatform applePlatform =
  // ShareSDKPlatform(name: "apple", id: 999);
  // static final ShareSDKPlatform saveToAlbumPlatform =
  // ShareSDKPlatform(name: "apple", id: 999);

  // static String getBindType(ShareSDKPlatform platform) {
  //   if (platform == ShareSDKPlatforms.wechatSession) {
  //     return "WECHAT";
  //   } else if (platform == ShareSDKPlatforms.qq) {
  //     return "QQ";
  //   } else if (platform == ShareSDKPlatforms.sina) {
  //     return "WEIBO";
  //   } else if (platform == applePlatform) {
  //     return "APPLE_ID";
  //   }
  //   return "";
  // }

  // static String getPlatformName(ShareSDKPlatform platform) {
  //   // if (platform == ShareSDKPlatforms.wechatSession) {
  //   //   return "微信";
  //   // } else if (platform == ShareSDKPlatforms.qq) {
  //   //   return "QQ";
  //   // } else if (platform == ShareSDKPlatforms.sina) {
  //   //   return "微博";
  //   // } else if (platform == applePlatform) {
  //   //   return "Apple ID";
  //   // }
  //   return "";
  // }

  static Future<OpenUserInfo> appleId() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    print(credential);
    // Now send the credential (especially `credential.authorizationCode`) to your server to create a session
    // after they have been validated with Apple (see `Integration` section for more information on how to do this)
    var userInfo = OpenUserInfo();
    // userInfo.platform = OpenAccountUtils.applePlatform;
    // userInfo.bindType = getBindType(applePlatform);
    userInfo.nick = credential.givenName;
    userInfo.token = credential.identityToken;
    userInfo.openId = credential.authorizationCode;

    return userInfo;

    // final AuthorizationResult result = await AppleSignIn.performRequests([
    //   AppleIdRequest(requestedScopes: [Scope.email, Scope.fullName])
    // ]);
    //
    // switch (result.status) {
    //   case AuthorizationStatus.authorized:
    //     var userInfo = OpenUserInfo();
    //     // userInfo.platform = OpenAccountUtils.applePlatform;
    //     // userInfo.bindType = getBindType(applePlatform);
    //     userInfo.nick = "";
    //     userInfo.token = String.fromCharCodes(result.credential.identityToken);
    //     userInfo.openId =
    //         String.fromCharCodes(result.credential.authorizationCode);
    //
    //     print("Sign in success");
    //     print(
    //         "user:${result.credential.user},name:${result.credential.fullName.givenName},token:${String.fromCharCodes(result.credential.identityToken)},email:${result.credential.email}");
    //     return userInfo;
    //     break;
    //
    //   case AuthorizationStatus.error:
    //     print("Sign in failed: ${result.error.localizedDescription}");
    //     throw Exception("授权失败");
    //     break;
    //
    //   case AuthorizationStatus.cancelled:
    //     print('User cancelled');
    //     throw Exception("");
    //     break;
    //   default:
    //     throw Exception("");
    //     break;
    // }
  }

  static OpenUserInfo getInfoByDbInfo(String dbInfo) {
    var info = jsonDecode(dbInfo);
    //wechat/qq（weibo没有unionid，正在研究是否可以支持B端登录）
    var unionid = info['unionid'];
    var openid = info['openid']; // wechat
    var userID = info['userID']; // qq/weibo
    var token = info['token'];
    var refresh_token = info['refresh_token']; // QQ不提供
    var expiresIn = info['expiresIn'];
    var expiresTime = info['expiresTime'];

    var gender = info['gender'];
    //要处理昵称规则，可能存在特殊字符，和我们的昵称规则不符，或者存在重复昵称
    var nickname = info['nickname'];
    var icon = info['icon']; // 头像，需要上传ufile
    var province = info['province']; // wechat
    var city = info['city']; // wechat

    var userInfo = OpenUserInfo();
    userInfo.unionId = unionid;
    userInfo.openId = openid != null ? openid : userID;
    userInfo.token = token;
    userInfo.refreshToken = refresh_token;
    userInfo.expiresIn = expiresIn;
    userInfo.expiresTime = expiresTime;
    userInfo.nick = nickname;
    userInfo.faceUrl = icon;
    userInfo.province = province;
    userInfo.city = city;
    return userInfo;
  }

  static OpenUserInfo getInfoByCommon(Map user) {
    print(jsonEncode(user));
    var icon = user["icon"];
    var gender = user["gender"];
    var nickname = user["nickname"];
    var credential = user["credential"];
    var uid = credential["uid"];
    var expired = credential["expired"];
    var rawData = credential["rawData"];
    var refresh_token = rawData["refresh_token"];
    var access_token = rawData["access_token"];
    var unionid = rawData["unionid"];
    var openid = rawData["openid"];

    var userInfo = OpenUserInfo();
    userInfo.unionId = unionid;
    userInfo.openId = openid != null ? openid : uid;
    userInfo.token = access_token;
    userInfo.refreshToken = refresh_token;
//    userInfo.expiresIn = expiresIn;
    userInfo.expiresTime = expired.toInt();
    userInfo.nick = nickname;
    userInfo.faceUrl = icon;
    userInfo.province = user["rawData"]['province'];
    userInfo.city = user["rawData"]['city'];
//    userInfo.gender = gender.toInt().toString();
    return userInfo;
  }

  static Future<OpenUserInfo> getUserInfo(
      BuildContext context,
      // ShareSDKPlatform platform,
      ) async {
    // if (platform == applePlatform) {
    //   return appleId();
    // }

    await ShareManager.setup();
    // var result = await SharesdkPlugin.isClientInstalled(platform);
    // bool isInstalled;
    // if (result is Map) {
    //   isInstalled = (result["state"] == "installed");
    // } else {
    //   isInstalled = result;
    // }
    // if (!isInstalled) {
    //   var name = "";
    //   if (platform == ShareSDKPlatforms.sina) {
    //     name = "微博";
    //   }
    //   if (platform == ShareSDKPlatforms.wechatSession) {
    //     name = "微信";
    //   }
    //   if (platform == ShareSDKPlatforms.qq) {
    //     name = "QQ";
    //   }
    //   throw Exception("你未安装$name");
    // }
    OpenUserInfo? userInfo;
    String errorContent = "";
    // await SharesdkPlugin.getUserInfo(platform, (state, user, error) {
    //   if (state == SSDKResponseState.Success) {
    //     if (user.containsKey("dbInfo")) {
    //       userInfo = getInfoByDbInfo(user['dbInfo']);
    //     } else {
    //       userInfo = getInfoByCommon(user);
    //     }
    //     if (platform == ShareSDKPlatforms.wechatSession) {
    //       userInfo.bindType = "WECHAT";
    //     } else if (platform == ShareSDKPlatforms.qq) {
    //       userInfo.bindType = "QQ";
    //     } else if (platform == ShareSDKPlatforms.sina) {
    //       userInfo.bindType = "WEIBO";
    //     }
    //   } else if (state == SSDKResponseState.Fail) {
    //     errorContent = "授权错误:${error.code}";
    //   } else if (state == SSDKResponseState.Cancel) {
    //     errorContent = "";
    //   }
    // });
    if (userInfo != null) {
      if (userInfo.faceUrl != null && userInfo.faceUrl!.isNotEmpty) {
        try {
          // var path = await ImageUtils.saveTemp(userInfo.faceUrl);
          // userInfo.faceUrl = await UFileManager.uploadFile(File(path));
          // print("新的图片链接 ${userInfo.faceUrl}");
        } catch (e) {}
      }
      // userInfo.platform = platform;
      return userInfo;
    }
    if (errorContent == null) {
      errorContent = "";
    }
    throw Exception(errorContent);
  }
}

@JsonSerializable()
class OpenUserInfo {
  String? phone;
  String? areaCode;
  String? verifyCode;

  String? unionId;
  String? openId; // wechat
  String? userID; // qq/weibo
  String? token;
  String? refreshToken; // QQ不提供
  int? expiresIn;
  int? expiresTime;
  String? nick;
  String? faceUrl;
  String? bindType;
  String? province;
  String? city;
  String? gender;
  @JsonKey(ignore: true)
  // ShareSDKPlatform platform;

  OpenUserInfo(
      {this.phone,
        this.areaCode,
        this.verifyCode,
        this.unionId,
        this.openId,
        this.userID,
        this.token,
        this.refreshToken,
        this.expiresIn,
        this.expiresTime,
        this.nick,
        this.faceUrl,
        this.bindType,
        this.province,
        this.city,
        this.gender});

  factory OpenUserInfo.fromJson(Map<String, dynamic> json) =>
      _$OpenUserInfoFromJson(json);

  Map<String, dynamic> toJson() => _$OpenUserInfoToJson(this);
}
