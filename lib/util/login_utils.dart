import 'package:flutter/cupertino.dart';
import 'package:new_edge/model/account.dart';
import 'package:new_edge/pages/login/login_input_phone.dart';
import 'package:new_edge/util/navigator.dart';

class LoginUtils {
  static Function? mFunction;
  static BuildContext? mContext;

  static setILogin(Function function, BuildContext context) {
    // mFunction = function;
    // mContext = context;
    if (Account.isLogin()) {
      if (function != null) {
        function();
      }
    } else {
      pushPage(context, LoginInputPhonePage());
    }
  }

  // static isLogin() {
  //   if (Account.isLogin()) {
  //     if (mFunction != null) {
  //       mFunction();
  //     }
  //   } else {
  //     pushPage(mContext, LoginInputPhonePage());
  //   }
  // }
}

abstract class ILogin {
  onLogin();
}
