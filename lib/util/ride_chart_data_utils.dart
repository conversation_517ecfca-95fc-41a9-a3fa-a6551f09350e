import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/model/ride_record.dart';
import 'package:new_edge/model/ride_record_chart.dart';
import 'package:new_edge/util/string_format_utils.dart';

extension RideRecordExtension on RideRecord {
  // 添加一个新方法
  void fixMinMaxValue() {
    calMaxMinSpeed(this);
    calMaxMinAltitude(this);
    calMaxMinCadenceRate(this);
    calMaxMinHeartRate(this);
    calMaxMinPowerRate(this);

    if (climb == 0) {
      climb = getClimb(this);
    }
  }
}

/// 全程均速
double getAllAvgSpeed(RideRecord record) {
  int seconds =
      (((record.endTime ?? 0) - (record.startTime ?? 0)) / 1000.0).ceil();
  return (record.meter! / 1000.0) / (seconds / 3600.0);
}

/// 全程时间
String? getAllTime(RideRecord record) {
  int seconds =
      (((record.endTime ?? 0) - (record.startTime ?? 0)) / 1000.0).ceil();
  return formatRideSeconds(seconds);
}

/// 计算最大最小速度
void calMaxMinSpeed(RideRecord record) {
  var maxSpeed = 0.0;
  var minSpeed = 1000.0;
  if (record.locationDetail != null) {
    List locationDetails = json.decode(record.locationDetail!);
    for (var i = 0; i < locationDetails.length; i++) {
      List locationDetail = locationDetails[i];
      if (locationDetail.length != 3) {
        return;
      }
      double speedD = 0.0;
      dynamic speed = locationDetail[2];
      if (speed is String) {
        speedD = double.parse(speed);
      } else if (speed is int) {
        speedD = speed.toDouble();
      } else {
        speedD = speed;
      }
      if (speedD > maxSpeed) {
        maxSpeed = speedD;
        record.setMaxSpeedIndex = i;
      }
      if (speedD < minSpeed) {
        record.setMinSpeedIndex = i;
      }
    }
  }
  maxSpeed = (maxSpeed * 10).ceil() / 10.0;
  minSpeed = (minSpeed * 10).ceil() / 10.0;

  record.maxSpeed = maxSpeed;
}

void calMaxMinCadenceRate(RideRecord record) {
  if (record.cadence != null) {
    List cadenceRate = [];
    try {
      cadenceRate = json.decode(record.cadence ?? '[]');
      int maxcadenceRate = 0;
      int mincadenceRate = 0;
      int sum = 0;
      if (cadenceRate.isNotEmpty) {
        mincadenceRate = cadenceRate[0][0];
      }
      for (int i = 0; i < cadenceRate.length; i++) {
        dynamic value = cadenceRate[i];
        sum = (sum + value[0]!) as int;
        if (value[0] > maxcadenceRate) {
          maxcadenceRate = value[0];
          record.setMaxCadenceRateIndex = i;
        }
        if (value[0] < mincadenceRate) {
          mincadenceRate = value[0];
          record.setMinCadenceRateIndex = i;
        }
      }
      record.setMinCadenceRate = mincadenceRate;
      record.setMaxCadenceRate = maxcadenceRate;
      record.setAvgCadenceRate =
          cadenceRate.isEmpty ? 0 : sum ~/ cadenceRate.length;
    } catch (e) {
      print(e);
    }
  }
}

void calMaxMinPowerRate(RideRecord record) {
  if (record.power != null) {
    List powerRate = [];
    try {
      powerRate = json.decode(record.power ?? '[]');
      int maxpowerRate = 0;
      int minpowerRate = 0;
      int sum = 0;
      if (powerRate.isNotEmpty) {
        minpowerRate = powerRate[0];
      }
      for (int i = 0; i < powerRate.length; i++) {
        dynamic value = powerRate[i];
        sum = (sum + value!) as int;
        if (value > maxpowerRate) {
          maxpowerRate = value;
          record.setMaxPowerRateIndex = i;
        }
        if (value < minpowerRate) {
          minpowerRate = value;
          record.setMinPowerRateIndex = i;
        }
      }
      record.setMinPowerRate = minpowerRate;
      record.setMaxPowerRate = maxpowerRate;
      record.setAvgPowerRate = powerRate.isEmpty ? 0 : sum ~/ powerRate.length;
    } catch (e) {
      print(e);
    }
  }
}

void calMaxMinHeartRate(RideRecord record) {
  if (record.heartRate != null) {
    List heartRate = [];
    try {
      heartRate = json.decode(record.heartRate ?? '[]');
      int maxheartRate = 0;
      int minheartRate = 0;
      int sum = 0;
      if (heartRate.isNotEmpty) {
        minheartRate = heartRate[0];
      }
      for (int i = 0; i < heartRate.length; i++) {
        dynamic value = heartRate[i];
        sum = (sum + value) as int;
        if (value > maxheartRate) {
          maxheartRate = value;
          record.setMaxHeartRateIndex = i;
        }
        if (value < minheartRate) {
          minheartRate = value;
          record.setMinHeartRateIndex = i;
        }
      }
      record.setMinHeartRate = minheartRate;
      record.setMaxHeartRate = maxheartRate;
      record.setAvgHeartRate = heartRate.isEmpty ? 0 : sum ~/ heartRate.length;
    } catch (e) {
      print(e);
    }
  }
}

void calMaxMinAltitude(RideRecord record) {
  if (record.altitude != null && record.altitude!.length > 2) {
    List altitudes = json.decode(record.altitude ?? '[]');
    double maxAltitude = 0.0;
    double minAltitude = 0.0;
    double sum = 0;
    if (altitudes.isNotEmpty) {
      try {
        var alt = altitudes[0];
        if (alt is String) {
          minAltitude = double.parse(alt);
        } else if (alt is int) {
          minAltitude = alt.toDouble();
        } else {
          minAltitude = alt;
        }
      } catch (e) {}
    }
    for (int i = 0; i < altitudes.length; i++) {
      var altitude = altitudes[i];
      dynamic value = altitude;
      if (altitude is String) {
        value = double.parse(altitude);
      } else if (altitude is int) {
        value = altitude.toDouble();
      } else {
        value = altitude;
      }
      sum += value;
      if (value > maxAltitude) {
        maxAltitude = value;
        record.setMaxAltitudeIndex = i;
      }
      if (value < minAltitude) {
        minAltitude = value;
        record.setMinAltitudeIndex = i;
      }
    }
    record.setMaxAltitude = maxAltitude;
    record.setMinAltitude = minAltitude;
    record.setAvgAltitude =
        altitudes.isEmpty ? 0 : sum / altitudes.length.toDouble();
  } else if (record.locationDetail != null &&
      record.locationDetail!.length > 2) {
    List locationDetails = json.decode(record.locationDetail ?? '[]');
    double maxAltitude = 0.0;
    double minAltitude = 0.0;
    double sum = 0;
    for (int i = 0; i < locationDetails.length; i++) {
      var locationDetail = locationDetails[i];
      var altitude = locationDetail[0];
      double value = 0.0;
      if (altitude is String) {
        value = double.parse(altitude) / 100.0;
      } else if (altitude is int) {
        value = altitude.toDouble() / 100.0;
      } else {
        value = altitude / 100.0;
      }
      sum += value;
      if (value > maxAltitude) {
        maxAltitude = value;
        record.setMaxAltitudeIndex = i;
      }
      if (value < minAltitude) {
        minAltitude = value;
        record.setMinAltitudeIndex = i;
      }
    }
    record.setMaxAltitude = maxAltitude;
    record.setMinAltitude = minAltitude;
    record.setAvgAltitude =
        locationDetails.isEmpty ? 0 : sum / locationDetails.length.toDouble();
  } else {
    record.setMaxAltitude = 0.0;
    record.setMinAltitude = 0.0;
  }
}

//获取爬升数据
int getClimb(RideRecord record) {
  if (record.altitude == null) {
    return 0;
  }
  List altitudes = json.decode(record.altitude!);
  if (altitudes.isNotEmpty) {
    var raise = 0.0;
    var lastBasedAltitude = 0.0;
    dynamic altitude = altitudes[0];
    if (altitude is String) {
      lastBasedAltitude = double.parse(altitude);
    } else if (altitude is int) {
      lastBasedAltitude = altitude.toDouble();
    } else {
      lastBasedAltitude = altitude;
    }
    for (int i = 1; i < altitudes.length; i++) {
      var cur = 0.0;
      dynamic altitude = altitudes[i];
      if (altitude is String) {
        cur = double.parse(altitude);
      } else if (altitude is int) {
        cur = altitude.toDouble();
      } else {
        cur = altitude;
      }
      var diff = cur - lastBasedAltitude;
      if (diff > 1) {
        raise += diff;
        lastBasedAltitude = cur;
      } else if (diff < 0) {
        lastBasedAltitude = cur;
      }
    }
    return raise.ceil();
  } else {
    return getGPSClimb(record);
  }
}

//获取GPS爬升数据
int getGPSClimb(RideRecord record) {
  List locationDetails = json.decode(record.locationDetail!);
  var raise = 0.0;
  double? lastBasedAltitude = 0.0;
  dynamic altitude = locationDetails[0][0];
  if (altitude is String) {
    lastBasedAltitude = double.parse(altitude) / 100.0;
  } else if (altitude is int) {
    lastBasedAltitude = altitude.toDouble() / 100.0;
  } else {
    lastBasedAltitude = altitude / 100.0;
  }
  for (int i = 1; i < locationDetails.length; i++) {
    List locationDetail = locationDetails[i];
    double? cur = 0.0;
    dynamic altitude = locationDetail[0];
    if (altitude is String) {
      cur = double.parse(altitude) / 100.0;
    } else if (altitude is int) {
      cur = altitude.toDouble() / 100.0;
    } else {
      cur = altitude / 100.0;
    }
    var diff = cur! - lastBasedAltitude!;
    if (diff > 1) {
      raise += diff;
      lastBasedAltitude = cur;
    } else if (diff < 0) {
      lastBasedAltitude = cur;
    }
  }
  return raise.ceil();
}

enum ChartType { Meter, Time }

/**
 * 取样绘图，1、避免轨迹点过多影响计算性能；2、1个像素只能绘制1个点，超出的点也无法绘制
 * 取样数定为1000
 */
class LineChartDataUtil {
  final RideRecord? record;
  final String? from; //'route'/'ride'
  final int? itemPosition;
  List<int> sampleIndexes = [];

  LineChartDataUtil({this.record, this.from, this.itemPosition});

  Future<Map<String, RideRecordChart>> buildChartData(
      {ChartType type = ChartType.Meter, int maxSampleCount = 170}) async {
    if (record == null) {
      throw Error();
    }

    ChartType chartType = type;
    if (type == ChartType.Meter && (record!.meter ?? 0) <= 0) {
      chartType = ChartType.Time;
    }

    List locationDetails = [];
    List paths = [];
    List pauseIndexes = [];
    try {
      locationDetails = json.decode(record!.locationDetail ?? "[]");
      if ((record!.content?.length ?? 0) > 0) {
        var temp = "[${record!.content?.replaceAll("]-[", "],[")}]";
        paths = json.decode(temp ?? "[]");
      }
      List pauses = json.decode(record!.pauseTime ?? "[]");
      for (var element in pauses) {
        pauseIndexes.add(element[0]);
      }
    } catch (e) {
      print(e);
    }
    if (locationDetails.isEmpty) {
      throw Error(); // 无法计算每个点的x坐标
    }

    // 获取抽样点
    sampleIndexes = getSampleIndexs(chartType, locationDetails, maxSampleCount);

    // 因为时间图表，要扣掉暂停时间不画，所以先计算每个点的数据属于第几秒，即将paths的时间戳，转换为运动的第几秒
    List dataSecondIndexes = [];
    // X轴
    var maxX = 0.0;
    String xUnit = '';

    if (chartType == ChartType.Meter) {
      // 有里程，x轴用里程
      maxX = (record!.meter ?? 0) / 1000.0;
      xUnit = 'KM';
    } else {
      // 没有里程，x轴用时间
      maxX = (record!.second ?? 0).toDouble();
      xUnit = 'S';
      // 计算dataSecondIndexes
      if (paths.isNotEmpty) {
        var sectionStartTime = paths[0][2]; // 每一段运动的开始时间
        var untilLastSectionSeconds = 0; // 直到最后一段的累计时间
        for (var i = 0; i < paths.length; i++) {
          var path = paths[i];
          var dataSecond = path[2] -
              sectionStartTime +
              1 +
              untilLastSectionSeconds; // 当前数据在第几秒
          dataSecondIndexes.add(dataSecond - 1); // 生成索引
          if (pauseIndexes.contains(i) && i < paths.length - 1) {
            // 当前是暂停点
            sectionStartTime = paths[i + 1][2];
            untilLastSectionSeconds = dataSecond;
          }
        }
      } else {
        // 旧数据
        for (var i = 0; i < locationDetails.length; i++) {
          dataSecondIndexes.add(i); // 生成索引
        }
      }
    }
    final verticalInterval = (maxX / 5.0).ceil();
    // 速度的Y轴
    double maxSpeed = record!.maxSpeed ?? 0;
    maxSpeed = maxSpeed * 1.1;
    final horizontalIntervalSpeed = (maxSpeed / 5.0).ceil();
    // 海拔的Y轴
    List? altitudes = [];
    try {
      altitudes = json.decode(record!.altitude ?? "[]");
    } catch (e) {
      print(e);
    }
    double maxAltitude = record!.maxAltitude ?? 0.0;
    double minAltitude = record!.minAltitude ?? 0.0;
    if (maxAltitude != 0) {
      maxAltitude += 40;
    }
    if (minAltitude != 0) {
      minAltitude -= 40;
    }

    //心率
    List? heartRate = [];
    try {
      heartRate = json.decode(record!.heartRate ?? "[]");
    } catch (e) {
      print(e);
    }
    int maxHeartRate = record!.maxHeartRate ?? 0;
    int minHeartRate = record!.minHeartRate ?? 0;
    maxHeartRate = (maxHeartRate * 1.1).toInt();
    if (minHeartRate != 0) {
      minHeartRate -= 20;
    }
    final horizontalIntervalHeart =
        ((maxHeartRate - minHeartRate) / 5.0).ceil();
    //功率
    List? powerRate = [];
    try {
      powerRate = json.decode(record!.power ?? "[]");
    } catch (e) {
      print(e);
    }
    int maxPowerRate = record!.maxPowerRate ?? 0;
    int minPowerRate = 0;
    maxPowerRate = (maxPowerRate * 1.1).toInt();
    final horizontalIntervalPower =
        ((maxPowerRate - minPowerRate) / 5.0).ceil();

    //踏频
    List? cadenceRate = [];
    try {
      cadenceRate = json.decode(record!.cadence ?? "[]");
    } catch (e) {
      print(e);
    }
    int maxCadenceRate = record!.maxCadenceRate ?? 0;
    int minCadenceRate = 0;
    maxCadenceRate = (maxCadenceRate * 1.1).toInt();
    final horizontalIntervalCadence =
        ((maxCadenceRate - minCadenceRate) / 5.0).ceil();

    // 计算坐标点
    List<FlSpot> altitudeSpots = _getAltitudeSpots(chartType, sampleIndexes,
        locationDetails, altitudes, record!, dataSecondIndexes);
    List<FlSpot> speedSpots = _getSpeedSpots(
        chartType, sampleIndexes, locationDetails, record!, dataSecondIndexes);
    List<FlSpot> heartSpots = _getHeartSpots(chartType, sampleIndexes,
        locationDetails, heartRate, record!, dataSecondIndexes);
    List<FlSpot> powerSpots = _getPowerSpots(chartType, sampleIndexes,
        locationDetails, powerRate, record!, dataSecondIndexes);
    List<FlSpot> cadenceSpots = _getCadenceSpots(chartType, sampleIndexes,
        locationDetails, cadenceRate, record!, dataSecondIndexes);
    var horizontalIntervalAltitude = ((maxAltitude - minAltitude) / 5.0).ceil();
    if (horizontalIntervalAltitude == 0) {
      horizontalIntervalAltitude = 1;
    }

    Map<String, RideRecordChart> chartData = {};

    try {
      if (speedSpots.isNotEmpty && maxSpeed != 0) {
        chartData["Speed"] = RideRecordChart(
            tag: "Speed",
            lineColor: DColor.ff3ebcff,
            minX: 0,
            maxX: maxX,
            xUnit: xUnit,
            minY: 0,
            maxY: maxSpeed,
            averageY: record!.speed == null
                ? (record!.avgSpeed * 3.6)
                : record!.avgSpeed * 3.6,
            // 运动均速
            horizontalInterval: horizontalIntervalSpeed.toDouble(),
            verticalInterval: verticalInterval.toDouble(),
            yAxisUnit: "KM/H",
            spots: speedSpots);
      }
    } catch (e) {
      print(e);
    }

    try {
      if (altitudeSpots.isNotEmpty &&
          !(maxAltitude == 0.0 && minAltitude == 0.0)) {
        chartData["Altitude"] = RideRecordChart(
            tag: "Altitude",
            lineColor: DColor.ccb9b9b9,
            minX: 0,
            maxX: maxX,
            xUnit: xUnit,
            minY: minAltitude,
            maxY: maxAltitude,
            averageY: 0,
            //海拔不要平均值线
            horizontalInterval: horizontalIntervalAltitude.toDouble(),
            verticalInterval: verticalInterval.toDouble(),
            yAxisUnit: "M",
            spots: altitudeSpots);
      }
    } catch (e) {
      print(e);
    }

    try {
      if (heartSpots.isNotEmpty &&
          !(minHeartRate.toDouble() == 0.0 && maxHeartRate.toDouble() == 0.0)) {
        chartData["HeartRate"] = RideRecordChart(
            tag: "HeartRate",
            // lineColor: Color(0xFFFB691D),
            lineColor: DColor.ccff6c6c,
            minX: 0,
            maxX: maxX,
            xUnit: xUnit,
            minY: minHeartRate.toDouble(),
            maxY: maxHeartRate.toDouble(),
            averageY: record!.avgHeartRate!.toDouble(),
            horizontalInterval: horizontalIntervalHeart.toDouble(),
            verticalInterval: verticalInterval.toDouble(),
            yAxisUnit: "BPM",
            spots: heartSpots);
      }
    } catch (e) {
      print(e);
    }
    try {
      if (powerSpots.isNotEmpty &&
          !(minPowerRate.toDouble() == 0.0 && maxPowerRate.toDouble() == 0.0)) {
        chartData["Power"] = RideRecordChart(
            tag: "Power",
            lineColor: DColor.ccffb06c,
            minX: 0,
            maxX: maxX,
            xUnit: xUnit,
            minY: 0,
            maxY: maxPowerRate.toDouble(),
            averageY: record!.avgPowerRate!.toDouble(),
            horizontalInterval: horizontalIntervalPower.toDouble(),
            verticalInterval: verticalInterval.toDouble(),
            yAxisUnit: "W",
            spots: powerSpots);
      }
    } catch (e) {
      print(e);
    }
    try {
      if (cadenceSpots.isNotEmpty &&
          !(minCadenceRate.toDouble() == 0.0 &&
              maxCadenceRate.toDouble() == 0.0)) {
        chartData["Cadence"] = RideRecordChart(
            tag: "Cadence",
            lineColor: DColor.cc776cff,
            minX: 0,
            maxX: maxX,
            xUnit: xUnit,
            minY: 0,
            maxY: maxCadenceRate.toDouble(),
            averageY: record!.avgCadenceRate?.toDouble(),
            horizontalInterval: horizontalIntervalCadence.toDouble(),
            verticalInterval: verticalInterval.toDouble(),
            yAxisUnit: "RPM",
            spots: cadenceSpots);
      }
    } catch (e) {
      print(e);
    }
    return chartData;
  }

  double speedYAfterZero = 0.0;

  int _getSpeedAfterZeroPosition(
      List<int> sampleIndexs, List? locationDetails) {
    speedYAfterZero = 0.0;
    for (var j = sampleIndexs.length - 1; j > 0; j--) {
      int index = sampleIndexs[j];
      if (locationDetails!.length > index) {
        List locationDetail = locationDetails[index];
        // 计算y坐标
        double speedY = 0;
        dynamic speed = locationDetail[2];
        if (speed is String) {
          speedY = double.parse(speed);
        } else if (speed is int) {
          speedY = speed.toDouble();
        } else {
          speedY = speed;
        }
        if (speedY < 0) {
          speedY = 0;
        }
        speedY = (speedY * 10).ceil() / 10.0;
        speedYAfterZero += speedY;
        if (speedYAfterZero > 0) {
          return index;
        }
      }
    }
    return 0;
  }

  List<FlSpot> _getSpeedSpots(ChartType chartType, List<int> sampleIndexs,
      List? locationDetails, RideRecord record, List dataSecondIndexes) {
    List<FlSpot> speedSpots = [];

    double speedYBeforeZero = 0.0;
    for (var j = 0; j < sampleIndexs.length; j++) {
      int index = sampleIndexs[j];
      //首先计算X坐标
      // 轨迹点：lat lng elevation speed meter
      if ((locationDetails?.length ?? 0) > index) {
        List locationDetail = locationDetails![index];

        // 计算X坐标
        double x = 0;
        if (chartType == ChartType.Meter) {
          // 有里程，x轴用里程
          dynamic meter = locationDetail[1];
          if (from == 'route') {
            x = (record.meter ?? 0) * (j / sampleIndexs.length) / 1000;
          } else {
            if (meter is String) {
              x = double.parse(meter) / 1000.0;
            } else if (meter is int) {
              x = meter.toDouble() / 1000.0;
            } else {
              x = meter / 1000.0;
            }
          }
        } else {
          // 没有里程，x轴用时间
          // x = dataSecondIndexes[index].toDouble();
          x = (record.second ?? 0) * (j / sampleIndexs.length);
        }
        // 计算y坐标
        double speedY = 0;
        if (locationDetail.length < 3) {
          continue;
        }
        dynamic speed = locationDetail[2];
        if (speed is String) {
          speedY = double.parse(speed);
        } else if (speed is int) {
          speedY = speed.toDouble();
        } else {
          speedY = speed;
        }
        if (speedY < 0) {
          speedY = 0;
        }
        speedY = (speedY * 10).ceil() / 10.0;
        speedYBeforeZero += speedY;
        // speedSpots.add(FlSpot(x, speedY));
        if (index == 0 ||
            speedYBeforeZero == 0 ||
            (_getSpeedAfterZeroPosition(sampleIndexs, locationDetails) != 0 &&
                _getSpeedAfterZeroPosition(sampleIndexs, locationDetails) <=
                    index) ||
            speedY > 0) {
          // 过滤行程异常速度0，开始第一个点,开始连续0，结束前连续0
          speedSpots.add(FlSpot(x, speedY));
        } else {
          if (index > 0 && speedSpots.isNotEmpty) {
            speedSpots.add(FlSpot(x, speedSpots[speedSpots.length - 1].y));
          }
        }
      }
    }
    return speedSpots;
  }

  List<FlSpot> _getCadenceSpots(
      ChartType chartType,
      List<int> sampleIndexs,
      List? locationDetails,
      List? cadenceRate,
      RideRecord record,
      List dataSecondIndexes) {
    List<FlSpot> cadenceSpots = [];

    sampleIndexs.sort();
    for (var j = 0; j < sampleIndexs.length; j++) {
      int index = sampleIndexs[j];
      if (locationDetails!.length > index) {
        //首先计算X坐标
        // 轨迹点：lat lng elevation speed meter
        List locationDetail = locationDetails[index];

        // 计算X坐标
        double? x = 0;
        if (chartType == ChartType.Meter) {
          // 有里程，x轴用里程
          dynamic meter = locationDetail[1];
          if (from == 'route') {
            x = (record.meter ?? 0) * (j / sampleIndexs.length) / 1000;
          } else {
            if (meter is String) {
              x = double.parse(meter) / 1000.0;
            } else if (meter is int) {
              x = meter.toDouble() / 1000.0;
            } else {
              x = meter / 1000.0;
            }
          }
        } else {
          // 没有里程，x轴用时间
          // x = dataSecondIndexes[index].toDouble();
          x = (record.second ?? 0) * (j / sampleIndexs.length);
        }

        // 计算y坐标
        if (cadenceRate!.length > index) {
          double? cadenceRateY = 0;
          dynamic cadence = cadenceRate[index];
          if (cadence[0] is int) {
            cadenceRateY = cadence[0].toDouble();
          } else {
            cadenceRateY = cadence[0];
          }
          cadenceRateY = (cadenceRateY! * 10).ceil() / 10.0;
          if (cadenceRateY > 0) {
            cadenceSpots.add(FlSpot(x!, cadenceRateY));
          } else {
            if (index > 0 && cadenceSpots.isNotEmpty) {
              cadenceSpots
                  .add(FlSpot(x!, cadenceSpots[cadenceSpots.length - 1].y));
            }
          }
        }
      }
    }
    return cadenceSpots;
  }

  List<FlSpot> _getHeartSpots(
      ChartType chartType,
      List<int> sampleIndexs,
      List? locationDetails,
      List? heartRate,
      RideRecord record,
      List dataSecondIndexes) {
    List<FlSpot> heartSpots = [];

    for (var j = 0; j < sampleIndexs.length; j++) {
      int index = sampleIndexs[j];
      if (locationDetails!.length > index) {
        //首先计算X坐标
        // 轨迹点：lat lng elevation speed meter
        List locationDetail = locationDetails[index];

        // 计算X坐标
        double? x = 0;
        if (chartType == ChartType.Meter) {
          // 有里程，x轴用里程
          dynamic meter = locationDetail[1];
          if (from == 'route') {
            x = (record.meter ?? 0) * (j / sampleIndexs.length) / 1000;
          } else {
            if (meter is String) {
              x = double.parse(meter) / 1000.0;
            } else if (meter is int) {
              x = meter.toDouble() / 1000.0;
            } else {
              x = meter / 1000.0;
            }
          }
        } else {
          // 没有里程，x轴用时间
          // x = dataSecondIndexes[index].toDouble();
          x = (record.second ?? 0) * (j / sampleIndexs.length);
        }

        // 计算y坐标
        if (heartRate!.length > index) {
          double heartRateY = 0;
          dynamic heart = heartRate[index];
          if (heart is int) {
            heartRateY = heart.toDouble();
          } else {
            heartRateY = heart;
          }
          heartRateY = (heartRateY * 10).ceil() / 10.0;
          heartSpots.add(FlSpot(x!, heartRateY));
        }
      }
    }
    return heartSpots;
  }

  List<FlSpot> _getPowerSpots(
      ChartType chartType,
      List<int> sampleIndexs,
      List? locationDetails,
      List? powerRate,
      RideRecord record,
      List dataSecondIndexes) {
    List<FlSpot> powerSpots = [];

    for (var j = 0; j < sampleIndexs.length; j++) {
      int index = sampleIndexs[j];

      if (locationDetails!.length > index) {
        //首先计算X坐标
        // 轨迹点：lat lng elevation speed meter
        List locationDetail = locationDetails[index];

        // 计算X坐标
        double? x = 0;
        if (chartType == ChartType.Meter) {
          // 有里程，x轴用里程
          dynamic meter = locationDetail[1];
          if (from == 'route') {
            x = (record.meter ?? 0) * (j / sampleIndexs.length) / 1000;
          } else {
            if (meter is String) {
              x = double.parse(meter) / 1000.0;
            } else if (meter is int) {
              x = meter.toDouble() / 1000.0;
            } else {
              x = meter / 1000.0;
            }
          }
        } else {
          // 没有里程，x轴用时间
          // x = dataSecondIndexes[index].toDouble();
          x = (record.second ?? 0) * (j / sampleIndexs.length);
        }

        // 计算y坐标
        if (powerRate!.length > index) {
          double powerRateY = 0;
          dynamic power = powerRate[index];
          if (power is int) {
            powerRateY = power.toDouble();
          } else {
            powerRateY = power;
          }
          powerRateY = (powerRateY * 10).ceil() / 10.0;
          if (powerRateY > 0) {
            powerSpots.add(FlSpot(x!, powerRateY));
          } else {
            if (index > 0 && powerSpots.isNotEmpty) {
              powerSpots.add(FlSpot(x!, powerSpots[powerSpots.length - 1].y));
            }
          }
        }
      }
    }
    return powerSpots;
  }

  List<FlSpot> _getAltitudeSpots(
      ChartType chartType,
      List<int> sampleIndexs,
      List? locationDetails,
      List? altitudes,
      RideRecord record,
      List dataSecondIndexes) {
    List<FlSpot> altitudeSpots = [];
    for (var j = 0; j < sampleIndexs.length; j++) {
      int index = sampleIndexs[j];

      //首先计算X坐标
      // 轨迹点：lat lng elevation speed meter
      if ((locationDetails?.length ?? 0) > index) {
        List locationDetail = locationDetails![index];

        // 计算X坐标
        double x = 0;
        if (chartType == ChartType.Meter) {
          // 有里程，x轴用里程
          dynamic meter = locationDetail[1];
          if (from == 'route') {
            x = (record.meter ?? 0) * (j / sampleIndexs.length) / 1000;
          } else {
            if (meter is String) {
              x = double.parse(meter) / 1000.0;
            } else if (meter is int) {
              x = meter.toDouble() / 1000.0;
            } else {
              x = meter / 1000.0;
            }
          }
        } else {
          // 没有里程，x轴用时间
          // x = dataSecondIndexes[index].toDouble();
          x = (record.second ?? 0) * (j / sampleIndexs.length);
        }

        // 计算y坐标
        if (altitudes!.length > index) {
          double altitudeY = 0;
          dynamic altitude = altitudes[index];
          if (altitude is String) {
            altitudeY = double.parse(altitude);
          } else if (altitude is int) {
            altitudeY = altitude.toDouble();
          } else {
            altitudeY = altitude;
          }
          altitudeY = (altitudeY * 10).ceil() / 10.0;
          altitudeSpots.add(FlSpot(x, altitudeY));
        } else {
          // android没有气压计，使用GPS海拔
          double? altitudeY = 0;
          dynamic altitude = locationDetail[0];
          if (altitude is String) {
            altitudeY = double.parse(altitude) / 100.0;
          } else if (altitude is int) {
            altitudeY = altitude.toDouble() / 100.0;
          } else {
            altitudeY = altitude / 100.0;
          }
          altitudeY = (altitudeY! * 100).ceil() / 100.0;
          altitudeSpots.add(FlSpot(x, altitudeY));
        }
      }
    }
    return altitudeSpots;
  }

  List<int> getSampleIndexs(
      ChartType chartType, List locations, int maxSampleCount) {
    int? sampLength = locations.length;
    if (chartType == ChartType.Meter) {
      sampLength = locations.length;
    } else {
      // ignore: unnecessary_statements
      sampLength = locations.length < (record!.second ?? 0)
          ? locations.length
          : (record!.second ?? 0);
    }
    // 抽样
    double step = sampLength / maxSampleCount.toDouble();
    if (step < 1.0) {
      // 数据点太少，不需要抽样
      step = 1.0;
    }
    Set<int> sampleIndexes = {};
    for (var i = 0; i < sampLength; i++) {
      int index = (i * step).toInt();
      if (index > (sampLength - 1)) {
        break;
      }
      sampleIndexes.add(index);
    }

    sampleIndexes.add(record?.maxSpeedIndex ?? 0);
    sampleIndexes.add(record?.minSpeedIndex ?? 0);

    sampleIndexes.add(record?.maxCadenceRateIndex ?? 0);
    sampleIndexes.add(record?.minCadenceRateIndex ?? 0);

    sampleIndexes.add(record?.maxHeartRateIndex ?? 0);
    sampleIndexes.add(record?.minHeartRateIndex ?? 0);

    sampleIndexes.add(record?.maxPowerRateIndex ?? 0);
    sampleIndexes.add(record?.minPowerRateIndex ?? 0);

    sampleIndexes.add(record?.maxAltitudeIndex ?? 0);
    sampleIndexes.add(record?.minAltitudeIndex ?? 0);

    if (sampleIndexes.last < (sampLength - 1)) {
      sampleIndexes.add(sampLength - 1);
    }
    List<int> result = sampleIndexes.toList();
    result.sort();
    return result;
  }
}
