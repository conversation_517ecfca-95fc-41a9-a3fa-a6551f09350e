import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:new_edge/util/location_utils.dart';
import 'package:permission_handler/permission_handler.dart';

class GeolocatorUtils {
//请求地理位置
  static Future<Position?> requestLocationPosition(BuildContext context) async {
    //附近 获取权限
    PermissionStatus permission =
        await LocationUtils.requestLocationPermission(context);
    if (permission == PermissionStatus.granted) {
      Position? position = await Geolocator.getLastKnownPosition();
      return position;
    }

    return null;
  }

  static double distanceBetween(Position start, Position end) {
    return Geolocator.distanceBetween(
        start.latitude, start.longitude, end.latitude, end.longitude);
  }
}
