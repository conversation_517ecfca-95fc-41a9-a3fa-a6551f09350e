import 'package:date_format/date_format.dart';
import 'package:intl/intl.dart';

String? formatSecToMin(var value) {
  if (value == 0) {
    return '0';
  } else {
    var num = value / 60;
    return num.toStringAsFixed(0);
  }
}

String? formatMinToH(var value) {
  if (value == 0) {
    return '0';
  } else {
    var num = value / 60;
    return num.toStringAsFixed(1);
  }
}

//米转千米
String? formatMToKm(var value, {int fractionDigits = 2}) {
  if (value == 0) {
    return '0';
  } else {
    var num = value / 100 / 10;
    return num.toStringAsFixed(fractionDigits);
  }
}

//秒转小时
String formatSecToH(double value, {int fractionDigits = 1}) {
  var num = value / 60 / 60;
  return num.toStringAsFixed(fractionDigits);
}

//m/s转km/h
String formatMSToKMH(double value) {
  var num = value * 3.6;
  return num.toStringAsFixed(1);
}

String formatSpeedMSToKMH(int second, int meter) {
  var seconds = meter / second;
  return formatMSToKMH(seconds);
}

//日期格式化
String formatdayTime(String? daytime) {
  if (daytime == null) {
    return '';
  }
  String year = daytime.substring(0, 4);
  String mouth = daytime.substring(4, 6);
  String day = daytime.substring(6, 8);
  return formatDate(DateTime(int.parse(year), int.parse(mouth), int.parse(day)),
      [mm, '.', dd]);
}

//时间格式化，根据总秒数转换为对应的 hh:mm:ss 格式
String? formatRideSeconds(int seconds) {
  var time = constructTime(seconds);
  return time["h"] + ":" + time["m"] + ":" + time["s"];
}

String? formatRideSecondsWithUnit(int seconds) {
  int days = seconds ~/ (24 * 3600);
  int hour = seconds % (24 * 3600) ~/ 3600;
  int minute = seconds % 3600 ~/ 60;

  return "${hour}h${formatTime(minute)}min";
}

//时间格式化，根据总秒数转换为对应的 hh:mm:ss 格式 4:04 4:44:34
String formatRideSecondsForChart(int seconds) {
  int days = seconds ~/ (24 * 3600);
  int hour = seconds % (24 * 3600) ~/ 3600;
  int minute = seconds % 3600 ~/ 60;
  int second = seconds % 60;

  var time = '';
  if (hour > 0) {
    time += hour.toString();
    time += ":" + formatTime(minute) + ":" + formatTime(second);
  } else {
    if (minute > 0) {
      time += minute.toString();
      time += ":" + formatTime(second);
    } else {
      time += second.toString();
    }
  }
  return time;
}

Map<String, dynamic> constructTime(int seconds) {
  // int days = seconds ~/ (24 * 3600);
  int hour = seconds ~/ 3600;
  int minute = seconds % 3600 ~/ 60;
  int second = seconds % 60;

  return {
    // 'd': days.toString(),
    'h': formatTime(hour),
    'm': formatTime(minute),
    's': formatTime(second),
  };
}

//数字格式化，将 0~9 的时间转换为 00~09
String formatTime(int timeNum) {
  return timeNum < 10 ? "0" + timeNum.toString() : timeNum.toString();
}

// "2020-02-02"
String formatDateForRecordDetail(int startTime) {
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  var date = formatDate(dateTime, [yyyy, "-", mm, "-", dd]);
  return date;
}

// "2020.12.25"
String formatDateForDotted(int startTime) {
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  var date = formatDate(dateTime, [yyyy, ".", mm, ".", dd]);
  return date;
}

// "2020-02-02 22:51"
String formatDateForRecordDetailTime(int startTime) {
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  var date = formatDate(dateTime, [yyyy, ".", mm, ".", dd]);
  var time = formatDate(dateTime, [HH, ":", nn]);
  return date + "  " + time;
}

// "2020.02"
String formatDateForRecordDayime(int startTime) {
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  var date = formatDate(dateTime, [yyyy, ".", mm]);
  return date;
}

// "7月14日"
String formatDateForRecordShareTime(int startTime) {
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  var date = formatDate(dateTime, [mm, "月", dd, "日"]);
  return date;
}

// "7月14日  22:51  周二"
String formatDateForBriefTime(int startTime) {
  // var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  // var date = formatDate(dateTime, [mm, "月", dd, "日"]);
  // var time = formatDate(dateTime, [HH, ":", nn]);
  // var weekday = weekdays[dateTime.weekday - 1];
  // return date + "  " + time + "  " + weekday;
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  var _formatDateForWhenTime = formatDateForWhenTime(startTime);
  var weekday = weekdays[dateTime.weekday - 1];
  return _formatDateForWhenTime + "  " + weekday;
}

// "7月14日  上午"
String formatDateForWhenTime(int? startTime) {
  if (startTime == null) {
    return '';
  }
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  var date = formatDate(dateTime, [mm, "月", dd, "日"]);
  var hour = formatDate(dateTime, [HH]);
  var when = formatWhenForDay(startTime);
  return date + " " + when;
}

String formatWhenForDay(int startTime) {
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime);
  int hour = dateTime.hour;
  if (hour < 6) {
    return "凌晨";
  } else if (hour < 11) {
    return "上午";
  } else if (hour < 13) {
    return "中午";
  } else if (hour < 19) {
    return "下午";
  } else {
    return "晚上";
  }
}

// "202009"
String formatTimeToMonth(int? startTime) {
  var dateTime = DateTime.fromMillisecondsSinceEpoch(startTime ?? 0);
  var date = formatDate(dateTime, [yyyy, "", mm]);
  return date;
}

String formatTimeOfDay(num startTime) {
  String formattedTime;
  var now = DateTime.now();
  var createTime = DateTime.fromMillisecondsSinceEpoch(startTime as int);
  var duration = now.difference(createTime);

  if (duration.inMinutes < 1) {
    formattedTime = '刚刚';
  } else if (duration.inMinutes < 60) {
    formattedTime = '${duration.inMinutes}分钟前';
  } else if (duration.inHours < 24 && now.day == createTime.day) {
    formattedTime = '${duration.inHours}小时前';
  } else if (duration.inHours < 48 && now.day != createTime.day) {
    formattedTime = '昨天';
  } else if (duration.inDays < 3) {
    formattedTime = '${duration.inDays}天前';
  } else {
    DateFormat dateFormat;
    if (now.year > createTime.year) {
      dateFormat = DateFormat('yyyy.MM.dd');
    } else {
      dateFormat = DateFormat('MM.dd');
    }
    formattedTime = dateFormat.format(createTime);
  }

  return formattedTime;
}

final List<String> weekdays = [
  "周一",
  "周二",
  "周三",
  "周四",
  "周五",
  "周六",
  "周日",
];

// String? breakWord(String? word) {
//   if (word == null || word.isEmpty) {
//     return word;
//   }
//   String breakWord = '';
//   word.runes.forEach((element) {
//     breakWord += String.fromCharCode(element);
//     breakWord += '\u200B';
//   });
//   return breakWord;
// }

String? replaceWord(String? word) {
  if (word == null || word.isEmpty) {
    return word;
  }
  print(word);
  String breakWord = word.replaceAll("IMIN", "EDGE");

  return breakWord;
}

String formatDuration(Duration duration, {bool includeMs = false}) {
  String twoDigits(int n) => n.toString().padLeft(2, '0');
  String threeDigits(int n) => n.toString().padLeft(3, '0');

  final hours = twoDigits(duration.inHours);
  final minutes = twoDigits(duration.inMinutes.remainder(60));
  final seconds = twoDigits(duration.inSeconds.remainder(60));
  final milliseconds = threeDigits(duration.inMilliseconds.remainder(1000));

  String formattedTime = '$hours:$minutes:$seconds';

  if (includeMs) {
    formattedTime += ".$milliseconds";
  }

  return formattedTime;
}

String maskPhoneNumber(String phoneNumber) {
  if (phoneNumber.length == 11) {
    // 使用substring方法获取需要的部分，并用'*'字符串替换中间四位
    return '${phoneNumber.substring(0, 3)}****${phoneNumber.substring(7)}';
  } else {
    // 如果号码长度不是11，则返回原号码或抛出异常
    return phoneNumber;
  }
}

DateTime parseTime(String? time) {
  if (time == null || time.isEmpty) {
    return DateTime.now();
  }
  DateTime date = DateTime.parse(time);
  return date;
}
