// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:new_edge/util/orientation_helper.dart';
//
// class OrientationUtils {
//   static DeviceOrientation currentOrientation = DeviceOrientation.portraitUp;
//
//   static List<Function(DeviceOrientation? orientation)> onOrientationChanges = [];
//
//   static DeviceOrientation? getUsableOrientation(DeviceOrientation? orientation) {
//     if (orientation == DeviceOrientation.portraitDown) {
//       return null;
//     }
// //    if (Platform.isIOS && orientation == DeviceOrientation.landscapeLeft) {
// //      return null;
// //    }
// //    if (Platform.isAndroid && orientation == DeviceOrientation.landscapeRight) {
// //      return null;
// //    }
// //    if (Platform.isAndroid) {
// //      if (orientation == DeviceOrientation.landscapeLeft) {
// //        orientation = DeviceOrientation.landscapeRight;
// //      } else if (orientation == DeviceOrientation.landscapeRight) {
// //        orientation = DeviceOrientation.landscapeLeft;
// //      }
// //    }
//     return orientation;
//   }
//
//   static Future switchPortrait() async {
//     if(Platform.isIOS){
//       await Future.delayed(Duration(milliseconds: 500), () {});
//     }
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.portraitUp,
//     ]);
//     print("switchPortrait");
//   }
//
//
//   static Future switchLandscape() async {
//     if (Platform.isAndroid) {
//       SystemChrome.setPreferredOrientations([
// //        DeviceOrientation.portraitUp,
// //        DeviceOrientation.landscapeLeft,
//         DeviceOrientation.landscapeRight,
//       ]);
//     } else {
//       SystemChrome.setPreferredOrientations([
// //        DeviceOrientation.portraitUp,
//         DeviceOrientation.landscapeRight,
//       ]);
//     }
//     print("switchLandscape");
//   }
//   static bool _openOrientationListener = false;
//
//   static DeviceOrientation? _cacheOrientation;
//
//   static void initOrientationListener() {
//     switchPortrait();
//     OrientationHelper.onOrientationChange!.listen((event) {
//       _cacheOrientation = event;
//       _handle(event);
//     });
//   }
//   static _handle(DeviceOrientation? orientation){
//     if (!_openOrientationListener) {
//       return;
//     }
//     print("OrientationUtils.listen $orientation");
//     orientation = OrientationUtils.getUsableOrientation(orientation);
//     if (orientation == null) {
//       print("orientation == null");
//       return;
//     }
//     currentOrientation = orientation;
//     SystemChrome.setPreferredOrientations([orientation]);
// //    print("onOrientationChange $orientation");
// //    navigatorKey.currentState.setState(() { });
//     onOrientationChanges.forEach((element) {
//       element(orientation);
//     });
// //    if (onOrientationChanges != null) {
// //      onOrientationChange(orientation);
// //    }
//   }
//
//   static void openOrientationListener() {
//     print("OrientationUtils.openOrientationListener");
//     _openOrientationListener = true;
//     _handle(_cacheOrientation);
//   }
//
//   static void closeOrientationListener() {
//     _openOrientationListener = false;
//     onOrientationChanges.clear();
//     switchPortrait();
//     print("OrientationUtils.closeOrientationListener");
//   }
//
//   static void lockOrientation() {
//     _openOrientationListener = false;
//   }
// //  switchLandscapeIos() {
// //    OrientationHelper.setPreferredOrientations(
// //        [DeviceOrientation.landscapeRight]).then((_) {
// //      OrientationHelper.forceOrientation(DeviceOrientation.landscapeRight);
// //    });
// //  }
// //
// //  switchPortraitIos() {
// //    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
// //        .then((_) {
// //      OrientationHelper.forceOrientation(DeviceOrientation.portraitUp);
// //    });
// //  }
// }
