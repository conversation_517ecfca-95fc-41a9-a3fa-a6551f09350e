import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart' as OkToast;

void showToast(String msg, BuildContext context,
    {OkToast.ToastPosition gravity = OkToast.ToastPosition.center,
    int? duration}) {
  // TODO 暂时这么处理

  msg = msg.replaceAll("Exception: ", "");
  if (msg.isEmpty) {
    print("showToast isEmpty");
    return;
  }
  print("showToast $msg");
  OkToast.showToast(msg, position: gravity);
}

void showSimpleToast(String msg,
    {OkToast.ToastPosition gravity = OkToast.ToastPosition.center,
    Duration? duration}) {
  msg = msg.replaceAll("Exception: ", "");
  if (msg.isEmpty) {
    print("showToast isEmpty");
    return;
  }
  print("showToast $msg");
  OkToast.showToast(msg, position: gravity, duration: duration);
}
