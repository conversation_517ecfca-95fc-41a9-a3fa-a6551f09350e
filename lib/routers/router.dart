//配置路由
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:new_edge/pages/address/user_address_binding.dart';
import 'package:new_edge/pages/address/user_address_page.dart';
import 'package:new_edge/pages/countries/country_select_binding.dart';
import 'package:new_edge/pages/countries/country_select_page.dart';
import 'package:new_edge/pages/games/activity/activity_detail_page.dart';
import 'package:new_edge/pages/games/challenge/challenge_detail_page.dart';
import 'package:new_edge/pages/games/competition/competition_detail_page.dart';
import 'package:new_edge/pages/games/competition/join/join_competition_detail_page.dart';
import 'package:new_edge/pages/personal/bike/accessories/bike_accessories_select.dart';
import 'package:new_edge/pages/personal/bike/bike_entirety_config.dart';
import 'package:new_edge/pages/personal/bike/bike_frame_brand.dart';
import 'package:new_edge/pages/personal/bike/bike_frame_model.dart';
import 'package:new_edge/pages/personal/bike/bike_frame_model_config.dart';
import 'package:new_edge/pages/personal/bike/bike_frame_series.dart';
import 'package:new_edge/pages/personal/bike/bike_history_list.dart';
import 'package:new_edge/pages/personal/bike/bike_other_frame_brand.dart';
import 'package:new_edge/pages/personal/bike/bike_preset_improve.dart';
import 'package:new_edge/pages/personal/bike/details/bike_details.dart';
import 'package:new_edge/pages/personal/bike/my_bike_list.dart';
import 'package:new_edge/pages/personal/customer_service.dart';
import 'package:new_edge/pages/personal/equipment/equipment.dart';
import 'package:new_edge/pages/personal/ride_history.dart';
import 'package:new_edge/pages/personal/settings/personal_edit.dart';
import 'package:new_edge/pages/personal/settings/phone_binding.dart';
import 'package:new_edge/pages/personal/settings/phone_validation.dart';
import 'package:new_edge/pages/personal/settings/phone_validation_twice.dart';
import 'package:new_edge/pages/personal/settings/settings.dart';
import 'package:new_edge/pages/personal/trophy/trophy_detail_page.dart';
import 'package:new_edge/pages/route/route_detail.dart';
import 'package:new_edge/pages/route/route_performance.dart';
import 'package:new_edge/pages/route/route_record.dart';

import '../pages/home_page.dart';
import '../pages/login/login_input_phone.dart';


class RouteGet {
  ///root page
  static const String home = "/home";
  static const String userAddress = "/userAddress";
  static const String countrySelect = "/countrySelect";
  static const String competitionDetail = "/competition/detail";
  static const String competitionJoinDetail = "/competition/JoinDetail";
  static const String challengeDetail = "/challenge/detail";
  static const String activityDetail = "/activity/detail";
  static const String trophyDetail = "/trophy/detail";

  ///pages map
  static final List<GetPage> getPages = [
    GetPage(
        name: userAddress,
        page: () => UserAddressPage(),
        binding: UserAddressBinding()
    ),
    GetPage(
        name: countrySelect,
        page: () => CountrySelectPage(),
        binding: CountrySelectBinding()
    ),
    GetPage(
        name: competitionDetail,
        page: () => CompetitionDetailPage(),
    ),
    GetPage(
      name: competitionJoinDetail,
      page: () => JoinCompetitionDetailPage(),
    ),
    GetPage(
      name: challengeDetail,
      page: () => ChallengeDetailPage(),
    ),
    GetPage(
      name: activityDetail,
      page: () => ActivityDetailPage(),
    ),
    GetPage(
      name: trophyDetail,
      page: () => TrophyDetailPage(),
    ),
  ];
}

final routes = {
  "/login": (context) => LoginInputPhonePage(),
  "/home": (context) => HomePage(),
  "/settings": (context) => SettingsPage(),//设置页
  "/customer": (context) => CustomerServicePage(),//联系客服
  "/personal/edit": (context) => PersonalEditPage(),//编辑资料
  "/phone/binding": (context) => PhoneBindingPage(),//修改手机号
  "/phone/validation": (context) => PhoneValidationPage(),//验证码验证
  "/phone/validation/twice": (context) => PhoneValidationTwicePage(),//绑定新手机号
  "/route/detail": (context) => RouteDetailPage(),//路段详情
  "/ride/history": (context) => RideHistoryPage(),//骑行记录
  "/equipment": (context) => EquipmentPage(),//码表列表
  "/bike/list": (context) => MyBikeListPage(),//我的自行车
  "/bike/history/list": (context) => BikeHistoryListPage(),//历史车辆
  "/bike/frame/brand": (context) => BikeFrameBrandPage(),//车架品牌
  "/bike/other/frame/brand": (context) => OtherFrameBrandPage(),//车架其它品牌
  "/bike/frame/series": (context) => BikeFrameSeriesPage(),//车架系列
  "/bike/frame/model": (context) => BikeFrameModelPage(),//车架型号
  "/bike/frame/model/config": (context) => BileFrameModelConfigPage(),//车架型号详细配置
  "/bike/entirety/config": (context) => BikeEntiretyConfigPage(),//整车配置
  "/bike/preset/improve": (context) => BikePresetImprove(),//完善自行车预设
  "/bike/details": (context) => BikeDetailsPage(),//自行车详情
  "/bike/accessories/select": (context) => BikeAccessoriesSelectPage(),//选择配件
  "/route/record": (context) => RouteRecordPage(),//我的路段
  "/route/performance": (context) => RoutePerformancePage(),//我的成绩
};





var onGenerateRoute = (RouteSettings settings) {
  var route = settings.name;
  // 如果是HTTP开头就跳转到浏览器
  if (route!.startsWith("http")) {
    route =
        "imin://www.imin-sports.com/web?url=" + Uri.encodeComponent(route);
  } else if (route.startsWith("imin://")) {
    // App原生，指定打开Flutter页面
    return openAppRoute(route);
  } else {
    return null;
  }
};

Route<dynamic>? openAppRoute(String route) {
  // 如果app没有初始化就跳转到初始化页面
//    if (!isAppInit()) {
//      return MaterialPageRoute(builder: (context) => InitAppAndOpenPage(route));
//    }
  var uri = Uri.parse(route);
  switch (uri.path) {
  // case "/report":
  //   {
  //     final courseId = int.parse(uri.queryParameters["courseId"]);
  //     return MaterialPageRoute(
  //         builder: (context) => ReportPage(courseId: courseId));
  //   }
  //   break;

    default:
      {
        //statements;
        return null;
      }
      break;
  }
  return null;
}
