import 'package:dio/dio.dart';
import 'package:new_edge/config/Api.dart';
import 'package:new_edge/model/achivement/achivement_detail_model.dart';
import 'package:new_edge/model/activity_info.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/trophy_info.dart';
import 'package:retrofit/retrofit.dart';

part 'trophy.g.dart';

@RestApi(baseUrl: Api.baseWithoutEdgeURL)
abstract class TrophyApi {
  factory TrophyApi(Dio dio, {String? baseUrl}) = _TrophyApi;

  ///查询个人奖杯获取记录
  @GET("/trophy/trophyRecord/queryRecordByUid4MobileV2")
  Future<ApiResponse<List<TrophyInfo>>> queryRecords({
    @Query("uid") int? uid,
  });

  ///查询个人奖杯获取详情
  @GET("/challenge_new/trophy/queryById")
  Future<ApiResponse<TrophyDetail>> getRecord({
    @Query("id") String? id,
  });
}
