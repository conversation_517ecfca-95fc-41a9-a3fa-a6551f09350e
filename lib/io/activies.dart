import 'package:dio/dio.dart';
import 'package:new_edge/config/Api.dart';
import 'package:new_edge/model/activies_banner.dart';
import 'package:new_edge/model/activies_join_member.dart';
import 'package:new_edge/model/activities_avg_speed.dart';
import 'package:new_edge/model/activities_challenge.dart';
import 'package:new_edge/model/activities_challenge_reward_distribution_records.dart';
import 'package:new_edge/model/activities_challenge_store_reward.dart';
import 'package:new_edge/model/activities_discount_code.dart';
import 'package:new_edge/model/activities_event.dart';
import 'package:new_edge/model/activities_joined_event.dart';
import 'package:new_edge/model/activities_race.dart';
import 'package:new_edge/model/activities_race_bag.dart';
import 'package:new_edge/model/activities_race_my_result.dart';
import 'package:new_edge/model/activities_race_order.dart';
import 'package:new_edge/model/activities_race_prize_record.dart';
import 'package:new_edge/model/activities_race_prize_record_submit.dart';
import 'package:new_edge/model/activities_race_product.dart';
import 'package:new_edge/model/activities_race_product_attribute.dart';
import 'package:new_edge/model/activities_race_product_sku.dart';
import 'package:new_edge/model/activities_race_registration_record.dart';
import 'package:new_edge/model/activities_race_registration_record_detail.dart';
import 'package:new_edge/model/activities_race_submit.dart';
import 'package:new_edge/model/activities_race_user_result.dart';
import 'package:new_edge/model/activities_user_challenge.dart';
import 'package:new_edge/model/activities_white_price.dart';
import 'package:new_edge/model/activity_media_request.dart';
import 'package:new_edge/model/activity_photo.dart';
import 'package:new_edge/model/activity_video.dart';
import 'package:new_edge/model/api_response/api_paging_entity.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/bike_entire.dart';
import 'package:new_edge/model/challenge_detail.dart';
import 'package:new_edge/model/challenge_info.dart';
import 'package:new_edge/model/challenge_lucky_drawa.dart';
import 'package:new_edge/model/challenge_lucky_drawa_record.dart';
import 'package:new_edge/model/country_info.dart';
import 'package:new_edge/model/join_challenge.dart';
import 'package:new_edge/model/joined_statistic.dart';
import 'package:new_edge/model/nearby_shop.dart';
import 'package:new_edge/model/payment_order.dart';
import 'package:new_edge/model/photo_system.dart';
import 'package:new_edge/model/prize_claim_form.dart';
import 'package:new_edge/model/reward_city.dart';
import 'package:new_edge/model/reward_distribution_record.dart';
import 'package:new_edge/model/reward_shop.dart';
import 'package:new_edge/model/ride_brief.dart';
import 'package:new_edge/model/route_info.dart';
import 'package:retrofit/retrofit.dart';

import '../model/activities_challenge_assistant_distrobution_records_detail.dart';
import '../model/activities_challenge_store_reward_statistic.dart';
import '../model/activities_challenge_store_reward_statistic_detail.dart';

part 'activies.g.dart';

@RestApi(baseUrl: Api.baseWithoutEdgeURL)
abstract class ActiviesApi {
  factory ActiviesApi(Dio dio, {String? baseUrl}) = _ActiviesApi;

  ///活动_轮播图
  @GET("/activities/activiesBanner/list")
  Future<ApiResponse<ApiPaging<ActiviesBanner>>> getActiviesBannerList({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
    @Query("type") String? type,
  });

  ///活动_挑战_用户挑战
  @GET(
      "/challenge_new/challengeSignUp/findUnderwayJoinChallenge")
  Future<ApiResponse<ApiPaging<JoinChallenge>>>
      getActivitiesUserChallengeList({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
    @Query("challengeId") int? challengeId,
  });

  ///活动_挑战_用户挑战-我的
  @GET(
      "/challenge_new/challengeSignUp/findAllJoinChallenge")
  Future<ApiResponse<ApiPaging<JoinChallenge>>>
      listActivitiesJoinedChallenges({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
    @Query("tenantId") int? tenantId,
  });

  ///活动_挑战_列表
  @GET("/challenge_new/challengeInfo/list")
  Future<ApiResponse<ApiPaging<ChallengeInfo>>> getActiviesChallengeList({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
    @Query("tenantId") String? tenantId
  });

  ///活动_挑战_列表
  @GET("/activities/activitiesChallengeUserShopLink/queryPageLinkedChallengeList")
  Future<ApiResponse<ApiPaging<ChallengeDetail>>> queryPageLinkedChallengeList({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize
  });

  ///活动_挑战
  @GET("/challenge_new/challengeInfo/queryById")
  Future<ApiResponse<ChallengeDetail>> getActivitiesChallenge({
    @Query("id") String? id,
  });

  ///活动-挑战运费订单
  @POST("/activities/activitiesChallengeDeliveryFeeOrder/pay4Mobile")
  Future<ApiResponse<String>> payDeliveryFee({
    @Query("challengeId") String? challengeId
  });


  ///活动-挑战运费订单
  @POST("/activities/activitiesChallengeDeliveryFeeOrder/checkPayStatus4Mobile")
  Future<ApiResponse<bool>> checkPayDeliveryStatus({
    @Query("challengeId") String? challengeId
  });

  ///活动_加入
  @POST("/challenge_new/challengeSignUp/joinChallenge")
  Future<ApiResponse<String>> joinChallenge({
    @Query("challengeId") String? challengeId,
    @Query("discountCode") String? discountCode,
  });

  ///国家信息
  @GET("/activities/countries/list")
  Future<ApiResponse<ApiPaging<CountryInfo>>> getCountries({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
    @Query("id") String? id,
  });

  ///活动_赛事_列表
  @GET("/activities/activitiesRace/list4Mobile")
  Future<ApiResponse<ApiPaging<ActivitiesRace>>> getActivitiesRaceList({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///活动_赛事_组别列表
  @GET("/activities/activitiesRaceGroup/list4Mobile")
  Future<ApiResponse<ApiPaging<ActivitiesRaceGroup>>> queryActivitiesRaceGroup({
    @Query("activitiesRaceId") String? activitiesRaceId,
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///活动_赛事_组别
  @GET("/activities/activitiesRaceGroup/queryById4Mobile")
  Future<ApiResponse<ActivitiesRaceGroup>> getActivitiesRaceGroup({
    @Query("id") String? id,
  });

  ///活动_赛事
  @GET("/activities/activitiesRace/queryById4Mobile")
  Future<ApiResponse<ActivitiesRace>> queryActivitiesRace({
    @Query("id") String? id,
  });

  ///活动_赛事_配套产品-移动端根据赛事id
  @GET("/activities/activitiesRaceProducts/queryByRaceId4Mobile")
  Future<ApiResponse<List<ActivitiesRaceProduct>>> queryProductByRaceId({
    @Query("raceId") String? raceId,
    @Query("groupId ") String? groupId,
  });

  ///活动_赛事_配套产品-移动端根据产品id
  @GET("/activities/activitiesRaceProductSku/queryByRaceId4Mobile")
  Future<ApiResponse<List<ActivitiesRaceProduct>>> querySkuByRaceId({
    @Query("raceId") String? raceId,
  });

  ///活动_赛事_配套产品-属性信息
  @GET("/activities/activitiesRaceProductAttributes/queryAttributes4Mobile")
  Future<ApiResponse<List<ActivitiesRaceProductAttribute>>>
      queryProductAttributes({
    @Query("productId") String? productId,
  });

  ///活动_赛事_配套产品-sku
  @GET("/activities/activitiesRaceProductSku/queryByRaceId4Mobile")
  Future<ApiResponse<List<ActivitiesRaceProductSku>>> queryProductSku({
    @Query("productId") String? productId,
  });

  ///活动_赛事_配套产品-sku
  @GET("/activities/activitiesDiscountCode/queryByDiscountCode4Mobile")
  Future<ApiResponse<ActivitiesDiscountCode>> queryDiscountCode({
    @Query("discountCode") String? discountCode,
  });

  ///活动_赛事_配套产品-sku
  @POST("/activities/activitiesRace/submitRegistrationinfo4Mobile")
  Future<ApiResponse<String>> submitRegistration(
      {@Body() ActivitiesRaceSubmit? data});

  ///活动_赛事_已报名的组别
  @GET("/activities/activitiesRaceRegistrationRecord/getJoinedGroup4Mobile")
  Future<ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>>
      getRaceJoinedGroup({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///活动_赛事_已报名的组别-我的
  @GET("/activities/activitiesRaceRegistrationRecord/listJoinedGroup4Mobile")
  Future<ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>>
      listRaceJoinedGroup({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///活动_赛事_已报名详情
  @GET(
      "/activities/activitiesRaceRegistrationRecord/getRegisteredGroupDetail4Mobile")
  Future<ApiResponse<ActivitiesRaceRegistrationRecordDetail>>
      queryRegistrationRecordDetail({
    @Query("raceId") String? raceId,
  });

  ///订单查询
  @GET("/payment/paymentOrder/queryByOrderNumber")
  Future<ApiResponse<PaymentOrder>> queryByOrderNumber({
    @Query("orderNumber") String? orderNumber,
  });

  @GET("/activities/activitiesRaceGroupRoute/queryByGroupId4Mobile")
  Future<ApiResponse<ActivitiesRaceGroupRoute>> queryGroupRoute({
    @Query("id") String? groupId,
  });

  ///活动_挑战_报名记录骑行记录
  @GET("/activities/activitiesChallengeRegistrationPostRide/listUserRideBrief")
  Future<ApiResponse<List<RideBrief>>> getActivitiesRideBrief({
    @Query("registrationRecordId") String? registrationRecordId,
  });

  ///活动_挑战_添加领奖记录
  @POST("/challenge_new/rewardDistributionRecords/add")
  Future<ApiResponse<PrizeClaimForm>> addRewardDistributionRecords({
    @Body() RewardDistributionRecord? data,
  });

  ///活动_挑战_获取领奖记录
  @GET(
      "/challenge_new/prizeClaimForm/queryById")
  Future<ApiResponse<PrizeClaimForm>>
      getRewardDistributionRecords({
    @Query("id") String? prizeClaimFormId,
  });

  ///查询需要发货的商品订单数据
  @GET("/activities/activitiesRaceProductsOrder/queryByUidRaceId4Mobile")
  Future<ApiResponse<List<ActivitiesRaceOrder>>>
      getActivitiesRaceProductsOrders({
    @Query("raceId") String? raceId,
  });

  ///查看个人比赛成绩
  @GET("/activities/activitiesRaceResult/queryAllResultByUid4Mobile")
  Future<ApiResponse<List<ActivityRaceMyResult>>>
      getActivitiesMyRaceResultList({
    @Query("groupId") String? groupId,
  });

  ///查看个人比赛总成绩
  @GET("/activities/activitiesRaceResult/queryGeneralResultByUid4Mobile")
  Future<ApiResponse<ActivityRaceMyResult?>> getActivitiesMyRaceResult({
    @Query("groupId") String? groupId,
  });

  ///查看榜单数据
  @GET("/activities/activitiesRaceResult/list4Mobile")
  Future<ApiResponse<ApiPaging<ActivityRaceUserResult>>>
      getActivitiesRaceResultList({
    @Query("resultTypeId") String? resultTypeId,
    @Query("groupId") String? groupId,
    @Query("type") String? type,
    @Query("uid") int? uid,
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///查询物品列表
  @GET("/activities/activitiesRaceBag/queryByGroupId4Mobile")
  Future<ApiResponse<List<ActivitiesRaceBag>>> getActivitiesRaceBags({
    @Query("groupId") String? groupId,
  });

  ///活动_列表
  @GET("/activities/activitiesEvents/list4Mobile")
  Future<ApiResponse<ApiPaging<ActivitiesEvent>>> getActivitiesEventList({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///活动
  @GET("/activities/activitiesEvents/queryById4Mobile")
  Future<ApiResponse<ActivitiesEvent>> getActivitiesEvent({
    @Query("id") String? id,
  });

  ///活动
  @GET("/activities/activitiesEventsStagesRoute/queryById4Mobile")
  Future<ApiResponse<RouteInfo>> getActivitiesRouteInfo({
    @Query("id") String? id,
  });

  ///活动-已加入
  @GET("/activities/activitiesEventsRegistrationRecord/getJoinedEvents4Mobile")
  Future<ApiResponse<ApiPaging<ActivitiesJoinedEvent>>>
      getActivitiesJoinedEvents({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///活动-已加入-我的
  @GET("/activities/activitiesEventsRegistrationRecord/listJoinedEvents4Mobile")
  Future<ApiResponse<ApiPaging<ActivitiesJoinedEvent>>>
      listActivitiesJoinedEvents({
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///活动-加入活动
  @POST("/activities/activitiesEvents/join4Mobile")
  Future<ApiResponse<String>> joinEvent({
    @Query("eventId") String? eventId,
    @Query("discountCode") String? discountCode,
  });

  @GET(
      "/activities/activitiesRaceRegistrationRecord/getJoinedGroupStatistic4Mobile")
  Future<ApiResponse<JoinedStatistic>> getJoinedGroupStatistic();

  @GET(
      "/activities/activitiesChallengeRegistrationRecord/getJoinedChallengesStatistic4Mobile")
  Future<ApiResponse<JoinedStatistic>> getJoinedChallengesStatistic();

  @GET(
      "/activities/activitiesEventsRegistrationRecord/getJoinedEventsStatistic4Mobile")
  Future<ApiResponse<JoinedStatistic>> getJoinedEventsStatistic();

  ///活动_查询奖金
  @GET("/activities/activitiesRacePrizeRecord/queryRecord")
  Future<ApiResponse<ActivityRacePrizeRecord>> getPrizeRecode({
    @Query("groupId") String? groupId,
    @Query("raceId") String? raceId,
    @Query("uid") int? uid,
  });

  ///活动_领取
  @POST("/activities/activitiesRacePrizeRecord/submitPrizeClaim")
  Future<ApiResponse<ActivityRacePrizeRecord>> submitPrizeClaim(
      {@Body() ActivitiesRacePrizeRecordSubmit? data});

  @GET(
      "/activities/activitiesChallengeRegistrationRecord/confirmCompletion4Mobile")
  Future<ApiResponse<String>> confirmCompletion({
    @Query("registrationRecordId") String? registrationRecordId,
  });


  @GET(
      "/activities/activitiesChallengeRegistrationPostRide/queryViableChallenges")
  Future<ApiResponse<List<ChallengeInfo>>> queryViableChallenges({
    @Query("postRideId") int? postRideId,
  });

  @GET(
      "/activities/activitiesChallengeRegistrationPostRide/changeChallenges")
  Future<ApiResponse<String>> changeChallenges({
    @Query("newChallengeId") String? newChallengeId,
    @Query("postRideId") int? postRideId,
  });

  ///点击抽奖
  @GET("/challenge_new/luckyDrawa/clickToDraw")
  Future<ApiResponse<PrizeInfo>> clickToDraw({
    @Query("luckyDrawaId") String? luckyDrawaId,
  });

  ///查询抽奖详情
  @GET(
      "/challenge_new/luckyDrawa/queryDetailByChallengeId")
  Future<ApiResponse<ChallengeLuckyDrawa>> queryDrawaDetailByChallengeId({
    @Query("challengeId") String? challengeId,
    @Query("uid") String? uid,
  });

  ///活动_查询已报名用户
  @GET("/activities/activitiesEventsRegistrationRecord/listUser4Mobile")
  Future<ApiResponse<ApiPaging<ActiviesJoinMember>>> getActiviesEventsRegistrationRecord({
    @Query("eventId") String? eventId,
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///查询自行车快照
  @GET("/activities/activitiesEventsUserBikeSnapshot/queryUserSnapshot")
  Future<ApiResponse<BikeEntire>> queryUserSnapshot(
      {@Query("bikeSnapshotId") String? bikeSnapshotId}); //快照id

  ///查询附近店铺
  @GET("/challenge_new/store/nearby")
  Future<ApiResponse<List<ShopInfo>>?> getNearShopList({
    @Query("prizeId") String? prizeId,
    @Query("tenantId") int? tenantId,
    @Query("latitude") String? latitude,
    @Query("longitude") String? longitude,
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize
  });

  ///根据活动id和城市信息查关联店铺信息
  @GET("/challenge_new/store/nearby")
  Future<ApiResponse<List<ShopInfo>>?> getShopListByCity({
    @Query("challengeId") String? challengeId,
    @Query("latitude") String? latitude,
    @Query("longitude") String? longitude,
    @Query("cityName") String? cityName,
  });

  ///检查是否绑定店铺
  @GET("/activities/activitiesChallengeRewardShopLink/checkShopBinding")
  Future<ApiResponse<bool>?> checkShopBinding({
    @Query("challengeId") String? challengeId,
  });

  ///支持领奖的城市列表
  @GET("/activities/activitiesChallengeRewardShopLink/getCityList")
  Future<ApiResponse<List<RewardCity>>?> getCityList({
    @Query("challengeId") String? challengeId,
    @Query("latitude") String? latitude,
    @Query("longitude") String? longitude,
  });

  ///根据二维码查询奖品发放详情
  @GET("/activities/activitiesChallengeRewardShopLink/queryByQrCodeStr")
  Future<ApiResponse<ActivitiesChallengeStoreReward>> queryChallengeReward({
    @Query("scannedUid") String? scannedUid,
    @Query("acPhysicalRewardId") String? acPhysicalRewardId,
  });

  ///根据二维码查询奖品发放详情
  @GET("/activities/activitiesChallengeRewardShopLink/queryByQrCodeStr4CurrentShop")
  Future<ApiResponse<ActivitiesChallengeAssistantDistrobutionRecordsDetail>> queryByQrCodeStr4CurrentShop({
    @Query("scannedUid") String? scannedUid,
    @Query("acPhysicalRewardId") String? acPhysicalRewardId,
  });

  ///工作人员发放奖品
  @GET("/activities/activitiesChallengeRewardShopLink/distributeV2")
  Future<ApiResponse<String>?> distributeChallengeReward({
    @Query("distributionRecordId") String? distributionRecordId,
    @Query("toUid") String? toUid,
  });

  ///扫描二维码
  @GET("/edgeOs/api/scanQrCode")
  Future<ApiResponse<int>?> scanQrCode({
    @Query("qrCodeStr") String? qrCodeStr,
  });

  ///查询均速选项列表
  @GET("/activities/activitiesRaceAvgSpd/queryByGroupId")
  Future<ApiResponse<List<ActivitiesAvgSpeed>>> queryAvgSpeedByGroupId({
    @Query("groupId") String? groupId,
  });

  ///根据关联id查询优惠
  @GET("/activities/activitiesWhiteList/queryByLinkedId")
  Future<ApiResponse<ActivitiesWhitePrice>> queryWhiteByLinkedId({
    @Query("linkedId") String? linkedId,
  });

  ///店铺关联待发放奖品
  @GET("/activities/activitiesChallengeStore/queryStoreRewardStatistic")
  Future<ApiResponse<List<ActivitiesChallengeStoreRewardStatistic>>> queryStoreRewardStatistic({
    @Query("challengeId") String? challengeId,
  });

  ///查询店铺奖品用户列表
  @GET("/activities/activitiesChallengeStore/queryStoreRewardUserList")
  Future<ApiResponse<ApiPaging<ActivitiesChallengeStoreRewardStatisticDetail>>> queryStoreRewardUserList({
    @Query("challengeId") String? challengeId,
    @Query("shopId") String? shopId,
    @Query("rewardId") String? rewardId,
    @Query("distributionStatus") String? distributionStatus,
    @Query("pageNo") int? pageNum,
    @Query("pageSize") int? pageSize
  });

  ///活动图片
  @POST("/wx/activities/pictures")
  Future<ApiResponse<ApiPaging<ActivityPhoto>>> pictures({
  @Body() ActivityMediaRequest? data});

  ///活动视频
  @POST("/wx/activities/videos")
  Future<ApiResponse<ApiPaging<ActivityVideo>>> videos({@Body() ActivityMediaRequest? data});
}
