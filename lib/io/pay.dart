import 'package:dio/dio.dart';
import 'package:new_edge/config/Api.dart';
import 'package:new_edge/model/api_response/api_response_entity.dart';
import 'package:new_edge/model/wx_pay.dart';
import 'package:retrofit/retrofit.dart';

part 'pay.g.dart';

@RestApi(baseUrl: Api.basePayURL)
abstract class PayApi {
  factory PayApi(Dio dio, {String? baseUrl}) = _PayApi;

  ///微信支付
  @POST("/wxPay/appOrderPay")
  @FormUrlEncoded()
  Future<ApiResponse<WxPay>> wxPay({
    @Query("orderNumber") String? orderNumber,
    @Query("orderType") int? orderType,
    @Query("platform") String? platform = "app",
    @Query("uid") int? uid,
  });

  ///支付宝支付
  @POST("/aliPay/appOrderPay")
  @FormUrlEncoded()
  Future<ApiResponse<String>> aliPay({
    @Query("orderNumber") String? orderNumber,
    @Query("orderType") int? orderType,
    @Query("uid") int? uid,
  });

}
