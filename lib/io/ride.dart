
import 'package:dio/dio.dart';
import 'package:new_edge/config/Api.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/model/bike_entire.dart';
import 'package:new_edge/model/community_ride_record.dart';
import 'package:new_edge/model/ride_brief.dart';
import 'package:new_edge/model/ride_detail.dart';
import 'package:new_edge/model/ride_month_stat.dart';
import 'package:new_edge/model/ride_stat_info.dart';
import 'package:new_edge/model/ride_upload_result.dart';
import 'package:new_edge/model/route_detail_chart.dart';
import 'package:retrofit/retrofit.dart';

part 'ride.g.dart';

/// http://yapi.thejoyrun.com/project/906/interface/api/cat_2163
@RestApi(baseUrl: Api.baseURL)
abstract class RideApi {
  factory RideApi(Dio dio, {String? baseUrl}) = _RideApi;

  /// 上传轨迹
  @POST("/ride/modify/uploadRideRecord")
  @FormUrlEncoded()
  Future<RideIploadResult> uploadRideRecord({
    @Field() int? meter,
    @Field() int? second,
    @Field() double? maxSpeed,
    @Field() int? climb,
    @Field() int? startTime,
    @Field() int? endTime,
    @Field() int? sampleInterval,
    @Field() String? rideId,
    @Field() int? wgs,
    @Field() String? province,
    @Field() String? city,
    @Field() String? content,
    @Field() String? altitude,
    @Field() String? pauseTime,
    @Field() String? locationDetail,
    @Field() String? coverImg,
  });

  @POST("/ride/modify/uploadRideRecordImg")
  @FormUrlEncoded()
  Future<bool> uploadRideRecordImg({
    @Field() String? rideId,
    @Field() String? coverMapImg,
  });

  ///查询总骑行记录信息
  @GET("/ride/query/getRideStatInfo")
  Future<RideStatInfo> getRideStatInfo();

  ///查询月度骑行列表信息
  @GET("/ride/query/getRideMonthStatList")
  Future<List<RideMonthStatInfo>> getRideMonthStatList();

  ///查询骑行列表信息
  @GET("/ride/query/getRideBriefList")
  Future<List<RideBrief>> getRideBriefList({
    @Query("pageNum") int? pageNum,
    @Query("pageSize") int? pageSize,
  });

  ///查询月度骑行记录概要信息列表
  @GET("/ride/query/getRideBriefListByMonth")
  Future<List<RideBrief>> getRideBriefListByMonth({
    @Query("month") String? month,
  });

  ///查询时间重复轨迹列表
  @GET("/ride/query/getConflictRecordList")
  Future<List<RideBrief>> getConflictRecordList({
    @Query("postRideId") int? postRideId,
  });

  /// 设置有效的时间冲突骑行轨迹
  @POST("/ride/modify/setValidConflictRecord")
  @FormUrlEncoded()
  Future<void> setValidConflictRecord({
    @Field() int? postRideId,
  });

  ///查询月度骑行记录概要信息列表
  @GET("/ride/query/getRideDetail")
  Future<RideDetail> getRideDetail({
    @Query("postRideId") int? postRideId,
  });

  /// 获取手机验证码
  @POST("/ride/modify/deleteRideRecord")
  @FormUrlEncoded()
  Future<void> deleteRideRecord({
    @Field() int? postRideId,
  });

  /// 获取手机验证码
  @POST("/ride/modify/updateBindingBike")
  @FormUrlEncoded()
  Future<void> updateBindingBike({
    @Field() int? postRideId,
    @Field() int? userBikeId,
  });

  ///查询自行车快照
  @GET("/ride/query/queryUserSnapshot")
  Future<BikeEntire> queryUserSnapshot(
      {@Query("postRideId") int? postRideId}); //轨迹id

  ///查询骑行记录中路段图表详情
  @GET("/ride/query/getRideRouteChartDetail")
  Future<RouteDetailChart> getRideRouteChartDetail({
    @Query("routeId") int? routeId, //路段ID
    @Query("postRideId") int? postRideId, //骑行业务ID
    @Query("rideCheckinTime") int? rideCheckinTime, //第几次匹配成绩
  });

  ///查询未读的同步数据
  @GET("/ride/query/getUnreadSynchronizeInfo")
  Future getUnreadSynchronizeInfo(@Query("uid") int uid);

  /// 清除未读同步数据
  @POST("/ride/modify/cleanUnreadSynchronizeInfo")
  @FormUrlEncoded()
  Future<int> cleanUnreadSynchronizeInfo();

  ///骑行记录
  @GET("/ride/query/getRideBriefListByYear")
  Future<List<CommunityRideRecord>> getRideBriefListByYear({
    @Query("pageNum") int? pageNum,
    @Query("pageSize") int? pageSize,
    @Query("year") int? year,
  });

  ///查询骑行年份
  @GET("/ride/query/getRideYearStatList")
  Future<List<int>> getRideYearStatList();

  ///轨迹数据隐私设置
  @POST("/ride/modify/setRideParamPrivacy")
  @FormUrlEncoded()
  Future<bool> setRideParamPrivacy({
    @Field() required int postRideId,
    @Field() required int heartRateStatus,
    @Field() required int cadenceStatus,
    @Field() required int powerStatus,
  });
}
