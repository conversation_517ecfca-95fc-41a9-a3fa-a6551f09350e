// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activies.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations

class _ActiviesApi implements ActiviesApi {
  _ActiviesApi(this._dio, {this.baseUrl, this.errorLogger}) {
    baseUrl ??= 'https://cycling.edgecycling.cn';
  }

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<ApiResponse<ApiPaging<ActiviesBanner>>> getActiviesBannerList({
    int? pageNum,
    int? pageSize,
    String? type,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
      r'type': type,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<ActiviesBanner>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activiesBanner/list',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActiviesBanner>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActiviesBanner>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActiviesBanner>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActiviesBanner.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<JoinChallenge>>> getActivitiesUserChallengeList({
    int? pageNum,
    int? pageSize,
    int? challengeId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
      r'challengeId': challengeId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<JoinChallenge>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/challengeSignUp/findUnderwayJoinChallenge',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<JoinChallenge>> _value;
    try {
      _value = ApiResponse<ApiPaging<JoinChallenge>>.fromJson(
        _result.data!,
        (json) => ApiPaging<JoinChallenge>.fromJson(
          json as Map<String, dynamic>,
          (json) => JoinChallenge.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<JoinChallenge>>> listActivitiesJoinedChallenges({
    int? pageNum,
    int? pageSize,
    int? tenantId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
      r'tenantId': tenantId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<JoinChallenge>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/challengeSignUp/findAllJoinChallenge',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<JoinChallenge>> _value;
    try {
      _value = ApiResponse<ApiPaging<JoinChallenge>>.fromJson(
        _result.data!,
        (json) => ApiPaging<JoinChallenge>.fromJson(
          json as Map<String, dynamic>,
          (json) => JoinChallenge.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ChallengeInfo>>> getActiviesChallengeList({
    int? pageNum,
    int? pageSize,
    String? tenantId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
      r'tenantId': tenantId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<ChallengeInfo>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/challengeInfo/list',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ChallengeInfo>> _value;
    try {
      _value = ApiResponse<ApiPaging<ChallengeInfo>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ChallengeInfo>.fromJson(
          json as Map<String, dynamic>,
          (json) => ChallengeInfo.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ChallengeDetail>>> queryPageLinkedChallengeList({
    int? pageNum,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<ChallengeDetail>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeUserShopLink/queryPageLinkedChallengeList',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ChallengeDetail>> _value;
    try {
      _value = ApiResponse<ApiPaging<ChallengeDetail>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ChallengeDetail>.fromJson(
          json as Map<String, dynamic>,
          (json) => ChallengeDetail.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ChallengeDetail>> getActivitiesChallenge({
    String? id,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'id': id};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ChallengeDetail>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/challengeInfo/queryById',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ChallengeDetail> _value;
    try {
      _value = ApiResponse<ChallengeDetail>.fromJson(
        _result.data!,
        (json) => ChallengeDetail.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<String>> payDeliveryFee({String? challengeId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'challengeId': challengeId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<String>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeDeliveryFeeOrder/pay4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<String> _value;
    try {
      _value = ApiResponse<String>.fromJson(
        _result.data!,
        (json) => json as String,
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<bool>> checkPayDeliveryStatus({
    String? challengeId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'challengeId': challengeId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<bool>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeDeliveryFeeOrder/checkPayStatus4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<bool> _value;
    try {
      _value = ApiResponse<bool>.fromJson(
        _result.data!,
        (json) => json as bool,
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<String>> joinChallenge({
    String? challengeId,
    String? discountCode,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'challengeId': challengeId,
      r'discountCode': discountCode,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<String>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/challengeSignUp/joinChallenge',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<String> _value;
    try {
      _value = ApiResponse<String>.fromJson(
        _result.data!,
        (json) => json as String,
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<CountryInfo>>> getCountries({
    int? pageNum,
    int? pageSize,
    String? id,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
      r'id': id,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<CountryInfo>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/countries/list',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<CountryInfo>> _value;
    try {
      _value = ApiResponse<ApiPaging<CountryInfo>>.fromJson(
        _result.data!,
        (json) => ApiPaging<CountryInfo>.fromJson(
          json as Map<String, dynamic>,
          (json) => CountryInfo.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivitiesRace>>> getActivitiesRaceList({
    int? pageNum,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<ActivitiesRace>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRace/list4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivitiesRace>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActivitiesRace>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivitiesRace>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActivitiesRace.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivitiesRaceGroup>>> queryActivitiesRaceGroup({
    String? activitiesRaceId,
    int? pageNum,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'activitiesRaceId': activitiesRaceId,
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<ApiPaging<ActivitiesRaceGroup>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceGroup/list4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(
            baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
          ),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivitiesRaceGroup>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActivitiesRaceGroup>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivitiesRaceGroup>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActivitiesRaceGroup.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesRaceGroup>> getActivitiesRaceGroup({
    String? id,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'id': id};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ActivitiesRaceGroup>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceGroup/queryById4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesRaceGroup> _value;
    try {
      _value = ApiResponse<ActivitiesRaceGroup>.fromJson(
        _result.data!,
        (json) => ActivitiesRaceGroup.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesRace>> queryActivitiesRace({String? id}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'id': id};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ActivitiesRace>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRace/queryById4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesRace> _value;
    try {
      _value = ApiResponse<ActivitiesRace>.fromJson(
        _result.data!,
        (json) => ActivitiesRace.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivitiesRaceProduct>>> queryProductByRaceId({
    String? raceId,
    String? groupId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'raceId': raceId,
      r'groupId ': groupId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ActivitiesRaceProduct>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceProducts/queryByRaceId4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivitiesRaceProduct>> _value;
    try {
      _value = ApiResponse<List<ActivitiesRaceProduct>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivitiesRaceProduct>(
                  (i) => ActivitiesRaceProduct.fromJson(
                    i as Map<String, dynamic>,
                  ),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivitiesRaceProduct>>> querySkuByRaceId({
    String? raceId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'raceId': raceId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ActivitiesRaceProduct>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceProductSku/queryByRaceId4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivitiesRaceProduct>> _value;
    try {
      _value = ApiResponse<List<ActivitiesRaceProduct>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivitiesRaceProduct>(
                  (i) => ActivitiesRaceProduct.fromJson(
                    i as Map<String, dynamic>,
                  ),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivitiesRaceProductAttribute>>>
      queryProductAttributes({String? productId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'productId': productId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<List<ActivitiesRaceProductAttribute>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceProductAttributes/queryAttributes4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivitiesRaceProductAttribute>> _value;
    try {
      _value = ApiResponse<List<ActivitiesRaceProductAttribute>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivitiesRaceProductAttribute>(
                  (i) => ActivitiesRaceProductAttribute.fromJson(
                    i as Map<String, dynamic>,
                  ),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivitiesRaceProductSku>>> queryProductSku({
    String? productId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'productId': productId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<List<ActivitiesRaceProductSku>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceProductSku/queryByRaceId4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(
            baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
          ),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivitiesRaceProductSku>> _value;
    try {
      _value = ApiResponse<List<ActivitiesRaceProductSku>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivitiesRaceProductSku>(
                  (i) => ActivitiesRaceProductSku.fromJson(
                    i as Map<String, dynamic>,
                  ),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesDiscountCode>> queryDiscountCode({
    String? discountCode,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'discountCode': discountCode};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ActivitiesDiscountCode>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesDiscountCode/queryByDiscountCode4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesDiscountCode> _value;
    try {
      _value = ApiResponse<ActivitiesDiscountCode>.fromJson(
        _result.data!,
        (json) => ActivitiesDiscountCode.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<String>> submitRegistration({
    ActivitiesRaceSubmit? data,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(data?.toJson() ?? <String, dynamic>{});
    final _options = _setStreamType<ApiResponse<String>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRace/submitRegistrationinfo4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<String> _value;
    try {
      _value = ApiResponse<String>.fromJson(
        _result.data!,
        (json) => json as String,
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>>
      getRaceJoinedGroup({int? pageNum, int? pageSize}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceRegistrationRecord/getJoinedGroup4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>> _value;
    try {
      _value =
          ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivitiesRaceRegistrationRecord>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActivitiesRaceRegistrationRecord.fromJson(
            json as Map<String, dynamic>,
          ),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>>
      listRaceJoinedGroup({int? pageNum, int? pageSize}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceRegistrationRecord/listJoinedGroup4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>> _value;
    try {
      _value =
          ApiResponse<ApiPaging<ActivitiesRaceRegistrationRecord>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivitiesRaceRegistrationRecord>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActivitiesRaceRegistrationRecord.fromJson(
            json as Map<String, dynamic>,
          ),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesRaceRegistrationRecordDetail>>
      queryRegistrationRecordDetail({String? raceId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'raceId': raceId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<ActivitiesRaceRegistrationRecordDetail>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceRegistrationRecord/getRegisteredGroupDetail4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesRaceRegistrationRecordDetail> _value;
    try {
      _value = ApiResponse<ActivitiesRaceRegistrationRecordDetail>.fromJson(
        _result.data!,
        (json) => ActivitiesRaceRegistrationRecordDetail.fromJson(
          json as Map<String, dynamic>,
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<PaymentOrder>> queryByOrderNumber({
    String? orderNumber,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'orderNumber': orderNumber};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<PaymentOrder>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/payment/paymentOrder/queryByOrderNumber',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<PaymentOrder> _value;
    try {
      _value = ApiResponse<PaymentOrder>.fromJson(
        _result.data!,
        (json) => PaymentOrder.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesRaceGroupRoute>> queryGroupRoute({
    String? groupId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'id': groupId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ActivitiesRaceGroupRoute>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceGroupRoute/queryByGroupId4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesRaceGroupRoute> _value;
    try {
      _value = ApiResponse<ActivitiesRaceGroupRoute>.fromJson(
        _result.data!,
        (json) =>
            ActivitiesRaceGroupRoute.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<RideBrief>>> getActivitiesRideBrief({
    String? registrationRecordId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'registrationRecordId': registrationRecordId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<RideBrief>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRegistrationPostRide/listUserRideBrief',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<RideBrief>> _value;
    try {
      _value = ApiResponse<List<RideBrief>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<RideBrief>(
                  (i) => RideBrief.fromJson(i as Map<String, dynamic>),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<PrizeClaimForm>> addRewardDistributionRecords({
    RewardDistributionRecord? data,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(data?.toJson() ?? <String, dynamic>{});
    final _options = _setStreamType<ApiResponse<PrizeClaimForm>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/rewardDistributionRecords/add',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<PrizeClaimForm> _value;
    try {
      _value = ApiResponse<PrizeClaimForm>.fromJson(
        _result.data!,
        (json) => PrizeClaimForm.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<PrizeClaimForm>> getRewardDistributionRecords({
    String? prizeClaimFormId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'id': prizeClaimFormId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<PrizeClaimForm>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/prizeClaimForm/queryById',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<PrizeClaimForm> _value;
    try {
      _value = ApiResponse<PrizeClaimForm>.fromJson(
        _result.data!,
        (json) => PrizeClaimForm.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivitiesRaceOrder>>>
      getActivitiesRaceProductsOrders({String? raceId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'raceId': raceId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ActivitiesRaceOrder>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceProductsOrder/queryByUidRaceId4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivitiesRaceOrder>> _value;
    try {
      _value = ApiResponse<List<ActivitiesRaceOrder>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivitiesRaceOrder>(
                  (i) => ActivitiesRaceOrder.fromJson(
                    i as Map<String, dynamic>,
                  ),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivityRaceMyResult>>> getActivitiesMyRaceResultList(
      {String? groupId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'groupId': groupId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ActivityRaceMyResult>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceResult/queryAllResultByUid4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivityRaceMyResult>> _value;
    try {
      _value = ApiResponse<List<ActivityRaceMyResult>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivityRaceMyResult>(
                  (i) => ActivityRaceMyResult.fromJson(
                    i as Map<String, dynamic>,
                  ),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivityRaceMyResult?>> getActivitiesMyRaceResult({
    String? groupId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'groupId': groupId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ActivityRaceMyResult?>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceResult/queryGeneralResultByUid4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivityRaceMyResult?> _value;
    try {
      _value = ApiResponse<ActivityRaceMyResult?>.fromJson(
        _result.data!,
        (json) => json == null
            ? null
            : ActivityRaceMyResult.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivityRaceUserResult>>>
      getActivitiesRaceResultList({
    String? resultTypeId,
    String? groupId,
    String? type,
    int? uid,
    int? pageNum,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'resultTypeId': resultTypeId,
      r'groupId': groupId,
      r'type': type,
      r'uid': uid,
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<ApiPaging<ActivityRaceUserResult>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceResult/list4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(
            baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl),
          ),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivityRaceUserResult>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActivityRaceUserResult>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivityRaceUserResult>.fromJson(
          json as Map<String, dynamic>,
          (json) =>
              ActivityRaceUserResult.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivitiesRaceBag>>> getActivitiesRaceBags({
    String? groupId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'groupId': groupId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ActivitiesRaceBag>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceBag/queryByGroupId4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivitiesRaceBag>> _value;
    try {
      _value = ApiResponse<List<ActivitiesRaceBag>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivitiesRaceBag>(
                  (i) => ActivitiesRaceBag.fromJson(i as Map<String, dynamic>),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivitiesEvent>>> getActivitiesEventList({
    int? pageNum,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<ActivitiesEvent>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEvents/list4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivitiesEvent>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActivitiesEvent>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivitiesEvent>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActivitiesEvent.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesEvent>> getActivitiesEvent({String? id}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'id': id};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ActivitiesEvent>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEvents/queryById4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesEvent> _value;
    try {
      _value = ApiResponse<ActivitiesEvent>.fromJson(
        _result.data!,
        (json) => ActivitiesEvent.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<RouteInfo>> getActivitiesRouteInfo({String? id}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'id': id};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<RouteInfo>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEventsStagesRoute/queryById4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<RouteInfo> _value;
    try {
      _value = ApiResponse<RouteInfo>.fromJson(
        _result.data!,
        (json) => RouteInfo.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivitiesJoinedEvent>>>
      getActivitiesJoinedEvents({int? pageNum, int? pageSize}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<ApiPaging<ActivitiesJoinedEvent>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEventsRegistrationRecord/getJoinedEvents4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivitiesJoinedEvent>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActivitiesJoinedEvent>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivitiesJoinedEvent>.fromJson(
          json as Map<String, dynamic>,
          (json) =>
              ActivitiesJoinedEvent.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivitiesJoinedEvent>>>
      listActivitiesJoinedEvents({int? pageNum, int? pageSize}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<ApiPaging<ActivitiesJoinedEvent>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEventsRegistrationRecord/listJoinedEvents4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivitiesJoinedEvent>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActivitiesJoinedEvent>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivitiesJoinedEvent>.fromJson(
          json as Map<String, dynamic>,
          (json) =>
              ActivitiesJoinedEvent.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<String>> joinEvent({
    String? eventId,
    String? discountCode,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'eventId': eventId,
      r'discountCode': discountCode,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<String>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEvents/join4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<String> _value;
    try {
      _value = ApiResponse<String>.fromJson(
        _result.data!,
        (json) => json as String,
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<JoinedStatistic>> getJoinedGroupStatistic() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<JoinedStatistic>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceRegistrationRecord/getJoinedGroupStatistic4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<JoinedStatistic> _value;
    try {
      _value = ApiResponse<JoinedStatistic>.fromJson(
        _result.data!,
        (json) => JoinedStatistic.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<JoinedStatistic>> getJoinedChallengesStatistic() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<JoinedStatistic>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRegistrationRecord/getJoinedChallengesStatistic4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<JoinedStatistic> _value;
    try {
      _value = ApiResponse<JoinedStatistic>.fromJson(
        _result.data!,
        (json) => JoinedStatistic.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<JoinedStatistic>> getJoinedEventsStatistic() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<JoinedStatistic>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEventsRegistrationRecord/getJoinedEventsStatistic4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<JoinedStatistic> _value;
    try {
      _value = ApiResponse<JoinedStatistic>.fromJson(
        _result.data!,
        (json) => JoinedStatistic.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivityRacePrizeRecord>> getPrizeRecode({
    String? groupId,
    String? raceId,
    int? uid,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'groupId': groupId,
      r'raceId': raceId,
      r'uid': uid,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ActivityRacePrizeRecord>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRacePrizeRecord/queryRecord',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivityRacePrizeRecord> _value;
    try {
      _value = ApiResponse<ActivityRacePrizeRecord>.fromJson(
        _result.data!,
        (json) =>
            ActivityRacePrizeRecord.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivityRacePrizeRecord>> submitPrizeClaim({
    ActivitiesRacePrizeRecordSubmit? data,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(data?.toJson() ?? <String, dynamic>{});
    final _options = _setStreamType<ApiResponse<ActivityRacePrizeRecord>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRacePrizeRecord/submitPrizeClaim',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivityRacePrizeRecord> _value;
    try {
      _value = ApiResponse<ActivityRacePrizeRecord>.fromJson(
        _result.data!,
        (json) =>
            ActivityRacePrizeRecord.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<String>> confirmCompletion({
    String? registrationRecordId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'registrationRecordId': registrationRecordId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<String>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRegistrationRecord/confirmCompletion4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<String> _value;
    try {
      _value = ApiResponse<String>.fromJson(
        _result.data!,
        (json) => json as String,
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ChallengeInfo>>> queryViableChallenges({
    int? postRideId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'postRideId': postRideId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ChallengeInfo>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRegistrationPostRide/queryViableChallenges',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ChallengeInfo>> _value;
    try {
      _value = ApiResponse<List<ChallengeInfo>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ChallengeInfo>(
                  (i) => ChallengeInfo.fromJson(i as Map<String, dynamic>),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<String>> changeChallenges({
    String? newChallengeId,
    int? postRideId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'newChallengeId': newChallengeId,
      r'postRideId': postRideId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<String>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRegistrationPostRide/changeChallenges',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<String> _value;
    try {
      _value = ApiResponse<String>.fromJson(
        _result.data!,
        (json) => json as String,
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<PrizeInfo>> clickToDraw({String? luckyDrawaId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'luckyDrawaId': luckyDrawaId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<PrizeInfo>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/luckyDrawa/clickToDraw',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<PrizeInfo> _value;
    try {
      _value = ApiResponse<PrizeInfo>.fromJson(
        _result.data!,
        (json) => PrizeInfo.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ChallengeLuckyDrawa>> queryDrawaDetailByChallengeId({
    String? challengeId,
    String? uid,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'challengeId': challengeId,
      r'uid': uid,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ChallengeLuckyDrawa>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/luckyDrawa/queryDetailByChallengeId',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ChallengeLuckyDrawa> _value;
    try {
      _value = ApiResponse<ChallengeLuckyDrawa>.fromJson(
        _result.data!,
        (json) => ChallengeLuckyDrawa.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActiviesJoinMember>>>
      getActiviesEventsRegistrationRecord({
    String? eventId,
    int? pageNum,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'eventId': eventId,
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ApiPaging<ActiviesJoinMember>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEventsRegistrationRecord/listUser4Mobile',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActiviesJoinMember>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActiviesJoinMember>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActiviesJoinMember>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActiviesJoinMember.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<BikeEntire>> queryUserSnapshot({
    String? bikeSnapshotId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'bikeSnapshotId': bikeSnapshotId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<BikeEntire>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesEventsUserBikeSnapshot/queryUserSnapshot',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<BikeEntire> _value;
    try {
      _value = ApiResponse<BikeEntire>.fromJson(
        _result.data!,
        (json) => BikeEntire.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ShopInfo>>?> getNearShopList({
    String? prizeId,
    int? tenantId,
    String? latitude,
    String? longitude,
    int? pageNum,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'prizeId': prizeId,
      r'tenantId': tenantId,
      r'latitude': latitude,
      r'longitude': longitude,
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ShopInfo>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/store/nearby',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ApiResponse<List<ShopInfo>>? _value;
    try {
      _value = _result.data == null
          ? null
          : ApiResponse<List<ShopInfo>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<ShopInfo>(
                        (i) => ShopInfo.fromJson(i as Map<String, dynamic>),
                      )
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ShopInfo>>?> getShopListByCity({
    String? challengeId,
    String? latitude,
    String? longitude,
    String? cityName,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'challengeId': challengeId,
      r'latitude': latitude,
      r'longitude': longitude,
      r'cityName': cityName,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ShopInfo>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/challenge_new/store/nearby',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ApiResponse<List<ShopInfo>>? _value;
    try {
      _value = _result.data == null
          ? null
          : ApiResponse<List<ShopInfo>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<ShopInfo>(
                        (i) => ShopInfo.fromJson(i as Map<String, dynamic>),
                      )
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<bool>?> checkShopBinding({String? challengeId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'challengeId': challengeId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<bool>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRewardShopLink/checkShopBinding',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ApiResponse<bool>? _value;
    try {
      _value = _result.data == null
          ? null
          : ApiResponse<bool>.fromJson(
              _result.data!,
              (json) => json as bool,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<RewardCity>>?> getCityList({
    String? challengeId,
    String? latitude,
    String? longitude,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'challengeId': challengeId,
      r'latitude': latitude,
      r'longitude': longitude,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<RewardCity>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRewardShopLink/getCityList',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ApiResponse<List<RewardCity>>? _value;
    try {
      _value = _result.data == null
          ? null
          : ApiResponse<List<RewardCity>>.fromJson(
              _result.data!,
              (json) => json is List<dynamic>
                  ? json
                      .map<RewardCity>(
                        (i) => RewardCity.fromJson(
                          i as Map<String, dynamic>,
                        ),
                      )
                      .toList()
                  : List.empty(),
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesChallengeStoreReward>> queryChallengeReward({
    String? scannedUid,
    String? acPhysicalRewardId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'scannedUid': scannedUid,
      r'acPhysicalRewardId': acPhysicalRewardId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<ApiResponse<ActivitiesChallengeStoreReward>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRewardShopLink/queryByQrCodeStr',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesChallengeStoreReward> _value;
    try {
      _value = ApiResponse<ActivitiesChallengeStoreReward>.fromJson(
        _result.data!,
        (json) => ActivitiesChallengeStoreReward.fromJson(
          json as Map<String, dynamic>,
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesChallengeAssistantDistrobutionRecordsDetail>>
      queryByQrCodeStr4CurrentShop({
    String? scannedUid,
    String? acPhysicalRewardId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'scannedUid': scannedUid,
      r'acPhysicalRewardId': acPhysicalRewardId,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        ApiResponse<ActivitiesChallengeAssistantDistrobutionRecordsDetail>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRewardShopLink/queryByQrCodeStr4CurrentShop',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesChallengeAssistantDistrobutionRecordsDetail>
        _value;
    try {
      _value = ApiResponse<
          ActivitiesChallengeAssistantDistrobutionRecordsDetail>.fromJson(
        _result.data!,
        (json) =>
            ActivitiesChallengeAssistantDistrobutionRecordsDetail.fromJson(
          json as Map<String, dynamic>,
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<String>?> distributeChallengeReward({
    String? distributionRecordId,
    String? toUid,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'distributionRecordId': distributionRecordId,
      r'toUid': toUid,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<String>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeRewardShopLink/distributeV2',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ApiResponse<String>? _value;
    try {
      _value = _result.data == null
          ? null
          : ApiResponse<String>.fromJson(
              _result.data!,
              (json) => json as String,
            );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<int>?> scanQrCode({String? qrCodeStr}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'qrCodeStr': qrCodeStr};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<int>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/edgeOs/api/scanQrCode',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>?>(_options);
    late ApiResponse<int>? _value;
    try {
      _value = _result.data == null
          ? null
          : ApiResponse<int>.fromJson(_result.data!, (json) => json as int);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivitiesAvgSpeed>>> queryAvgSpeedByGroupId({
    String? groupId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'groupId': groupId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<List<ActivitiesAvgSpeed>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesRaceAvgSpd/queryByGroupId',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivitiesAvgSpeed>> _value;
    try {
      _value = ApiResponse<List<ActivitiesAvgSpeed>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivitiesAvgSpeed>(
                  (i) => ActivitiesAvgSpeed.fromJson(
                    i as Map<String, dynamic>,
                  ),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ActivitiesWhitePrice>> queryWhiteByLinkedId({
    String? linkedId,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'linkedId': linkedId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<ApiResponse<ActivitiesWhitePrice>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesWhiteList/queryByLinkedId',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ActivitiesWhitePrice> _value;
    try {
      _value = ApiResponse<ActivitiesWhitePrice>.fromJson(
        _result.data!,
        (json) => ActivitiesWhitePrice.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<List<ActivitiesChallengeStoreRewardStatistic>>>
      queryStoreRewardStatistic({String? challengeId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'challengeId': challengeId};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        ApiResponse<List<ActivitiesChallengeStoreRewardStatistic>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeStore/queryStoreRewardStatistic',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<List<ActivitiesChallengeStoreRewardStatistic>> _value;
    try {
      _value =
          ApiResponse<List<ActivitiesChallengeStoreRewardStatistic>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<ActivitiesChallengeStoreRewardStatistic>(
                  (i) => ActivitiesChallengeStoreRewardStatistic.fromJson(
                    i as Map<String, dynamic>,
                  ),
                )
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivitiesChallengeStoreRewardStatisticDetail>>>
      queryStoreRewardUserList({
    String? challengeId,
    String? shopId,
    String? rewardId,
    String? distributionStatus,
    int? pageNum,
    int? pageSize,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{
      r'challengeId': challengeId,
      r'shopId': shopId,
      r'rewardId': rewardId,
      r'distributionStatus': distributionStatus,
      r'pageNo': pageNum,
      r'pageSize': pageSize,
    };
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<
        ApiResponse<ApiPaging<ActivitiesChallengeStoreRewardStatisticDetail>>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/activities/activitiesChallengeStore/queryStoreRewardUserList',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivitiesChallengeStoreRewardStatisticDetail>>
        _value;
    try {
      _value = ApiResponse<
          ApiPaging<ActivitiesChallengeStoreRewardStatisticDetail>>.fromJson(
        _result.data!,
        (json) =>
            ApiPaging<ActivitiesChallengeStoreRewardStatisticDetail>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActivitiesChallengeStoreRewardStatisticDetail.fromJson(
            json as Map<String, dynamic>,
          ),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivityPhoto>>> pictures({
    ActivityMediaRequest? data,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(data?.toJson() ?? <String, dynamic>{});
    final _options = _setStreamType<ApiResponse<ApiPaging<ActivityPhoto>>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/wx/activities/pictures',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivityPhoto>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActivityPhoto>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivityPhoto>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActivityPhoto.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<ApiResponse<ApiPaging<ActivityVideo>>> videos({
    ActivityMediaRequest? data,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    queryParameters.removeWhere((k, v) => v == null);
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(data?.toJson() ?? <String, dynamic>{});
    final _options = _setStreamType<ApiResponse<ApiPaging<ActivityVideo>>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/wx/activities/videos',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late ApiResponse<ApiPaging<ActivityVideo>> _value;
    try {
      _value = ApiResponse<ApiPaging<ActivityVideo>>.fromJson(
        _result.data!,
        (json) => ApiPaging<ActivityVideo>.fromJson(
          json as Map<String, dynamic>,
          (json) => ActivityVideo.fromJson(json as Map<String, dynamic>),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(String dioBaseUrl, String? baseUrl) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
