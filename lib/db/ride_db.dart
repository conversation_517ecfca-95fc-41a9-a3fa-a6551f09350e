import 'package:new_edge/model/account.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

import 'record_table.dart';

class RideDB {
  Database? db;

  static const String db_name = 'ride';

  Future close() async {
    if (db != null) {
      await db!.close();
    }
  }

  Future open() async {
    if (db != null) {
      return;
    }
    var version = 1;
    var path = (await getApplicationSupportDirectory()).path;
    db = await openDatabase(path + "/$db_name.db", version: version,
        onCreate: (Database db, int version) async {
      StringBuffer sql = StringBuffer(
          'create table $table_record ($column_rideId text primary key');
      record_table_columns.forEach((key, value) {
        if (key == column_rideId) {
          return;
        }
        sql.write(',$key $value');
      });
      sql.write(')');
      await db.execute(sql.toString());
    }, onUpgrade: (db, oldVersion, newVersion) async {
      // autoUpgrade(db: db, columns: columns, tableName: table_record);
    });
  }

  Future insertOrUpdateRecord(Map record, {bool uploadStatus = false}) async {
    var result = await getRecord(record['rideId']);
    if (result == null) {
      await db!.insert(table_record, {
        column_id: record['id'],
        column_postRideId: record['postRideId'],
        column_uid: record['uid'],
        column_meter: record['meter'],
        column_second: record['second'],
        column_speed: record['speed'],
        column_maxSpeed: record['maxSpeed'],
        column_fullSpeed: record['fullSpeed'],
        column_climb: record['climb'],
        column_startTime: record['startTime'],
        column_endTime: record['endTime'],
        column_uploadTime: record['uploadTime'],
        column_sampleInterval: record['sampleInterval'],
        column_source: record['source'],
        column_rideId: record['rideId'],
        column_wgs: record['wgs'],
        column_province: record['province'],
        column_city: record['city'],
        column_type: record['type'],
        column_coverImg: record['coverImg'],
        column_status: record['status'],
        column_subStatus: record['subStatus'],
        column_content: record['content'],
        column_pauseTime: record['pauseTime'],
        column_altitude: record['altitude'],
        column_heartRate: record['heartRate'],
        column_cadence: record['cadence'],
        column_power: record['power'],
        column_locationDetail: record['locationDetail'],
        column_uploadStatus: uploadStatus,
      });
    } else {
      await db!.update(
          table_record,
          {
            column_id: record['id'],
            column_postRideId: record['postRideId'],
            column_uid: record['uid'],
            column_meter: record['meter'],
            column_second: record['second'],
            column_speed: record['speed'],
            column_maxSpeed: record['maxSpeed'],
            column_fullSpeed: record['fullSpeed'],
            column_climb: record['climb'],
            column_startTime: record['startTime'],
            column_endTime: record['endTime'],
            column_uploadTime: record['uploadTime'],
            column_sampleInterval: record['sampleInterval'],
            column_source: record['source'],
            column_rideId: record['rideId'],
            column_wgs: record['wgs'],
            column_province: record['province'],
            column_city: record['city'],
            column_type: record['type'],
            column_coverImg: record['coverImg'],
            column_status: record['status'],
            column_subStatus: record['subStatus'],
            column_content: record['content'],
            column_pauseTime: record['pauseTime'],
            column_altitude: record['altitude'],
            column_heartRate: record['heartRate'],
            column_cadence: record['cadence'],
            column_power: record['power'],
            column_locationDetail: record['locationDetail'],
            column_uploadStatus: uploadStatus,
          },
          where: '$column_rideId = ?',
          whereArgs: [record['rideId']]);
    }
  }

  Future updateRecordUploadStatus({String? rideId, bool? uploadStatus}) async {
    await db!.update(
        table_record,
        {
          column_uploadStatus: uploadStatus,
        },
        where: '$column_rideId = ?',
        whereArgs: [rideId]);
  }

  Future<List<Map>> getUnUploadRecordsDate() async {
    return await db!.query(table_record,
        columns: [column_startTime],
        where: '$column_uid = ? and $column_uploadStatus = ?',
        whereArgs: [Account.loginAccount!.uid, 0]);
  }

  Future<List<Map>> getUnUploadRecords({int? startTime, int? endTime}) async {
    var where = '$column_uid = ? and $column_uploadStatus = ?';
    var whereArgs = [Account.loginAccount!.uid, 0];
    if (startTime != null) {
      where = where + ' and $column_startTime >= ?';
      whereArgs.add(startTime);
    }
    if (endTime != null) {
      where = where + ' and $column_endTime <= ?';
      whereArgs.add(endTime);
    }
    return await db!.query(table_record,
        columns: record_table_columns.keys.toList(),
        where: where,
        whereArgs: whereArgs);
  }

  Future<Map?> getRecord(String? rideId) async {
    List<Map> maps = await db!.query(table_record,
        columns: record_table_columns.keys.toList(),
        where: '$column_rideId = ?',
        whereArgs: [rideId]);
    if (maps.length > 0) {
      return maps.first;
    }
    return null;
  }

  Future<int> deleteRecord(String? rideId) async {
    return await db!
        .delete(table_record, where: '$column_rideId = ?', whereArgs: [rideId]);
  }
}
