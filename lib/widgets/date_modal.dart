import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';

class DateModal extends StatelessWidget {
  DateTime? initialDateTime;
  ValueChanged<DateTime?>? onDateTimeChanged;
  CupertinoDatePickerMode mode;
  DateTime? minimumDate =  DateTime(1900);
  DateTime? maximumDate;
  int minuteInterval;

  DateModal({
    this.initialDateTime,
    this.onDateTimeChanged,
    this.mode = CupertinoDatePickerMode.date,
    this.maximumDate,
    this.minimumDate,
    this.minuteInterval = 1
  }){
   if(initialDateTime != null) {
     if(minimumDate != null && initialDateTime!.millisecondsSinceEpoch < minimumDate!.millisecondsSinceEpoch){
       initialDateTime = minimumDate;
     }
     if(maximumDate != null && initialDateTime!.millisecondsSinceEpoch > maximumDate!.millisecondsSinceEpoch){
       initialDateTime = maximumDate;
     }
     // 解决课程的时间选择器的分钟间隔改成15分钟一项问题，比如把分钟改成 0/15/30/45，否则会报错
     var tmp = initialDateTime!.minute % minuteInterval;
     if(tmp != 0){
       initialDateTime = initialDateTime!.subtract(Duration(minutes: tmp));
     }
   }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      color: Colors.white,
      child: Column(
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  // shape: CircleBorder(),
                  // textColor: Color(0xFF2285F4),
                  // padding: EdgeInsets.all(12),
                  child: Text(
                    "取消",
                    style: TextStyle(fontSize: 16, color: DColor.ff232323),
                  )),
              TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    onDateTimeChanged!(initialDateTime);
                  },
                  // shape: CircleBorder(),
                  // textColor: Color(0xFF2285F4),
                  // padding: EdgeInsets.all(12),
                  child: Text("确认", style: TextStyle(fontSize: 16, color: DColor.primary))),
            ],
          ),
          Divider(
            height: 1,
            color: Color(0xFFE1E1E3),
          ),
          Expanded(
              child: CupertinoDatePicker(
                minimumDate: minimumDate!,
                maximumDate: maximumDate!,
                mode: mode,
                use24hFormat: true,
                minuteInterval: minuteInterval,
                initialDateTime: initialDateTime!,
                onDateTimeChanged: (value) {
                  initialDateTime = value;
                },
              ))
        ],
      ),
    );
  }
}
