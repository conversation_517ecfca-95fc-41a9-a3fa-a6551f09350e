import 'package:flutter/material.dart';
import 'package:new_edge/config/image_path.dart';

class StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  StickyTabBarDelegate({required this.child, this.padding, this.margin});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Container(
        padding: padding ?? EdgeInsets.only(left: 10, right: 10),
        child: Card(
            margin: margin?? EdgeInsets.only(left: 4, right: 4, top: 4, bottom: 0.0),
            color: Color(0xFFEAEAEA),
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(5), topRight: Radius.circular(5))),
            shadowColor: Color(0xFF000000).withOpacity(0.16),
            elevation: 2,
            clipBehavior: Clip.antiAlias,
            child: Container(width: double.infinity, child: child)),
      ),
    );
  }

  @override
  double get maxExtent => child.preferredSize.height;

  @override
  double get minExtent => child.preferredSize.height;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}

class CustomIcon extends StatelessWidget {
  final String name;
  final double height;
  final Color? color;

  const CustomIcon(
    this.name, {
    Key? key,
    this.height = 21,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final IconThemeData iconTheme = IconTheme.of(context);
    final double iconOpacity = iconTheme.opacity ?? 1.0;
    Color iconColor = color ?? iconTheme.color ?? Colors.black;

    if (iconOpacity != 1.0) {
      iconColor = iconColor.withOpacity(iconColor.opacity * iconOpacity);
    }
    return Image.asset(
      DImages.formatPathPng(name),
      color: iconColor,
      height: height,
    );
  }
}
