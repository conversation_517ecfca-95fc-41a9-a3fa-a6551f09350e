import 'dart:async';

import 'package:flutter/material.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/pages/common/developer_tools.dart';
import 'package:new_edge/request/SignUtil.dart';
import 'package:new_edge/util/navigator.dart';

class AppInfoWidget extends StatefulWidget {
  @override
  _AppInfoWidgetState createState() => _AppInfoWidgetState();
}

class _AppInfoWidgetState extends State<AppInfoWidget> {
  var _appVersion;
  int _tapCounter = 0;
  Timer? _tapTimer;

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  @override
  void dispose() {
    if (_tapTimer != null) {
      _tapTimer!.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          "assets/images/logo_EDGE_black.png",
          height: 40.5,
          width: 80,
          fit: BoxFit.cover,
        ),
        SizedBox(height: 3),
        GestureDetector(
          onTap: _handleVersionTap,
          child: Text('v_$_appVersion',
              style: TextStyle(color: DColor.ff222332, fontSize: 24)),
        ),
      ],
    );
  }

  void _getAppVersion() async {
    String? version = await getAppVersion();
    setState(() {
      _appVersion = version;
    });
  }

  void _handleVersionTap() {
    _tapCounter++;

    if (_tapCounter == 1) {
      _tapTimer = Timer(Duration(seconds: 1), () {
        _tapCounter = 0; // Reset the counter if no further taps
      });
    }

    if (_tapCounter >= 5) { // Change this value to the number of taps you want
      _tapCounter = 0;
      _tapTimer?.cancel();
      _triggerAction();
    }
  }

  void _triggerAction() {
    pushPage(context, DeveloperToolsPage());
  }
}
