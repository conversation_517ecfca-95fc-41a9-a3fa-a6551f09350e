import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:new_edge/bloc/follow_bloc.dart';
import 'package:new_edge/config/color.dart';
import 'package:new_edge/io/follow.dart';
import 'package:new_edge/picker/constants/constants.dart';
import 'package:new_edge/request/MyHttp.dart';
import 'package:new_edge/util/modal_pop_utils.dart';
import 'package:new_edge/util/toast.dart';

class FocusWidget extends StatefulWidget {
  num toUid;
  num isFriend;

  FocusWidget(this.toUid, this.isFriend);

  @override
  State<StatefulWidget> createState() {
    return _FocusState();
  }
}

class _FocusState extends State<FocusWidget> {
  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: () async {
        try {
          //本人未关注对方，直接关注
          if (widget.isFriend == 0 || widget.isFriend == 3) {
            await FollowApi(MyHttp.dio).queryFollow(widget.toUid as int);
            var eventFollow = FollowCountUpdated(++Constants.followCount);
            BlocProvider.of<FollowBloc>(context).add(eventFollow);
            setState(() {
              if (widget.isFriend == 3) {
                widget.isFriend = 1;
              } else {
                widget.isFriend = 2;
              }
            });
          } else {
            //本人已关注对方，询问是否真的取关
            ModalPopUtils.showModalPop(context, (value) async {
              try {
                await FollowApi(MyHttp.dio).queryUnfollow(widget.toUid as int);
                var eventFollow = FollowCountUpdated(--Constants.followCount);
                BlocProvider.of<FollowBloc>(context).add(eventFollow);
                setState(() {
                  if (widget.isFriend == 1) {
                    widget.isFriend = 3;
                  } else {
                    widget.isFriend = 0;
                  }
                });
              } on DioException catch (e) {
                showToast(e.message.toString(), context);
              }
            }, modals: [
              PopModal(
                  tag: "unfollow", content: "取消关注", color: DColor.ff242424),
            ]);
          }

          setState(() {});
        } on DioException catch (e) {
          try {
            showToast(e.message.toString(), context);
          } catch (e) {}
        }
      },
      style: ButtonStyle(
        textStyle: WidgetStateProperty.all(TextStyle(fontSize: 13)),
        minimumSize: WidgetStateProperty.all(Size(55, 25)),
        padding: WidgetStateProperty.all(
            EdgeInsets.symmetric(vertical: 3, horizontal: 8)),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        backgroundColor: WidgetStateProperty.resolveWith((states) {
          // if (states.contains(WidgetState.pressed)) {
          //   return Colors.green[200];
          // }
          return widget.isFriend == 1 || widget.isFriend == 2
              ? Colors.transparent
              : DColor.fffb691d;
        }),
        foregroundColor: WidgetStateProperty.all(
            widget.isFriend == 1 || widget.isFriend == 2
                ? DColor.fffb691d
                : Colors.white),
        side: WidgetStateProperty.all(BorderSide(
            color: widget.isFriend == 1 || widget.isFriend == 2
                ? DColor.fffb691d
                : DColor.fffb691d,
            width: 1)),
        // shape: WidgetStateProperty.all(StadiumBorder()),
      ),
      child: Text(widget.isFriend == 2
          ? "已关注"
          : widget.isFriend == 1
              ? "互相关注"
              : "关注"),
    );
  }
}
