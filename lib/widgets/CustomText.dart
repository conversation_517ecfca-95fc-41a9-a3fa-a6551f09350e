import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class CustomText extends LeafRenderObjectWidget {
  final String data;
  final TextStyle? style;
  final TextAlign textAlign;
  final TextDirection textDirection;
  final int? maxLines;
  final TextOverflow overflow;

  CustomText(
      this.data, {
        Key? key,
        this.style,
        this.textAlign = TextAlign.start,
        this.textDirection = TextDirection.ltr,
        this.maxLines,
        this.overflow = TextOverflow.clip,
      }) : super(key: key);

  @override
  RenderObject createRenderObject(BuildContext context) {
    return RenderCustomText(
      text: data,
      style: style ?? DefaultTextStyle.of(context).style,
      textAlign: textAlign,
      textDirection: textDirection,
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  @override
  void updateRenderObject(
      BuildContext context, RenderCustomText renderObject) {
    renderObject
      ..text = data
      ..style = style ?? DefaultTextStyle.of(context).style
      ..textAlign = textAlign
      ..textDirection = textDirection
      ..maxLines = maxLines
      ..overflow = overflow;
  }
}

class RenderCustomText extends RenderBox {
  RenderCustomText({
    required String text,
    required TextStyle style,
    required TextAlign textAlign,
    required TextDirection textDirection,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  })  : _text = text,
        _style = style,
        _textAlign = textAlign,
        _textDirection = textDirection,
        _maxLines = maxLines,
        _overflow = overflow;

  String _text;
  TextStyle _style;
  TextAlign _textAlign;
  TextDirection _textDirection;
  int? _maxLines;
  TextOverflow _overflow;

  TextPainter _textPainter = TextPainter();

  set text(String value) {
    if (_text != value) {
      _text = value;
      markNeedsLayout();
    }
  }

  set style(TextStyle value) {
    if (_style != value) {
      _style = value;
      markNeedsLayout();
    }
  }

  set textAlign(TextAlign value) {
    if (_textAlign != value) {
      _textAlign = value;
      markNeedsLayout();
    }
  }

  set textDirection(TextDirection value) {
    if (_textDirection != value) {
      _textDirection = value;
      markNeedsLayout();
    }
  }

  set maxLines(int? value) {
    if (_maxLines != value) {
      _maxLines = value;
      markNeedsLayout();
    }
  }

  set overflow(TextOverflow value) {
    if (_overflow != value) {
      _overflow = value;
      markNeedsLayout();
    }
  }

  @override
  void performLayout() {
    _textPainter
      ..text = TextSpan(text: _text, style: _style)
      ..textAlign = _textAlign
      ..textDirection = _textDirection
      ..maxLines = _maxLines
      ..ellipsis = _overflow == TextOverflow.ellipsis ? '...' : null;

    _textPainter.layout(minWidth: 0, maxWidth: constraints.maxWidth);
    size = Size(_textPainter.width, _textPainter.height);
  }

  @override
  void paint(PaintingContext context, Offset offset) {
    _textPainter.paint(context.canvas, offset);
  }
}