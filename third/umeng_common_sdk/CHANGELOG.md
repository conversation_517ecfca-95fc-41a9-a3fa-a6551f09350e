##1.2.8
-插件及Android Demo工程适配flutter_3.24.3

##1.2.7
-Android端initCommon接口新增可选参数Push Secret。
-Android端plugin插件项目依赖common包升级到9.6.5，asms包升级到1.8.0。

##1.2.6
-Android端plugin插件项目依赖的common包版本升级到9.5.6，asms包版本升级到1.6.3。
-Android端example工程增加MyApp类文件，将UMConfigure.preInit函数放到MyApp类中io.flutter.app.FlutterApplication
.onCreate生命周期函数中执行。

##1.2.5
-Android 稳定性优化；

##1.2.4
-Android 上下文获取优化；

##1.2.3
-Android 依赖库asms更新1.4.1；

##1.2.2
-Android 依赖更新；初始化简化；

##1.2.1
-Android Example工程初始化优化；

##1.2.0
-支持空安全；

##1.1.3
-Android Example工程优化；Android依赖库迁移maven center；

##1.1.2
-Android Example工程添加release包混淆规则

##1.1.1
-Android依赖库改为线上maven形式

##1.1.0
-优化描述，同步友盟+官方下载flutter插件

## 0.0.3
-example工程完善基础组件初始化

## 0.0.2
-同步最新友盟+common库

## 0.0.1
-初始提交
