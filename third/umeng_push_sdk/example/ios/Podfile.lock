PODS:
  - Flutter (1.0.0)
  - UMCCommon<PERSON><PERSON> (2.0.2)
  - UMC<PERSON><PERSON> (7.3.7):
    - UMDevice
  - UMDev<PERSON> (2.2.1)
  - umeng_push_sdk (0.0.1):
    - Flutter
    - UMCCommonLog
    - UMCommon
    - UMDevice
    - UMPush
  - UMPush (4.0.3):
    - UMCommon

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - umeng_push_sdk (from `.symlinks/plugins/umeng_push_sdk/ios`)

SPEC REPOS:
  trunk:
    - UMCCommonLog
    - UMCommon
    - UMDevice
    - UMPush

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  umeng_push_sdk:
    :path: ".symlinks/plugins/umeng_push_sdk/ios"

SPEC CHECKSUMS:
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  UMCCommonLog: bea707e50c85cef4b0eb47cc5c7226bb843245ca
  UMCommon: 37fe35f498e92cf57bbb06a086f02915fc35f211
  UMDevice: 053478c4b4d7292f31f0a275c227d3c4007a5571
  umeng_push_sdk: ec5a07d1b752e3910bd0679cc9e65c29032319ce
  UMPush: a517cd5907eb10669ce6cf2e6c0dfe40be84b6b5

PODFILE CHECKSUM: e5ee00144d04e7b168ba7ea28a9753540e444f3d

COCOAPODS: 1.11.3
