package com.edge.cycling.utils;

import android.os.Build;
import android.text.TextUtils;

import java.lang.reflect.Method;

/**
 * Created by Wiki on 15/8/21.
 */
public class SystemUtils {

    private final static String TAG = "SystemUtils";

    public static final String KEY_MIUI_VERSION_CODE = "ro.miui.ui.version.code";
    public static final String KEY_MIUI_VERSION_NAME = "ro.miui.ui.version.name";
    public static final String KEY_MIUI_INTERNAL_STORAGE = "ro.miui.internal.storage";
    public static final String RO_BUILD_VERSION_OPPOROM = "ro.build.version.opporom";


    public static String getSystemVersion() {
        return Build.VERSION.RELEASE;
    }

    private static String screen_bright_wake_lock;

    public static boolean isOPPO() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().equals("oppo");
    }

    public static boolean isSamsung() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().equals("samsung");
    }

    public static boolean isMoto() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().contains("moto");
    }

    public static boolean isHtc() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().contains("htc");
    }

    public static boolean isSony() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().contains("sony");
    }


    public static boolean isVIVO() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().equals("vivo");
    }

    public static boolean isXiaomi() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().equals("xiaomi");
    }

    public static boolean isMeizu() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().equals("meizu");
    }
    public static boolean isOnePlus() {
        if (Build.BRAND == null){
            return false;
        }
        return Build.BRAND.toLowerCase().equals("oneplus");
    }
    public static boolean isLeTV() {
        if (Build.BRAND == null){
            return false;
        }
        return Build.BRAND.toLowerCase().equals("letv");
    }
    public static boolean isGoogle() {
        if (Build.BRAND == null){
            return false;
        }
        return Build.BRAND.toLowerCase().equals("google");
    }
    public static boolean isSmartisan() {
        if (Build.BRAND == null){
            return false;
        }
        return Build.BRAND.toLowerCase().equals("smartisan");
    }

    public static boolean isMIUI() {
        String property = getSystemProperty(KEY_MIUI_VERSION_NAME, "");
        return !TextUtils.isEmpty(property);
    }

    public static String getSystemProperty(String name, String defaultValue) {
        try {
            Class<?> clz = Class.forName("android.os.SystemProperties");
            Method get = clz.getMethod("get", String.class, String.class);

            return (String) get.invoke(clz, name, defaultValue);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static boolean isHuawei() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().equals("huawei") || Build.BRAND.toLowerCase().equals("honor");
    }

    public static boolean isDoov() {
        if (Build.BRAND == null) {
            return false;
        }
        return Build.BRAND.toLowerCase().equals("doov");
    }

    public static boolean isHuaweiAndroidOreo() {
        if (!isHuawei()) {
            return false;
        }
        return Build.VERSION.SDK_INT >= 26;
    }

    public static String getPhoneBrand() {
        if (isXiaomi()) {
            return "小米";
        } else if (isHuawei()) {
            return "华为";
        } else if (isOPPO()) {
            return "OPPO";
        } else if (isVIVO()) {
            return "vivo";
        } else if (isSamsung()) {
            return "三星";
        } else if (isMeizu()) {
            return "魅族";
        } else if (isSony()) {
            return "索尼";
        } else if (isHtc()) {
            return "HTC";
        } else {
            return "其他品牌";
        }
    }


}