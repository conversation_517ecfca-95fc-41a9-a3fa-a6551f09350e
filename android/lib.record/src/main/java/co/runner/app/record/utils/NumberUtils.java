package co.runner.app.record.utils;

/**
 * Created by Wiki on 2016/10/23.
 */

public class NumberUtils {

    public static String keep2DecimalMeter(double number) {
        return keep2Decimal(number);
    }

    public static String keep2Decimal(double number) {
        String result = String.valueOf(number);
        int dotIndex = result.indexOf(".");
        if (dotIndex >= 0) {
            if (dotIndex < result.length() - 3) {
                return result.substring(0, dotIndex + 3);
            } else if (dotIndex == result.length() - 3) {
                return result;
            } else {
                return result + "0";
            }
        }
        return result;
    }

    public static String keepMax2Decimal(double number) {
        String result = String.valueOf(number);
        if (result.endsWith(".0")){
            result = result.replace(".0","");
        }
        int dotIndex = result.indexOf(".");
        if (dotIndex >= 0) {
            if (dotIndex < result.length() - 3) {
                return result.substring(0, dotIndex + 3);
            }
        }
        return result;
    }


    /**
     * 以万为单位
     * @param number
     * @return
     */
    public static String keep1Decimal(double number) {
        String result = String.valueOf(number);
        int dotIndex = result.indexOf(".");
        if (dotIndex >= 0) {
            if (dotIndex < result.length() - 2) {
                return result.substring(0, dotIndex + 2);
            }
        }
        return result;
    }

    public static String getSplitString(int number) {
        String result = String.valueOf(number);
        if (result.length() <= 3) {
            return result;
        }
        return result.substring(0, result.length() - 3) + "," + result.substring(result.length() - 3, result.length());
    }

}
