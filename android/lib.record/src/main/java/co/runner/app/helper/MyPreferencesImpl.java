package co.runner.app.helper;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Parcelable;

import com.alibaba.fastjson.JSON;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Wiki on 16/2/13.
 */
public class MyPreferencesImpl implements MyPreferences {
    private final String mName;
    private SharedPreferences mPreferences = null;
    private SharedPreferences.Editor mEdit = null;


    MyPreferencesImpl(Context context, String name, int mode) {
        this.mPreferences = context.getSharedPreferences(name, mode);
        this.mEdit = mPreferences.edit();
        this.mName = name;
    }

    @Override
    public Map<String, ?> getAll() {
        return mPreferences.getAll();
    }

    @Override
    public float getFloat(String key, float defValue) {
        return mPreferences.getFloat(key, defValue);
    }

    @Override
    public boolean getBoolean(String key, boolean defValue) {
        return mPreferences.getBoolean(key, defValue);
    }


    @Override
    public int getInt(String key, int defValue) {
        return mPreferences.getInt(key, defValue);
    }


    @Override
    public String getString(String key, String defValue) {
        return mPreferences.getString(key, defValue);
    }

    @Override
    public Set<String> getStringSet(String key, Set<String> defValues) {
        return mPreferences.getStringSet(key, defValues);
    }

    @Override
    public boolean contains(String key) {
        return mPreferences.contains(key);
    }

    @Override
    public long getLong(String key, long defValue) {
        return mPreferences.getLong(key, defValue);
    }

    @Override
    public void putString(String key, String value) {
        mEdit.putString(key, value);
        apply();
    }

    @Override
    public void putInt(String key, int value) {
        mEdit.putInt(key, value);
        apply();
    }

    @Override
    public void putLong(String key, long value) {
        mEdit.putLong(key, value);
        apply();
    }

    @Override
    public void clear() {
        mEdit.clear();
        apply();
    }

    @Override
    public void putFloat(String key, float value) {
        mEdit.putFloat(key, value);
        apply();
    }

    @Override
    public void putBoolean(String key, boolean value) {
        mEdit.putBoolean(key, value);
        apply();
    }

    @Override
    public void putStringSet(String key, Set<String> values) {
        mEdit.putStringSet(key, values);
        apply();
    }

    @Override
    public void remove(String key) {
        mEdit.remove(key);
        apply();
    }

    /**
     * 将修改数据原子提交到内存, 而后同步真正提交到硬件磁盘
     */
    @Override
    public boolean commit() {
        return mEdit.commit();
    }


    /**
     * 将修改数据原子提交到内存, 而后异步真正提交到硬件磁盘
     */
    @Override
    public void apply() {
        mEdit.apply();
    }

    /**
     * @param <T> Serializable/Parcelable
     */
    @Override
    public <T> T getObject(String key, Class<T> clazz) {
        String text = getString(key, null);
        if (text != null) {
            return JSON.parseObject(text, clazz);
        }
        return null;
    }

    @Override
    public void putObject(String key, Serializable object) {
        if (object == null) {
            remove(key);
        } else {
            putString(key, JSON.toJSONString(object));
        }
        apply();
    }

    @Override
    public void putObject(String key, Parcelable object) {
        if (object == null) {
            remove(key);
        } else {
            putString(key, JSON.toJSONString(object));
        }
        apply();
    }

    @Override
    public <T> List<T> getSerializableSet(String key, Class<T> clazz) {
        String text = getString(key, null);
        if (text != null) {
            return JSON.parseArray(text, clazz);
        }
        return null;
    }

    @Override
    public void putSerializableSet(String key, List object) {
        if (object == null) {
            remove(key);
        } else {
            putString(key, JSON.toJSONString(object));
        }
        apply();
    }
}
