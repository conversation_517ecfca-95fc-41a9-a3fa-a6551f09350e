package co.runner.app.running.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.os.Build;
import androidx.core.app.NotificationCompat;
import android.widget.RemoteViews;

import co.runner.app.record.R;
import co.runner.app.record.utils.LogUtils;
import co.runner.app.record.utils.NumberUtils;
import co.runner.app.record.utils.RxJavaPluginUtils;

/**
 * 通知
 * Created by Wiki on 15/9/4.
 */
public class RunningNotification {
    private static final String NOTIFICATION_TAG = "Record";

    private NotificationManager mNotificationManager;
    private Notification mNotification;

    public RunningNotification(Context context) {
        if (mNotificationManager == null) {
            mNotificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        }
    }


	public void notify2(Service context, int meter, int second) {
        final Resources res = context.getResources();

        final Bitmap picture = BitmapFactory.decodeResource(res, R.drawable.ic_notfication_head);


        final String title = res.getString(R.string.app_name);
        final String text = "正在运行";
        Intent intent = new Intent();
        intent.setClassName(context.getPackageName(),"co.runner.app.ui.record.StartRunActivity");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        PendingIntent contentIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(context)
                .setContentText(text)
                .setOngoing(true)
                .setContentTitle(title)
                .setPriority(Notification.PRIORITY_MAX)
                .setWhen(System.currentTimeMillis() - second * 1000)
                .setLargeIcon(picture)
                .setContentIntent(contentIntent).setUsesChronometer(true);

        // 5.0
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            notificationBuilder.setSmallIcon(R.drawable.ic_data_app);
            notificationBuilder.setColor(Color.parseColor("#ED5465"));
        } else {
            notificationBuilder.setSmallIcon(R.drawable.ic_data_app);
        }

        Notification notification = notificationBuilder.build();
        notification.flags = Notification.FLAG_ONGOING_EVENT | Notification.FLAG_NO_CLEAR;
        notify(context, notification);
    }

    private void notify(final Service context, final Notification notification) {
        mNotificationManager.notify(NOTIFICATION_TAG.hashCode(), notification);
        //开启服务进程守护
        context.startForeground(NOTIFICATION_TAG.hashCode(), notification);
    }



    public void cancel(final Service context) {
        context.stopForeground(true);
        mNotificationManager.cancel(NOTIFICATION_TAG.hashCode());
        LogUtils.e("关闭通知");
    }



    public static String second2Time(int tempTime) {
        int tempMHout = tempTime / 3600;
        int tempMMinute = tempTime % 3600 / 60;
        int tempMSecond = tempTime % 60;
        StringBuilder timeBuilder = new StringBuilder();
        timeBuilder.append(tempMHout > 9 ? tempMHout : "0" + tempMHout);
        timeBuilder.append(":");
        timeBuilder.append(tempMMinute > 9 ? tempMMinute : "0" + tempMMinute);
        timeBuilder.append(":");
        timeBuilder.append(tempMSecond > 9 ? tempMSecond : "0" + tempMSecond);

        return timeBuilder.toString();
    }

    public boolean isAndroidQAlert(){
        if (Build.VERSION.SDK_INT < 29){
            return false;
        }
        NotificationChannel channel = mNotificationManager.getNotificationChannel("joyrun_running2");
        if (channel == null){
            return false;
        }
        return channel.getImportance()>=NotificationManager.IMPORTANCE_HIGH;
    }

    public static String getMeterStr(int meter) {
        return NumberUtils.keep2DecimalMeter(meter / 1000f);
    }

    public void notify(Service context, int meter, int second, double speed) {
        if (mNotification == null) {

            String channelId = "joyrun_running2";
            String channelName = "骑行中数据展示";

            Intent intent = new Intent();
            intent.setClassName(context.getPackageName(), "co.runner.app.running.activity.RunningDataActivity");
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            PendingIntent contentIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                NotificationChannel mChannel = new NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_LOW);
                mNotificationManager.createNotificationChannel(mChannel);
                mNotification = new NotificationCompat.Builder(context, channelId)
                        .setLargeIcon(BitmapFactory.decodeResource(context.getResources(), R.drawable.ic_notfication_head))
                        .setSmallIcon(R.drawable.ic_data_app)
                        .setAutoCancel(false)
                        .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
                        .setGroup("group1")
                        .setCustomContentView(new RemoteViews(context.getPackageName(), R.layout.layout_notify_info))
                        .setContentIntent(contentIntent)
                        .build();
            } else {
                NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(context, channelId)
                        .setLargeIcon(BitmapFactory.decodeResource(context.getResources(), R.drawable.ic_notfication_head))
                        .setSmallIcon(R.drawable.ic_data_app)
                        .setOngoing(true)
                        .setAutoCancel(false)
                        .setCustomContentView(new RemoteViews(context.getPackageName(), R.layout.layout_notify_info))
                        .setContentIntent(contentIntent);
                mNotification = notificationBuilder.build();
            }
        }

        try {
            // 时间
            mNotification.contentView.setTextViewText(R.id.textView1, second2Time(second));
            // 里程
            mNotification.contentView.setTextViewText(R.id.textView2, getMeterStr(meter));
            // 速度
            if(speed==-1){
                speed=0.00;
            }
            mNotification.contentView.setTextViewText(R.id.textView3, String.format("%.2f", speed));
        } catch (Exception e) {
            RxJavaPluginUtils.handleException(e);
        }
        try {
            notify(context, mNotification);
        } catch (Exception e) {
            RxJavaPluginUtils.handleException(e);
            ServiceUtils.setNotSupportStartForegroundService(context);
        }
    }

}
