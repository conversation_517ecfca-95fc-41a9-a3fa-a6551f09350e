//package co.runner.app.view;
//
//import org.json.JSONObject;
//
//import co.runner.app.domain.RunRecord;
//
///**
// * Created by keve<PERSON>-lian<PERSON> on 2018/4/13.
// */
//
//public interface RunRecordView {
//    /**
//     * 请求骑行数据成功
//     * @param runRecord
//     */
//    void onRunRecordLoaded(RunRecord runRecord, JSONObject dataJson);
//
//    /**
//     * 请求骑行数据失败
//     * @param e
//     */
//    void onLoadRunRecordFailed(Throwable e);
//}
