package co.runner.app.record.utils;

import android.content.Context;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Environment;
import android.text.TextUtils;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;


/**
 * 文件相关操作：读写、拷贝、文件大小 等
 */
public class FileUtils {

    private static final String TAG = FileUtils.class.getSimpleName();
    protected static String sBasePath;

    /**
     * 获取或创建Cache目录
     */
    private static String getMyCacheDir(String bucket) {

        File file = new File(sBasePath, bucket);
        if (!file.exists()) {
            file.mkdirs();
        }
        return file.getAbsolutePath() + "/";
    }

    public static String getBasePath() {
        return sBasePath;
    }

    public static String getImageDir() {
        return getMyCacheDir("悦跑圈");
    }

    public static String getTempDir() {
        return getMyCacheDir("cache/temp");
    }
    public static void deleteTempDir(){
        File file = new File(getTempDir());
        if (file.exists()){
            FileUtils.delAllFile(file.getAbsolutePath());
        }
    }

    public static String getCache(String path) {
        return getMyCacheDir("cache/" + path);
    }

    public static String getVideoDir() {
        return getMyCacheDir("video");
    }

    public static String getWatermarkDir() {
        return getMyCacheDir("watermark");
    }

    public static String getWarmupDir() {
        String warmupDir = getMyCacheDir("warmup");
        File nomedia =  new File(warmupDir,".nomedia");
        if(!nomedia.exists()){
            try {
                nomedia.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return warmupDir;
    }

    public static String getBottomTabCacheDir(){
        return getMyCacheDir("bottom_tab/cache");
    }

    public static String getBottomTabTempDir(){
        return getMyCacheDir("bottom_tab/temp");
    }

    public static String getTrimVideoDir(){
        String trim_videoDir = getMyCacheDir("trim_video");
        File nomedia =  new File(trim_videoDir,".nomedia");
        if(!nomedia.exists()){
            try {
                nomedia.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return trim_videoDir;
    }

    public static boolean isSDCardExist() {
        return Environment.getExternalStorageState()
                          .equals(Environment.MEDIA_MOUNTED);
    }




    /**
     * 获取文件夹大小
     */
    public static long getDirSize(File f) throws Exception {
        long size = 0;
        File[] flist = f.listFiles();
        if (flist != null)
            for (int i = 0; i < flist.length; i++) {
                if (flist[i].isDirectory()) {
                    size = size + getDirSize(flist[i]);
                } else {
                    size = size + flist[i].length();
                }
            }
        return size;
    }



    /**
     * 删除 文件夹 以及 目录下所有文件
     */
    public static void deleteFile(File file) {
        try {
            if (file.exists()) {
                if (file.isFile()) {

                    final File to = new File(file.getAbsolutePath() + System.currentTimeMillis());
                    file.renameTo(to);
                    to.delete();

                } else if (file.isDirectory()) {
                    File files[] = file.listFiles();
                    if (files != null) {
                        for (int i = 0; i < files.length; i++) {
                            deleteFile(files[i]);// 递归
                        }
                    }
                }
                // file.delete();
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    /**
     * 删除指定文件夹下所有文件
     *
     * @param path 文件夹完整绝对路径
     * @return
     */
    public static boolean delAllFile(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return false;
        }
        if (!file.isDirectory()) {
            return false;
        }

        boolean flag = false;
        String[] tempList = file.list();

        if (tempList != null) {
            for (int i = 0; i < tempList.length; i++) {
                File temp;

                if (path.endsWith(File.separator)) {
                    temp = new File(path + tempList[i]);
                } else {
                    temp = new File(path + File.separator + tempList[i]);
                }
                if (temp.isFile()) {
                    temp.delete();
                }
                if (temp.isDirectory()) {
                    delAllFile(path + "/" + tempList[i]);// 先删除文件夹里面的文件
                    delFolder(path + "/" + tempList[i]);// 再删除空文件夹
                    flag = true;
                }
            }
        }
        return flag;
    }

    /**
     * 删除文件夹
     *
     * @param folderPath 文件夹完整绝对路径
     */
    public static void delFolder(String folderPath) {
        try {
            delAllFile(folderPath); // 删除完里面所有内容
            String filePath = folderPath;
            filePath = filePath.toString();
            File myFilePath = new File(filePath);
            myFilePath.delete(); // 删除空文件夹
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }


    /**
     * 从文件尾部追加内容
     *
     * @param content  文本内容
     * @param filename 文件名
     * @return
     */
    public static boolean logDevInfo(String content, String filename) {
        File file = new File(sBasePath, filename);

        boolean success = false;
        try {
            FileOutputStream fos = new FileOutputStream(file, true);
            BufferedOutputStream bout = new BufferedOutputStream(fos);
            bout.write(content.getBytes());
            bout.flush();

            success = true;
            IOUtil.closeIO(bout);
            IOUtil.closeIO(fos);
        } catch (IOException e) {
            LogUtils.e(e);
        }

        return success;
    }

    public static String read(File file) {
        String result = "";
        if (!file.exists()) {
            return null;
        }
        try {
            BufferedReader br = new BufferedReader(new FileReader(file));//构造一个BufferedReader类来读取文件
            String s = "";
            while ((s = br.readLine()) != null) {//使用readLine方法，一次读一行
                result = result + s + "\n";
            }
            IOUtil.closeIO(br);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String readString(String path) {
        return read(new File(path));
    }

    public static void writeString(String filePath, String content) {
        BufferedWriter output = null;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                file.createNewFile();// 不存在则创建
            }
            output = new BufferedWriter(new FileWriter(file));
            output.write(content);
            output.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtil.closeIO(output);
        }
    }

    public static void setBasePath(String baseDir) {
        sBasePath = baseDir;
    }

    public static boolean copy(File sourceFile, File targetFile) {
        try {
            int bytesum = 0;
            int byteread = 0;
            if (sourceFile.exists()) {
                InputStream inStream = new FileInputStream(sourceFile);
                FileOutputStream fs = new FileOutputStream(targetFile);
                byte[] buffer = new byte[1444];
                int length;
                while ((byteread = inStream.read(buffer)) != -1) {
                    bytesum += byteread;
                    System.out.println(bytesum);
                    fs.write(buffer, 0, byteread);
                }
                inStream.close();
            }
            return true;
        } catch (Exception e) {
            System.out.println("error  ");
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 复制整个文件夹内容
     * @param oldPath String 原文件路径 如：c:/fqf
     * @param newPath String 复制后路径 如：f:/fqf/ff
     * @return boolean
     */
    public static boolean copyFolder(String oldPath, String newPath) {

        boolean isSuccess = false;
        try {
            (new File(newPath)).mkdirs(); //如果文件夹不存在 则建立新文件夹
            File a=new File(oldPath);
            String[] file=a.list();
            File temp=null;
            for (int i = 0; i < file.length; i++) {
                if(oldPath.endsWith(File.separator)){
                    temp=new File(oldPath+file[i]);
                }
                else{
                    temp=new File(oldPath+File.separator+file[i]);
                }

                if(temp.isFile()){
                    FileInputStream input = new FileInputStream(temp);
                    FileOutputStream output = new FileOutputStream(newPath + "/" +
                            (temp.getName()).toString());
                    byte[] b = new byte[1024 * 5];
                    int len;
                    while ( (len = input.read(b)) != -1) {
                        output.write(b, 0, len);
                    }
                    output.flush();
                    output.close();
                    input.close();
                }
                if(temp.isDirectory()){//如果是子文件夹
                    copyFolder(oldPath+"/"+file[i],newPath+"/"+file[i]);
                }
            }
            isSuccess = true;
        }
        catch (Exception e) {
            System.out.println("复制整个文件夹内容操作出错");
            e.printStackTrace();

        }finally {
            return isSuccess;
        }

    }

    public interface CopyFileListener {
        void exception(String msg);

        void success(String path);
    }
    public static boolean checkFileExist(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return false;
        }
        return new File(filePath).exists();
    }

    /**
     * 获取音频文件的时间长度
     */
    public static int getFilePlayTime(Context context, int resid) {
        try {
            MediaPlayer mediaPlayer = MediaPlayer.create(context, resid);
            //使用Date格式化播放时间mediaPlayer.getDuration()
            int duration = mediaPlayer.getDuration();
            mediaPlayer.release();
            return duration;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 获取时间长度
     */
    public static int getFilePlayTime(Context context, File file) {
        try {
            MediaPlayer mediaPlayer = MediaPlayer.create(context, Uri.parse(file.toString()));
            //使用Date格式化播放时间mediaPlayer.getDuration()
            int duration = mediaPlayer.getDuration();

            mediaPlayer.release();

            return duration;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
    /**
     * 确保目录存在,没有则创建
     */
    public static boolean confirmFolderExist(String folderPath) {
        File file = new File(folderPath);
        if (!file.exists()) {
            return file.mkdirs();
        }
        return false;
    }
    /**
     * 读取assest文件
     *
     * @param context
     * @param fileName
     * @return
     */
    public static String readAssest(Context context, String fileName) {
        return new AssestsReader(context).read(fileName);
    }


    public static BufferedOutputStream getBufferedOutputStreamFromFile(String fileUrl) {
        BufferedOutputStream bufferedOutputStream = null;
        try {
            File file = new File(fileUrl);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();

            bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(file));
        } catch (Exception e) {
            LogUtils.e("GetBufferedOutputStreamFromFile异常", e);
        }

        return bufferedOutputStream;
    }

    public static void renameFile(String oldPath, String newPath) {
        if (!TextUtils.isEmpty(oldPath) && !TextUtils.isEmpty(newPath)) {
            File newFile = new File(newPath);

            if (newFile.exists()) {
                newFile.delete();
            }

            File oldFile = new File(oldPath);

            if (oldFile.exists()) {
                try {
                    oldFile.renameTo(new File(newPath));
                } catch (Exception e) {
                    LogUtils.e("删除本地文件失败", e);
                }
            }
        }
    }
}
